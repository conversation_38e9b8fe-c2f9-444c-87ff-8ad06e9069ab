package ma.almobadara.backend.service.migration;

import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.service.AddServicesDTO;
import ma.almobadara.backend.dto.service.ServicesDTO;
import ma.almobadara.backend.exceptions.FunctionalException;
import ma.almobadara.backend.repository.services.ServicesRepository;
import ma.almobadara.backend.service.services.ServicesService;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;

import static ma.almobadara.backend.service.migration.util.UtilityClass.getCellValue;

@Service
@RequiredArgsConstructor
@Slf4j
public class ServiceMigration {
private final ServicesService servicesService;
private final ServicesRepository servicesRepository;
    @Transactional(rollbackOn=Exception.class)
    public void migrationService(MultipartFile file) throws IOException {
        try(Workbook workbook=new XSSFWorkbook(file.getInputStream())){
            Sheet sheet=workbook.getSheetAt(0);
            for (Row row:sheet){
                if(row.getRowNum()==0)continue;
                if (row.getCell(0) == null || row.getCell(0).getCellType() == CellType.BLANK) break;


                String serviceId=getCellValue(row,0);
                String categoryId=getCellValue(row,1);
                if(servicesRepository.existsByServiceCategoryIdAndServiceCategoryTypeId(Long.parseLong(serviceId),Long.parseLong(categoryId)))continue;

                String collectionType=getCellValue(row,2);
                String distrubutionType=getCellValue(row,3);
                String dedicatedToEps=getCellValue(row,4);
                String isActif=getCellValue(row,5);
                String priority=getCellValue(row,6);
                String positionSystem=getCellValue(row,7);
                String costs=getCellValue(row,8);
                String montant=getCellValue(row,9);
                AddServicesDTO servicesDTO=
                        AddServicesDTO.builder()
                                .serviceCategoryId(Long.valueOf(serviceId))
                                .serviceCategoryTypeId(Long.valueOf(categoryId))
                                .costs(BigDecimal.valueOf(Long.parseLong(costs)))
                                .priority(Boolean.valueOf(priority))
                                .propositionSystem(Boolean.valueOf(positionSystem))
                                .statutIsActif(Boolean.valueOf(isActif))
                                .isDedicatedToEps(Boolean.valueOf(dedicatedToEps))
                                .collectionType(collectionType)
                                .distributionType(distrubutionType)
                                .amountPerBeneficiary(Long.valueOf(montant))
                                .build();
                servicesService.addService(servicesDTO);
            }
        } catch (FunctionalException e) {
            throw new RuntimeException(e);
        }
    }
}
