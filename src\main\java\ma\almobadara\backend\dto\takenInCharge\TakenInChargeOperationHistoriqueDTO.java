package ma.almobadara.backend.dto.takenInCharge;

import lombok.*;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class TakenInChargeOperationHistoriqueDTO {
    private Long id;
    private String action;
    private String userName;
    private Long operationId;
    private Double managementFees;
    private LocalDateTime createdAt;
    private String comment;
    private Double amount;
}
