package ma.almobadara.backend.repository.takenInCharge;

import ma.almobadara.backend.model.takenInCharge.TakenInChargeYearCount;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface TakenInChargeYearCountRepository extends JpaRepository<TakenInChargeYearCount, Long> {
    Optional<TakenInChargeYearCount> findByYear(String Year);

}
