package ma.almobadara.backend.controller.donation;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.config.HasAccessToModule;
import ma.almobadara.backend.dto.donation.DonationDTO;
import ma.almobadara.backend.dto.donation.DonationHistoryDTO;
import ma.almobadara.backend.dto.donation.DonationProductNatureDTO;
import ma.almobadara.backend.dto.exportentities.ExportFileDTO;
import ma.almobadara.backend.dto.mobile.MobileDonationDto;
import ma.almobadara.backend.enumeration.Functionality;
import ma.almobadara.backend.enumeration.Module;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.service.Donation.DonationService;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.*;

import static ma.almobadara.backend.service.administration.CacheAdUserService.logUserInfo;

@RefreshScope
@RestController
@AllArgsConstructor
@Slf4j
@RequestMapping("/donations")
public class DonationController {

    private final DonationService donationService;
    @PostMapping(value = "/create", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    @Operation(summary = "Create a Donation", description = "add a new Donation", tags = {"donation"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = DonationDTO.class))))})
    public ResponseEntity<DonationDTO> createDonation(@ModelAttribute DonationDTO donationDTO) {
        logUserInfo("createDonation", donationDTO.getType());

        HttpStatus status;
        DonationDTO created = new DonationDTO();
        try {
            created = donationService.addDonation(donationDTO);
            status = HttpStatus.OK;
            log.info("End resource createDonation with id: {} type: {}, OK", donationDTO.getId(), donationDTO.getType());
        } catch (Exception e) {
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            log.error("End resource createDonation with id: {} type: {}, KO: {}", donationDTO.getId(), donationDTO.getType(), e.getMessage());
        }

        return new ResponseEntity<>(created, new HttpHeaders(), status);
    }

    @GetMapping(value = "/csv", produces = {"application/json"})
    public ResponseEntity<ExportFileDTO> exportAllDonationsToCsv(
            @RequestParam(required = false) String searchByDonationType,
            @RequestParam(required = false) String searchByDonorType,
            @RequestParam(required = false) String searchByNom,
            @RequestParam(required = false) String lastNameAr,
            @RequestParam(required = false) String searchByPrenom,
            @RequestParam(required = false) Double minAmount,
            @RequestParam(required = false) Double maxAmount,
            @RequestParam(required = false) Double canalDonationId,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date minDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date maxDate
    ) {

        logUserInfo("exportAllDonationsToCsv");

        ExportFileDTO exportFileDTO;
        HttpStatus status;
        try {
            exportFileDTO = donationService.exportFileWithName(
                    searchByDonationType, searchByDonorType, searchByNom, lastNameAr, searchByPrenom,
                    minAmount, maxAmount, canalDonationId, minDate, maxDate);
            status = HttpStatus.OK;
            log.info("End resource exportAllDonationsToCsv, OK");
        } catch (Exception e) {
            exportFileDTO = null;
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            log.error("End resource exportAllDonationsToCsv, KO: {}", e.getMessage());
        }

        return new ResponseEntity<>(exportFileDTO, new HttpHeaders(), status);
    }

    @GetMapping(value = "/findAll", produces = {"application/json"})
    @Operation(summary = "Find All Donations", description = "Find All Donations", tags = {"donation"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = DonationDTO.class))))})
    public ResponseEntity<Page<DonationDTO>> findAllDonations(
            @RequestParam(defaultValue = "0") final Integer page,
            @RequestParam(defaultValue = "10") final Integer size,
            @RequestParam(required = false) final String searchByDonationType,
            @RequestParam(required = false) final String searchByDonorType,
            @RequestParam(required = false) final String searchByNom,
            @RequestParam(required = false) final String lastNameAr,
            @RequestParam(required = false) final String searchByPrenom,
            @RequestParam(required = false) final Double minAmount,
            @RequestParam(required = false) final Double maxAmount,
            @RequestParam(required = false) final Double canalDonationId,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") final Date minDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") final Date maxDate,
            @RequestParam(required = false) final Long searchByTagId
    ) {

        logUserInfo("findAllDonations");
        HttpStatus status;
        Page<DonationDTO> donationsDTOS;
        try {
            donationsDTOS = donationService.getAllDonations(page, size, searchByDonationType, searchByDonorType, searchByNom, searchByPrenom, lastNameAr, minAmount, maxAmount,canalDonationId, minDate, maxDate,searchByTagId);
            status = HttpStatus.OK;
            log.info("End resource findAllDonations with size: {}, OK", donationsDTOS.getTotalElements());
        } catch (Exception e) {
            donationsDTOS = new PageImpl<>(Collections.emptyList());
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            log.error("End resource findAllDonations, KO: {}", e.getMessage());
        }

        return new ResponseEntity<>(donationsDTOS, status);
    }

    @DeleteMapping(value = "delete/{idDonation}", headers = "Accept=application/json")
    @Operation(summary = "Delete donation donor", description = "delete donation donor", tags = {"donation"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = DonationDTO.class))))})
    public ResponseEntity<String> deleteDonation(@PathVariable Long idDonation) {
        logUserInfo("deleteDonation", String.valueOf(idDonation));
        try {
            donationService.deleteDonation(idDonation);
            log.info("End resource deleteDonation By Id {}, OK", idDonation);
            return new ResponseEntity<>(HttpStatus.NO_CONTENT);
        } catch (TechnicalException e) {
            log.error("End resource deleteDonation By Id {}, KO: {}", idDonation, e.getMessage());
            return new ResponseEntity<>(e.getMessage(), HttpStatus.BAD_REQUEST); // Return the error message
        } catch (Exception e) {
            log.error("End resource deleteDonation By Id {}, KO: {}", idDonation, e.getMessage());
            return new ResponseEntity<>("An unexpected error occurred.", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }


    @Operation(summary = "Find donation by ID", description = "Returns a single donation", tags = {"donation"})
    //@HasAccessToModule(modules = {Module.DONATION}, functionalities = {Functionality.VIEW})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(schema = @Schema(implementation = DonationDTO.class))),
            @ApiResponse(responseCode = "404", description = "Donation not found")})
    @GetMapping(value = "/{idDonation}", produces = {"application/json"})
    public ResponseEntity<DonationDTO> getDonationByID(@PathVariable Long idDonation) {

        logUserInfo("getDonationByID", String.valueOf(idDonation));

        DonationDTO donationDTO;
        HttpStatus status;
        try {
            donationDTO = donationService.getDonationById(idDonation);
            status = HttpStatus.OK;
            log.info("End resource getDonationByID  : {}, OK", idDonation);
        } catch (Exception e) {
            donationDTO = null;
            status = HttpStatus.NOT_FOUND;
            log.error("End resource getDonationByID  : {}, KO: {}", idDonation, e.getMessage());
        }

        return new ResponseEntity<>(donationDTO, new HttpHeaders(), status);
    }


    @PostMapping(value = "/donationProductNatures", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
   // @HasAccessToModule(modules = {Module.DONATION}, functionalities = {Functionality.UPDATE})
    @Operation(summary = "Add Product to Donation", description = "Add a new product to the donation", tags = {"donation"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = @Content(schema = @Schema(implementation = DonationDTO.class))),})
    public ResponseEntity<DonationDTO> addProductToNatureDonation(@ModelAttribute DonationProductNatureDTO productNatureDTO) {

        logUserInfo("addProductToNatureDonation", String.valueOf(productNatureDTO));

        HttpStatus status;
        DonationDTO donationDTO = new DonationDTO();
        try {
            donationDTO = donationService.addProductToNatureDonation(productNatureDTO);
            status = HttpStatus.OK;
            log.info("End resource addProductToNatureDonation with productID: {} and donationID: {}, OK", productNatureDTO.getId(), productNatureDTO.getDonation().getId());
        } catch (Exception e) {
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            log.error("End resource addProductToNatureDonation with productID: {} and donationID: {}, KO: {}", productNatureDTO.getId(), productNatureDTO.getDonation().getId(), e.getMessage());
        }

        return new ResponseEntity<>(donationDTO, status);
    }

    @DeleteMapping(value = "/donationProductNatures/{productId}")
    @HasAccessToModule(modules = {Module.DONATION}, functionalities = {Functionality.UPDATE})
    @Operation(summary = "Delete Product from Donation", description = "Delete a product from the donation", tags = {"donation"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Product deleted successfully"),})
    public ResponseEntity<Void> deleteProductFromDonation(@PathVariable Long productId) {

        logUserInfo("deleteProductFromNatureDonation", String.valueOf(productId));

        HttpStatus status;
        try {
            donationService.deleteProductFromNatureDonation(productId);
            status = HttpStatus.OK;
            log.info("End resource deleteProductFromNatureDonation for product id: {}, OK", productId);
        } catch (Exception e) {
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            log.error("End resource deleteProductFromNatureDonation for product id: {}, KO: {}", productId, e.getMessage());
        }

        return new ResponseEntity<>(status);
    }

    @GetMapping(value = "/history/{donationId}", produces = {"application/json"})
    @HasAccessToModule(modules = {Module.DONATION}, functionalities = {Functionality.VIEW})
    @Operation(summary = "Find Donation History by Donation ID", description = "Retrieve the history of donations based on the given donation ID", tags = {"donation"})
    @ApiResponses(value = {
            @ApiResponse(
                    responseCode = "200",
                    description = "Successful operation",
                    content = @Content(array = @ArraySchema(schema = @Schema(implementation = DonationHistoryDTO.class)))
            ),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<List<DonationHistoryDTO>> getDonationHistoryByDonationId(@PathVariable Long donationId) {
        logUserInfo("getDonationHistoryByDonationId", String.valueOf(donationId));

        try {
            List<DonationHistoryDTO> donationHistoryDTOS = donationService.getDonationHistory(donationId);
            log.info("End resource getDonationHistoryByDonationId with size: {}, OK", donationHistoryDTOS.size());
            return ResponseEntity.ok(donationHistoryDTOS);
        } catch (Exception e) {
            log.error("End resource getDonationHistoryByDonationId, KO: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Collections.emptyList());
        }
    }

    @GetMapping(value = "/findAll/by-assistant/{assistantId}", produces = {"application/json"})
    @Operation(summary = "Find All Donations by Assistant Zone", description = "Find All Donations in the zone of the specified assistant", tags = {"donation"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = MobileDonationDto.class))))})
    public ResponseEntity<Page<MobileDonationDto>> findAllDonationsByAssistant(
            @PathVariable Long assistantId,
            @RequestParam(defaultValue = "0") final Integer page,
            @RequestParam(defaultValue = "10") final Integer size,
            @RequestParam(required = false) final String searchByDonationType,
            @RequestParam(required = false) final String searchByDonorType,
            @RequestParam(required = false) final String searchByNom,
            @RequestParam(required = false) final String lastNameAr,
            @RequestParam(required = false) final String searchByPrenom,
            @RequestParam(required = false) final Double minAmount,
            @RequestParam(required = false) final Double maxAmount,
            @RequestParam(required = false) final Double canalDonationId,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") final Date minDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") final Date maxDate,
            @RequestParam(required = false) final Long searchByTagId
    ) {

        logUserInfo("findAllDonationsByAssistant", String.valueOf(assistantId));
        HttpStatus status;
        Page<MobileDonationDto> donationsDTOS;
        try {
            donationsDTOS = donationService.getAllDonationsByAssistantZoneForMobile(assistantId, page, size, searchByDonationType, searchByDonorType, searchByNom, searchByPrenom, lastNameAr, minAmount, maxAmount, canalDonationId, minDate, maxDate, searchByTagId);
            status = HttpStatus.OK;
            log.info("End resource findAllDonationsByAssistant with assistantId: {} and size: {}, OK", assistantId, donationsDTOS.getTotalElements());
        } catch (Exception e) {
            donationsDTOS = new PageImpl<>(Collections.emptyList());
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            log.error("End resource findAllDonationsByAssistant with assistantId: {}, KO: {}", assistantId, e.getMessage());
        }

        return new ResponseEntity<>(donationsDTOS, status);
    }



}
