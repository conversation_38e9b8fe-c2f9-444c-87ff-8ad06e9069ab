package ma.almobadara.backend.service.migration;

import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.administration.AssistantDTO;
import ma.almobadara.backend.dto.administration.EpsDto;
import ma.almobadara.backend.dto.administration.ZoneDTO;
import ma.almobadara.backend.dto.donor.DonorDTO;
import ma.almobadara.backend.dto.referentiel.CanalCommunicationDTO;
import ma.almobadara.backend.dto.referentiel.CanalDonationDTO;
import ma.almobadara.backend.dto.referentiel.CityDTO;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.model.administration.Tag;
import ma.almobadara.backend.model.administration.Taggable;
import ma.almobadara.backend.model.donor.Donor;
import ma.almobadara.backend.repository.administration.TagRepository;
import ma.almobadara.backend.repository.administration.TaggableRepository;
import ma.almobadara.backend.repository.donor.DonorRepository;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;

import static ma.almobadara.backend.service.migration.util.UtilityClass.getCellValue;

import ma.almobadara.backend.service.donor.DonorService;
import ma.almobadara.backend.clients.RefFeignClient;
import ma.almobadara.backend.dto.donor.DonorPhysicalDTO;
import ma.almobadara.backend.dto.donor.DonorMoralDTO;
import ma.almobadara.backend.dto.donor.DonorContactDTO;
import ma.almobadara.backend.dto.referentiel.DonorStatusDTO;

@Service
@RequiredArgsConstructor
@Slf4j
public class DonateurMigration {
    private final DonorRepository donorRepository;
    private final DonorService donorService;
    private final RefFeignClient refFeignClient;
    private final TagRepository tagRepository;
    private final TaggableRepository taggableRepository;

    @Transactional(rollbackOn = Exception.class)
    public void migrateDonateur(MultipartFile file) throws TechnicalException, IOException {
        log.info("Request to migrateDonateur : {}", file);
        Map<String, Integer> locationMap = new HashMap<>();
        locationMap.put("agadir", 76418);
        locationMap.put("canada", 17302);
        locationMap.put("casa", 76451);
        locationMap.put("Espagne", 114204);
        locationMap.put("France", 30493);
        locationMap.put("Kenitra", 76518);
        locationMap.put("Kénitra", 76518);
        locationMap.put("Kuit", 65590);
        locationMap.put("laayoun", 76587);
        locationMap.put("mohamadia", 76455);
        locationMap.put("rabat", 76523);
        locationMap.put("salé", 76524);
        locationMap.put("saudi", 107597);
        locationMap.put("sidi kacem", 76526);
        locationMap.put("sidi slimane", 76528);
        locationMap.put("temara", 76534);
        try (Workbook workbook = new XSSFWorkbook(file.getInputStream())) {
            Sheet sheet = workbook.getSheetAt(0);
            for (Row row : sheet) {
                if (row.getRowNum() == 0) continue;
                if (row.getCell(0) == null || row.getCell(0).getCellType() == CellType.BLANK) break;
                String id=getCellValue(row,0);
                if(id==null) continue;
                String code = getCellValue(row, 1);
                String name = getCellValue(row, 2);
                String type = getCellValue(row, 3);
                String nameAr=getCellValue(row,4);
                String phone=getCellValue(row,5);
                String city=getCellValue(row,6);
                String canal=getCellValue(row,7);
                String status=getCellValue(row,8);
                String email=getCellValue(row,9);
                DonorStatusDTO actifStatus = refFeignClient.getParDonorStatus(Objects.equals(status, "inactif") ?2L:1L);
                List<CanalCommunicationDTO> canalCommunicationDTOs=(canal!=null && !canal.equals(""))?List.of(CanalCommunicationDTO.builder().id(Long.parseLong(canal)).build()):List.of();
                if (donorRepository.findByCodeComptabilite(code).isPresent()) continue;

                if ("P".equalsIgnoreCase(type)) {
                    // Physique
                    String[] nameParts = name.trim().split("\\s+");
                    String firstName = nameParts[nameParts.length - 1];
                    String lastName = String.join(" ", Arrays.copyOf(nameParts, nameParts.length - 1));
                    String firstNameAr;
                    String lastNameAr;
                    if (nameAr == null || nameAr.trim().isEmpty()) {
                        firstNameAr = "الاسم";
                        lastNameAr = "النسب";
                    } else {
                        String[] nameArParts = nameAr.trim().split("\\s+");
                        if (nameArParts.length == 1) {
                            firstNameAr = nameArParts[0];
                            lastNameAr = nameArParts[0];
                        } else {
                            firstNameAr = nameArParts[0];
                            // Join all words except the first one for lastNameAr
                            lastNameAr = String.join(" ", Arrays.copyOfRange(nameArParts, 1, nameArParts.length));
                        }
                    }
                    DonorPhysicalDTO dto = DonorPhysicalDTO.builder()
                            .firstName(firstName)
                            .lastName(lastName)
                            .balance(0D)
                            .firstNameAr(firstNameAr)
                            .codeComptabilite(code)
                            .city(CityDTO.builder().id(Long.valueOf(locationMap.getOrDefault(city,76451))).build())
                            .lastNameAr(lastNameAr)
                            .sex("Homme")
                            .address("Inconnu")
                            .addressAr("غير معروف")
                            .comment(city)
                            .email(email)
                            .phoneNumber(phone)
                            .firstDonationYear("2025-01-01")
                            .canalCommunications(canalCommunicationDTOs)
                            .status(actifStatus)
                            .type("Physique")
                            .build();
                    DonorDTO donorDTO=donorService.addDonorPhysique(dto);
                    Optional<Tag> migTag=tagRepository.findByNameAndType("migration","metier");
                    Taggable taggable=Taggable.builder()
                            .tag(migTag.get()).taggableType("donor").taggableId(donorDTO.getId()).build();
                    taggableRepository.save(taggable);
                    Optional<Tag> aCompleteTag=tagRepository.findByNameAndType("à_compléter","metier");
                    Taggable taggable2=Taggable.builder()
                            .tag(aCompleteTag.get()).taggableType("donor").taggableId(donorDTO.getId()).build();
                    taggableRepository.save(taggable2);
                } else if ("S".equalsIgnoreCase(type)) {
                    // Moral
                    String firstNameAr;
                    String lastNameAr;
                    if (nameAr == null || nameAr.trim().isEmpty()) {
                        firstNameAr = "الاسم";
                        lastNameAr = "النسب";
                    } else {
                        String[] nameArParts = nameAr.trim().split("\\s+");
                        if (nameArParts.length == 1) {
                            firstNameAr = nameArParts[0];
                            lastNameAr = nameArParts[0];
                        } else {
                            firstNameAr = nameArParts[0];
                            // Join all words except the first one for lastNameAr
                            lastNameAr = String.join(" ", Arrays.copyOfRange(nameArParts, 1, nameArParts.length));
                        }
                    }
                    DonorContactDTO contact = DonorContactDTO.builder()
                            .firstName(name)
                            .lastName(name)
                            .email(email)
                            .phoneNumber(phone)
                            .canalCommunications(canalCommunicationDTOs)
                            .firstNameAr(firstNameAr)
                            .lastNameAr(lastNameAr)

                            .sex("Homme")
                            .build();
                    DonorMoralDTO dto = DonorMoralDTO.builder()
                            .company(name)
                            .balance(0D)
                            .status(actifStatus)
                            .codeComptabilite(code)
                            .comment(city)
                            .firstDonationYear("2025-01-01")
                            .city(CityDTO.builder().id(Long.valueOf(locationMap.getOrDefault(city,76451))).build())
                            .type("Moral")
                            .address("Inconnu")
                            .addressAr("غير معروف")
                            .donorContacts(List.of(contact))
                            .build();
                    DonorDTO donorDTO=donorService.addDonorMoral(dto);
                    Optional<Tag> migTag=tagRepository.findByNameAndType("migration","metier");
                    Taggable taggable=Taggable.builder()
                            .tag(migTag.get()).taggableType("donor").taggableId(donorDTO.getId()).build();
                    taggableRepository.save(taggable);
                    Optional<Tag> aCompleteTag=tagRepository.findByNameAndType("à_compléter","metier");
                    Taggable taggable2=Taggable.builder()
                            .tag(aCompleteTag.get()).taggableType("donor").taggableId(donorDTO.getId()).build();
                    taggableRepository.save(taggable2);
                }
            }
        }
    }

    public void migrateFirstYearOfDonation(MultipartFile file) throws IOException {
        try (Workbook workbook = new XSSFWorkbook(file.getInputStream())) {
            Sheet sheet = workbook.getSheetAt(0);
            for (Row row : sheet) {
                if (row.getRowNum() == 0) continue;
                if (row.getCell(0) == null || row.getCell(0).getCellType() == CellType.BLANK) break;
                String code = getCellValue(row, 1);
                Optional<Donor> donor=donorRepository.findByCodeComptabilite(code);
                if(donor.isEmpty()){
                    continue;
                }
                Donor donor1=donor.get();
                String firstYearOfDonation=getCellValue(row,10);
                donor1.setFirstDonationYear(firstYearOfDonation);
                donorRepository.save(donor1);
            }}
    }
}
