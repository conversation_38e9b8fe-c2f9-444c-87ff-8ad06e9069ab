package ma.almobadara.backend.dto.beneficiary;

import lombok.*;
import ma.almobadara.backend.model.beneficiary.BeneficiaryStatut;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BeneficiaryForAideComplementaireDTO {
    private Long id;
    private String code;
    private String firstName;
    private String lastName;
    private String sex;
    private String phoneNumber;
    private String identityCode;
    private Boolean independent;
    private String type;
    private BeneficiaryStatut beneficiaryStatut;
}
