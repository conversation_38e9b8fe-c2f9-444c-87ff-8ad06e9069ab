package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.*;
import ma.almobadara.backend.dto.beneficiary.*;
import ma.almobadara.backend.dto.donation.DonationDTO;
import ma.almobadara.backend.dto.donor.DonorDTO;
import ma.almobadara.backend.dto.family.*;
import ma.almobadara.backend.dto.takenInCharge.TakenInChargeBeneficiaryDTO;
import ma.almobadara.backend.dto.takenInCharge.TakenInChargeDTO;
import ma.almobadara.backend.dto.takenInCharge.TakenInChargeDonorDTO;
import ma.almobadara.backend.dto.takenInCharge.TakenInChargeOperationDTO;
import ma.almobadara.backend.model.beneficiary.*;
import ma.almobadara.backend.model.donation.Donation;
import ma.almobadara.backend.model.donor.Donor;
import ma.almobadara.backend.model.takenInCharge.TakenInChargeDonor;
import ma.almobadara.backend.model.family.*;
import ma.almobadara.backend.model.takenInCharge.TakenInCharge;
import ma.almobadara.backend.model.takenInCharge.TakenInChargeBeneficiary;
import ma.almobadara.backend.model.takenInCharge.TakenInChargeOperation;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface FamilyMemberMapper {


	@Mapping(source = "familyRelationshipId", target = "familyRelationship.id")
	FamilyMemberDTO familyMemberToFamilyMemberDTO(FamilyMember familyMember);

	Iterable<FamilyMemberDTO> familyMemberToFamilyMemberDTO(Iterable<FamilyMember> familyMembers);

	@Mapping(source = "familyRelationship.id", target = "familyRelationshipId")
	FamilyMember familyMemberDTOToFamilyMember(FamilyMemberDTO familyMemberDTO);

	Iterable<FamilyMember> familyMemberDTOToFamilyMember(Iterable<FamilyMemberDTO> familyMemberDTOS);

	@Mapping(target = "familyMembers", ignore = true)
	FamilyDTO familyToFamilyDTO(Family family);

	@Mapping(target = "familyMembers", ignore = true)
	Family familyDTOToFamily(FamilyDTO familyDTO);

	@Mapping(target = "person", ignore = true)
	@Mapping(target = "epsResidents", ignore = true)
	@Mapping(target = "diseaseTreatments", ignore = true)
	@Mapping(target = "allergies", ignore = true)
	@Mapping(target = "diseases", ignore = true)
	@Mapping(target = "notes", ignore = true)
	@Mapping(target = "documents", ignore = true)
	@Mapping(target = "educations", ignore = true)
	@Mapping(target = "scholarshipBeneficiaries", ignore = true)
	BeneficiaryDTO beneficiaryToBeneficiaryDTO(Beneficiary beneficiary);

	@Mapping(target = "beneficiary", ignore = true)
	@Mapping(source = "document.id", target = "document.id")
    DocumentBeneficiary beneficiaryDocumentDTOToBeneficiaryDocument(DocumentBeneficiaryDTO documentDTO);

	@Mapping(target = "beneficiary", ignore = true)
	@Mapping(source = "city.id", target = "cityId")
	@Mapping(source = "schoolYear.id", target = "schoolYearId")
	@Mapping(source = "schoolLevel.id", target = "schoolLevelId")
	@Mapping(source = "honor.id", target = "honorId")
	@Mapping(source = "major.id", target = "majorId")
	Education educationDTOToEducation(EducationDTO educationDTO);

	@Mapping(target = "beneficiary", ignore = true)
	@Mapping(source = "serviceId", target = "service.id")
	@Mapping(source = "statusId", target = "status.id")
	BeneficiaryServiceDTO beneficiaryServiceToBeneficiaryServiceDTO(BeneficiaryService beneficiaryService);

	@Mapping(target = "beneficiary", ignore = true)
	@Mapping(source = "service.id", target = "serviceId")
	@Mapping(source = "status.id", target = "statusId")
	BeneficiaryService beneficiaryServiceDTOToBeneficiaryService(BeneficiaryServiceDTO beneficiaryServiceDTO);

	@Mapping(target = "beneficiary", ignore = true)
	@Mapping(source = "eps.id", target = "epsId")
	EpsResident epsResidentDTOToEpsResident(EpsResidentDTO epsResidentDTO);

	@Mapping(target = "beneficiary", ignore = true)
	@Mapping(source = "type.id", target = "typeId")
	DiseaseTreatment diseaseTreatmentDTOToDiseaseTreatment(DiseaseTreatmentDTO diseaseTreatmentDTO);


	@Mapping(target = "beneficiary", ignore = true)
	@Mapping(source = "scholarship.id", target = "scholarshipId")
	ScholarshipBeneficiary scholarshipBeneficiaryDTOToScholarshipBeneficiary(ScholarshipBeneficiaryDTO scholarshipBeneficiaryDTO);

	@Mapping(target = "familyMember", ignore = true)
	@Mapping(source = "typeIdentityId", target = "typeIdentity.id")
	@Mapping(source = "cityId", target = "city.id")
	@Mapping(source = "schoolLevelId", target = "schoolLevel.id")
	@Mapping(source = "professionId", target = "profession.id")
    PersonDTO personToPersonDTO(Person person);

	@Mapping(target = "familyMember", ignore = true)
	@Mapping(source = "city.id", target = "cityId")
	@Mapping(source = "typeIdentity.id", target = "typeIdentityId")
	@Mapping(source = "schoolLevel.id", target = "schoolLevelId")
	@Mapping(source = "profession.id", target = "professionId")
	Person personDTOToPerson(PersonDTO personDTO);

	@Mapping(target = "person", ignore = true)
	@Mapping(source = "cardTypeId", target = "cardType.id")
	BankCardDTO bankCardToBankCardDTO(BankCard bankCard);

	@Mapping(target = "person", ignore = true)
	@Mapping(source = "cardType.id", target = "cardTypeId")
	BankCard bankCardDTOToBankCard(BankCardDTO bankCardDTO);

	@Mapping(target = "beneficiary", ignore = true)
	TakenInChargeBeneficiary takenInChargeBeneficiaryToTakenInChargeBeneficiaryDTO(TakenInChargeBeneficiaryDTO takenInChargeBeneficiaryDTO);

	@Mapping(target = "beneficiary", ignore = true)
	TakenInChargeBeneficiaryDTO takenInChargeBeneficiaryDTOToTakenInChargeBeneficiary(TakenInChargeBeneficiary takenInChargeBeneficiary);

	@Mapping(target = "takenInCharge", ignore = true)
	TakenInChargeDonor takenInChargeDonorTotTakenInChargeDonorDTO(TakenInChargeDonorDTO takenInChargeDonorDTO);

	@Mapping(target = "takenInCharge", ignore = true)
	TakenInChargeDonorDTO takenInChargeDonorDTOToTakenInChargeDonor(TakenInChargeDonor takenInChargeDonor);

	@Mapping(target = "takenInChargeDonor", ignore = true)
	TakenInChargeOperation takenInChargeOperationDTOToTakenInChargeOperation(TakenInChargeOperationDTO takenInChargeOperationDTO);

	@Mapping(target = "takenInChargeDonor", ignore = true)
	TakenInChargeOperationDTO takenInChargeOperationToTakenInChargeOperationDTO(TakenInChargeOperation takenInChargeOperation);

	@Mapping(target = "takenInChargeDonors", ignore = true)
	Donor donorToDonorDTO(DonorDTO donorDTO);

	@Mapping(target = "donor", ignore = true)
	@Mapping(target = "documentDonations", ignore = true)
	@Mapping(target = "donationProductNatures", ignore = true)
	Donation donationDTOToDonation(DonationDTO donationDTO);

	@Mapping(target = "donor", ignore = true)
	@Mapping(target = "documentDonations", ignore = true)
	@Mapping(target = "donationProductNatures", ignore = true)
	DonationDTO donationToDonationDTO(Donation donation);


	@Mapping(target = "takenInChargeBeneficiaries", ignore = true)
	TakenInChargeDTO takenInChargeToTakenInChargeDTO(TakenInCharge takenInCharge);


	@Mapping(target = "takenInChargeBeneficiaries", ignore = true)
	TakenInCharge takenInChargeDTOToTakenInCharge(TakenInChargeDTO takenInChargeDTO);

	NoteFamilyDTO familyNoteToFamilyNoteDTO(NoteFamily familyNote);


	DocumentFamilyDTO familyDocumentToFamilyDocumentDTO(DocumentFamily familyDocument);

	@Mapping(target = "family", ignore = true)
	DocumentFamily familyDocumentDTOToFamilyDocument(DocumentFamilyDTO familyDocumentDTO);

	DocumentFamilyMemberDTO familyMemberDocumentToFamilyMemberDocumentDTO(DocumentFamilyMember familyMemberDocument);

	@Mapping(target = "familyMember", ignore = true)
	DocumentFamilyMember familyMemberDocumentDTOToFamilyMemberDocument(DocumentFamilyMemberDTO familyMemberDocumentDTO);

	@Mapping(target = "familyMember", ignore = true)
	@Mapping(source = "incomeSourceId", target = "incomeSource.id")
	ExternalIncomeDTO externalIncomeToExternalIncomeDTO(ExternalIncome externalIncome);

	@Mapping(target = "familyMember", ignore = true)
	@Mapping(source = "incomeSource.id", target = "incomeSourceId")
	ExternalIncome externalIncomeDTOToExternalIncome(ExternalIncomeDTO externalIncomeDTO);

	Person mapFamilyMemberAddDTOToPerson(FamilyMemberAddDTO familyMemberAddDTO);

	@Mapping(source = "id", target = "personId")
	@Mapping(target = "id", ignore = true)
	@Mapping(target = "familyId", ignore = true)
	@Mapping(target = "code", ignore = true)
	@Mapping(target = "tutor", ignore = true)
	@Mapping(target = "tutorStartDate", ignore = true)
	@Mapping(target = "tutorEndDate", ignore = true)
	@Mapping(target = "familyRelationshipId", ignore = true)
	FamilyMemberAddDTO PersonTomapFamilyMemberAddDTO(Person person);

	FamilyMember mapFamilyMemberAddDTOToFamilyMember(FamilyMemberAddDTO familyMemberAddDTO);

	@Mapping(source = "code", target = "Code")
	@Mapping(source = "tutor", target = "Tuteur")
	@Mapping(source = "tutorStartDate", target = "TuteurDateDebut")
	@Mapping(source = "tutorEndDate", target = "TuteurDateFin")
	@Mapping(source = "firstName", target = "Prenom")
	@Mapping(source = "lastName", target = "Nom")
	@Mapping(source = "firstNameAr", target = "PrenomArabe")
	@Mapping(source = "lastNameAr", target = "NomArabe")
	@Mapping(source = "birthDate", target = "DateDeNaissance")
	@Mapping(source = "phoneNumber", target = "Telephone")
	@Mapping(source = "identityCode", target = "NumIdentite")
	@Mapping(source = "address", target = "Adresse")
	@Mapping(source = "addressAr", target = "AdresseArabe")
	@Mapping(source = "deceased", target = "Deceder")
	@Mapping(source = "deathDate", target = "DateDuDeces")
	@Mapping(source = "deathReason", target = "RaisonDuDeces")
	FamilyMemberAuditDTO FamilyMemberAddDtoToFamilyMemberAuditDto(FamilyMemberAddDTO familyMemberAddDTO);


}
