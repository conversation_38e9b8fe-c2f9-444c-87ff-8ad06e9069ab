package ma.almobadara.backend.dto.mobile;

import lombok.*;

import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class MobileDonationDto {
    private Long id;
    private LocalDateTime timeCreated;
    private Double montant;
    private String direction;  // "Entrée" or "Sortie"
    private String service;
    private String type;
    private String channel;
}
