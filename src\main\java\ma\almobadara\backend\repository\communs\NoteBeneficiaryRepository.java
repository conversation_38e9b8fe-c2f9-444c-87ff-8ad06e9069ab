package ma.almobadara.backend.repository.communs;

import ma.almobadara.backend.model.beneficiary.Beneficiary;
import ma.almobadara.backend.model.beneficiary.NoteBeneficiary;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface NoteBeneficiaryRepository extends JpaRepository<NoteBeneficiary, Long> {

    Iterable<NoteBeneficiary> findByBeneficiary(Beneficiary beneficiary);

    List<NoteBeneficiary> findByNoteId(Long id);

    List<NoteBeneficiary> findByBeneficiaryId(Long beneficiaryId);
}
