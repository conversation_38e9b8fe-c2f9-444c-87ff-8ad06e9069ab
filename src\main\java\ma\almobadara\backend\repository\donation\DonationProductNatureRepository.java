package ma.almobadara.backend.repository.donation;

import ma.almobadara.backend.model.donation.Donation;
import ma.almobadara.backend.model.donation.DonationProductNature;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface DonationProductNatureRepository extends JpaRepository<DonationProductNature, Long> {
    Iterable<DonationProductNature> findByDonation(Donation donation);

    // findByDonationIdAndProductId
    Optional<DonationProductNature> findByDonationIdAndProductNatureId(Long donationId, Long productNatureId);

}
