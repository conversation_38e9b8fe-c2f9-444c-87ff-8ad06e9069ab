package ma.almobadara.backend.model.beneficiary;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Objects;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class DocumentBeneficiaryId implements Serializable {

    private Long beneficiary;
    private Long document;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DocumentBeneficiaryId that = (DocumentBeneficiaryId) o;
        return Objects.equals(beneficiary, that.beneficiary) && Objects.equals(document, that.document);
    }

    @Override
    public int hashCode() {
        return Objects.hash(beneficiary, document);
    }

}