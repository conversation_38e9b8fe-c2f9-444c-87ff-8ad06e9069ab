package ma.almobadara.backend.model.donor;

import jakarta.persistence.*;
import lombok.*;
import ma.almobadara.backend.model.communs.Document;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@IdClass(DocumentDonorId.class)
public class DocumentDonor {

    @Id
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "donor_id")
    @Fetch(FetchMode.JOIN)
    private Donor donor;

    @Id
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "document_id")
    private Document document;

}
