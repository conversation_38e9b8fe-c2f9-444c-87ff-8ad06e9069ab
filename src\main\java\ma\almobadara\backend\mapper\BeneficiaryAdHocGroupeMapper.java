package ma.almobadara.backend.mapper;


import ma.almobadara.backend.dto.aideComplemenatire.BeneficiaryAideComplemenatireDTO;
import ma.almobadara.backend.dto.beneficiary.BeneficiaryAdHocGroupDto;

import ma.almobadara.backend.dto.beneficiary.BeneficiaryForAideComplementaireDTO;
import ma.almobadara.backend.model.beneficiary.Beneficiary;
import ma.almobadara.backend.model.beneficiary.BeneficiaryAdHocGroup;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;


@Mapper(componentModel = "spring")
public interface BeneficiaryAdHocGroupeMapper {

	@Mappings({
			@Mapping(source = "id", target = "id"),
			@Mapping(source = "code", target = "code"),
			@Mapping(source = "name", target = "name"),
			@Mapping(source = "cityId", target = "cityId"),
			@Mapping(source = "fullNameContact", target = "fullNameContact"),
			@Mapping(source = "phoneNumber", target = "phoneNumber"),
			@Mapping(source = "comment", target = "comment"),
			@Mapping(source = "status", target = "status"),

	})
	BeneficiaryAdHocGroupDto toDTO(BeneficiaryAdHocGroup beneficiaryAdHocGroup);


	@Mappings({
			@Mapping(source = "id", target = "id"),
			@Mapping(source = "code", target = "code"),
			@Mapping(source = "name", target = "name"),
			@Mapping(source = "cityId", target = "cityId"),
			@Mapping(source = "fullNameContact", target = "fullNameContact"),
			@Mapping(source = "phoneNumber", target = "phoneNumber"),
			@Mapping(source = "comment", target = "comment"),
			@Mapping(source = "status", target = "status"),
			@Mapping(target = "typePriseEnChargeIdsList", ignore = true)
	})
	BeneficiaryAdHocGroup toEntity(BeneficiaryAdHocGroupDto beneficiaryAdHocGroupDto);

	@Mappings({
			@Mapping(source = "id", target = "id"),
			@Mapping(source = "code", target = "code"),
			@Mapping(source = "name", target = "name"),
			@Mapping(source = "cityId", target = "cityId"),
			@Mapping(source = "fullNameContact", target = "fullNameContact"),
			@Mapping(source = "phoneNumber", target = "phoneNumber"),
			@Mapping(source = "comment", target = "comment"),
			@Mapping(source = "status", target = "status"),
			@Mapping(target = "typePriseEnChargeIdsList", ignore = true)
	})
	void updateBeneficiaryAdHocGroupFromDTO(BeneficiaryAdHocGroupDto beneficiaryAdHocGroupDto, @MappingTarget BeneficiaryAdHocGroup beneficiaryAdHocGroup);

	@Mapping(source = "name", target = "firstName")
	BeneficiaryForAideComplementaireDTO toBeneficiaryAdHocForAideComplementaireDTO(BeneficiaryAdHocGroup beneficiaryAdHocGroup);


	@Mapping(source = "name", target = "firstName")
	@Mapping(source = "phoneNumber", target = "phoneNumber")
	@Mapping(source = "beneficiaryStatut", target = "beneficiaryStatut")
	@Mapping(source = "beneficiaryAdHocGroup.beneficiaryStatut.nameStatut", target = "statut")
	BeneficiaryAideComplemenatireDTO beneficiaryAdHocGroupetoBeneficiaryAdHocGroupeAideComplemenatireDTO(BeneficiaryAdHocGroup beneficiaryAdHocGroup);

	}