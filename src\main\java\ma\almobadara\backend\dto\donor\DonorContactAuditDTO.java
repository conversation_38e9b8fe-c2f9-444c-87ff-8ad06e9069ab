package ma.almobadara.backend.dto.donor;


import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Transient;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import ma.almobadara.backend.dto.referentiel.DonorContactFunctionDTO;
import ma.almobadara.backend.model.donor.DonorContactCanalCommunication;
import ma.almobadara.backend.model.donor.DonorContactLanguageCommunication;
import ma.almobadara.backend.model.donor.DonorMoral;
import ma.almobadara.backend.model.donor.NoteDonorContact;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@SuperBuilder
@AllArgsConstructor
public class DonorContactAuditDTO {
    private String contactPourEntreprise;
    private String prenom;
    private String nom;
    private String prenomArabe;
    private String nomArabe;
    private String sexe;
    private String email;
    private String telephone;
    private Boolean contactPrincipal;
    private String fonction;
    private Boolean isPasswordChanged;
    private List<String> canalCommunication;
    private List<String> languageCommunication;

}
