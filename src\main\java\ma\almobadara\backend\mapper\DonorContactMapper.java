package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.referentiel.CanalCommunicationDTO;
import ma.almobadara.backend.dto.donor.DonorContactDTO;
import ma.almobadara.backend.dto.referentiel.LanguageCommunicationDTO;
import ma.almobadara.backend.model.donor.DonorContact;
import ma.almobadara.backend.model.donor.DonorContactCanalCommunication;
import ma.almobadara.backend.model.donor.DonorContactLanguageCommunication;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.List;

@Mapper(componentModel = "spring")
public interface DonorContactMapper {

	@Mapping(source = "donorContactFunctionId", target = "donorContactFunction.id")
	@Mapping(source = "donorContactCanalCommunications", target = "canalCommunications")
	DonorContactDTO donorContactModelToDto(DonorContact donorContact);

	Iterable<DonorContactDTO> donorContactListModelToDto(Iterable<DonorContact> donorContacts);

	@Mapping(source = "donorContactFunction.id", target = "donorContactFunctionId")
	@Mapping(source = "languageCommunications", target = "donorContactLanguageCommunications")
	@Mapping(source = "canalCommunications", target = "donorContactCanalCommunications")
	DonorContact donorContactDtoToModel(DonorContactDTO donorContact);

	Iterable<DonorContact> donorContactListDtoToModal(Iterable<DonorContactDTO> donorContacts);

	@Named("mapWithoutNesting")
	@Mapping(target = "canalCommunications", ignore = true)
	@Mapping(target = "languageCommunications", ignore = true)
	DonorContactDTO donorPhysicalModelToDtoForList(DonorContact donor);


	@Mapping(target = "id", ignore = true)
	@Mapping(source = "id", target = "languageCommunicationId")
	DonorContactLanguageCommunication languageCommunicationDTOToDonorContactLanguageCommunication(LanguageCommunicationDTO languageCommunicationDTO);

	List<DonorContactLanguageCommunication> languageCommunicationDTOToDonorContactLanguageCommunication(List<LanguageCommunicationDTO> languageCommunicationDTO);

	@Mapping(source = "languageCommunicationId", target = "id")
	LanguageCommunicationDTO donorContactLanguageCommunicationToLanguageCommunicationDTO(DonorContactLanguageCommunication donorContactLanguageCommunication);

	List<LanguageCommunicationDTO> donorContactLanguageCommunicationToLanguageCommunicationDTO(List<DonorContactLanguageCommunication> donorContactLanguageCommunications);

	@Mapping(target = "id",ignore = true)
	@Mapping(source = "id", target = "canalCommunicationId")
	DonorContactCanalCommunication canalCommunicationDTOToDonorCanalCommunication(CanalCommunicationDTO canalCommunicationDTO);

	List<DonorContactCanalCommunication> canalCommunicationDTOToDonorCanalCommunication(List<CanalCommunicationDTO> canalCommunicationDTOS);

	@Mapping(source = "canalCommunicationId", target = "id")
	CanalCommunicationDTO donorContactCanalCommunicationToCanalCommunicationDTO(DonorContactCanalCommunication donorContactCanalCommunication);

	List<CanalCommunicationDTO> donorContactCanalCommunicationToCanalCommunicationDTO(List<DonorContactCanalCommunication> donorContactCanalCommunications);

}
