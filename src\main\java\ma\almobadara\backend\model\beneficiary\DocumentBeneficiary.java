package ma.almobadara.backend.model.beneficiary;


import jakarta.persistence.*;
import lombok.*;
import ma.almobadara.backend.model.communs.Document;


@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@IdClass(DocumentBeneficiaryId.class)
public class DocumentBeneficiary{

    @Id
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "document_id")
    private Document document;

    @Id
    @ManyToOne
    @JoinColumn(name = "beneficiary_id")
    private Beneficiary beneficiary;

}
