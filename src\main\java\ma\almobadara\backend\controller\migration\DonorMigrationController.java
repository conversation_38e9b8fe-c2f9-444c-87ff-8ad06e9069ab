package ma.almobadara.backend.controller.migration;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.exceptions.FunctionalException;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.service.migration.BeneficiaryFamilyMigrationService;
import ma.almobadara.backend.service.migration.DonateurMigration;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

@RestController
@RequestMapping("/donor-migration")
@Slf4j
@RequiredArgsConstructor
public class DonorMigrationController {

    private final DonateurMigration donateurMigration;

    @PostMapping("/upload")
    public ResponseEntity<String> migrateDonorData(@RequestParam("file") MultipartFile file) throws FunctionalException {
        log.info("Received request to migrate donor data");
        try {
            donateurMigration.migrateDonateur(file);
            return ResponseEntity.ok("Donor data migration completed successfully.");
        } catch (IOException e) {
            log.error("Error during donor migration: {}", e.getMessage());
            return ResponseEntity.internalServerError().body("Error during donor migration: " + e.getMessage());
        } catch (TechnicalException e) {
            return ResponseEntity.internalServerError().body("Error during donor migration: " + e.getMessage());
        }
    }

    @PostMapping("/update-first-year-of-donation")
    public ResponseEntity<String> migrateFirstYearOfDonation(@RequestParam("file") MultipartFile file) throws FunctionalException {
        log.info("Received request to migrate donor data");
        try {
            donateurMigration.migrateFirstYearOfDonation(file);
            return ResponseEntity.ok("Donor First Year Of Donation data migration completed successfully.");
        } catch (IOException e) {
            log.error("Error during First Year Of Donation migration: {}", e.getMessage());
            return ResponseEntity.internalServerError().body("Error during First Year Of Donation migration: " + e.getMessage());
        }
    }
}
