package ma.almobadara.backend.dto.donation;


import lombok.*;


import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class BudgetLineForAideComplementaireDTO {
    private Long id;
    private String code;
    private Double amount;
    private Double amountByBeneficiary;
    private String comment;
    private LocalDateTime createdAt;
    private String status;
    private String fullNameDonor;
    private String typeDonor;
    private String codeDonor;
    private Long idDonation;
    private Long idDonor;
//    private Services service;
    private Double montantReserve;
    private LocalDateTime executionDate;
}
