package ma.almobadara.backend.service.migration;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.mapper.DonorMapper;
import ma.almobadara.backend.model.donation.Donation;
import ma.almobadara.backend.service.Donation.DonationService;
import ma.almobadara.backend.service.donor.DonorService;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import ma.almobadara.backend.repository.donor.DonorRepository;
import ma.almobadara.backend.repository.donation.DonationRepository;
import ma.almobadara.backend.repository.services.ServicesRepository;
import ma.almobadara.backend.repository.administration.TagRepository;
import ma.almobadara.backend.model.donor.Donor;
import ma.almobadara.backend.model.service.Services;
import ma.almobadara.backend.dto.donation.DonationDTO;
import ma.almobadara.backend.dto.donation.BudgetLineDTO;
import ma.almobadara.backend.dto.donor.DonorDTO;
import ma.almobadara.backend.dto.administration.TagDTO;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import java.util.*;
import java.time.LocalDate;
import java.time.ZoneId;
import static ma.almobadara.backend.service.migration.util.UtilityClass.getCellValue;

@Service
@Slf4j
@RequiredArgsConstructor
public class DonationMigrationService {

    private final DonorRepository donorRepository;
    private final DonationRepository donationRepository;
    private final DonationService donationService;
    private final ServicesRepository servicesRepository;
    private final DonorMapper donorMapper;
    private final DonorService donorService;
    private final TagRepository tagRepository;

    public void migrateDonations(MultipartFile file) {
        log.info("Received donation migration file: {}", file.getOriginalFilename());
        Map<String, List<Long>> serviceMap = Map.of(
                "ORPHELIN", List.of(1L, 1L),
                "ETUDIANT", List.of(1L, 2L),
                "VEUVE", List.of(1L, 3L),
                "HANDICAPE", List.of(1L, 4L),
                "FAMILLE", List.of(1L, 5L),
                "ENFANT DIFFICULTE", List.of(1L, 6L)
        );
        try (Workbook workbook = new XSSFWorkbook(file.getInputStream())) {
            Sheet sheet = workbook.getSheetAt(0);
            for (Row row : sheet) {
                if (row.getRowNum() == 0) continue; // skip header
                if (row.getCell(0) == null || row.getCell(0).getCellType() == CellType.BLANK) break;

                String donorCode = getCellValue(row, 5);
                String valueStr = getCellValue(row, 7);
                String serviceName = getCellValue(row, 3);
                if (donorCode == null || donorCode.isEmpty() || valueStr == null || valueStr.isEmpty()) {
                    log.warn("Row {} skipped: missing donor code or value", row.getRowNum());
                    continue;
                }
                Optional<Donor> donorOpt = donorRepository.findByCodeComptabilite(donorCode);
                if (donorOpt.isEmpty()) {
                    log.warn("Row {} skipped: donor not found for code {}", row.getRowNum(), donorCode);
                    continue;
                }
                List<Donation> donations=donationRepository.findByDonor(donorOpt.get());
                if(!donations.isEmpty()){
                    log.warn("donateur already have a donation");
                    continue;
                }
                List<Long> serviceId =  serviceMap.getOrDefault(serviceName != null ? serviceName.toUpperCase() : "", null);
                Optional<Services> service = servicesRepository.findByServiceCategoryIdAndServiceCategoryTypeId(serviceId.get(0), serviceId.get(1));
                if (serviceId == null) {
                    log.warn("Row {} skipped: unknown service name {}", row.getRowNum(), serviceName);
                    continue;
                }
                if(!service.isPresent()){
                    log.warn("Row {} skipped: service not found for id {}", row.getRowNum(), serviceId);
                    continue;
                }
                double value = 0;
                try {
                    value = Double.parseDouble(valueStr);
                } catch (NumberFormatException e) {
                    log.warn("Row {}: invalid value '{}', defaulting to 0", row.getRowNum(), valueStr);
                }
                // Date: Jan 1 of last year
                LocalDate jan1LastYear = LocalDate.now().minusYears(1).withDayOfYear(1);
                Date receptionDate = Date.from(jan1LastYear.atStartOfDay(ZoneId.systemDefault()).toInstant());
                // Tags
                List<TagDTO> tags = new ArrayList<>();
                tagRepository.findByNameAndType("migration", "metier").ifPresent(tag -> tags.add(TagDTO.builder().id(tag.getId()).name(tag.getName()).build()));
                tagRepository.findByNameAndType("à_compléter", "metier").ifPresent(tag -> tags.add(TagDTO.builder().id(tag.getId()).name(tag.getName()).build()));
                // Build donation DTO
                Donor donor = donorOpt.get();
                DonorDTO donorDTO = donorService.getDonorById(donor.getId());

                BudgetLineDTO budgetLine = BudgetLineDTO.builder()
                        .amount(value)
                        .type("Kafalat")
                        .makeItAvailable(false)
                        .natureBudgetLine(false)
                        .service(ma.almobadara.backend.dto.service.ServicesDTO.builder().id(service.get().getId()).build())
                        .build();
                DonationDTO donation = DonationDTO.builder()
                        .donor(donorDTO)
                        .value(value)
                        .enableCurrency(false)
                        .transactionNumber("Inconnu")
                        .receptionDate(receptionDate)
                        .identifiedDonor(true)
                        .canalDonation(ma.almobadara.backend.dto.referentiel.CanalDonationDTO.builder().id(2L).build())
                        .tags(tags)
                        .type("Financière")
                        .budgetLines(List.of(budgetLine))
                        .build();
                donation.setNonIdentifiedValue(0D);
                donation.setNonIdentifiedStatus("DISPONIBLE");


                try {
                    // Save donation (assume mapping to entity and saving)
                    donationService.addDonation(donation);
                    log.info("Row {} migrated: donor {} value {} service {}", row.getRowNum(), donorCode, value, serviceName);
                } catch (Exception e) {
                    log.error("Row {} migration failed: {}", row.getRowNum(), e.getMessage());
                }
            }
        } catch (Exception e) {
            log.error("Migration failed: {}", e.getMessage());
        }
    }
} 