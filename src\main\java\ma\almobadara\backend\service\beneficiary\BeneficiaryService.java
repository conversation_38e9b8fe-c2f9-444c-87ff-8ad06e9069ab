package ma.almobadara.backend.service.beneficiary;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.*;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.Audit.AuditApplicationService;
import ma.almobadara.backend.clients.RefFeignClient;
import ma.almobadara.backend.controller.referentiel.RefController;
import ma.almobadara.backend.dto.CityWithRegionAndCountryDTO;
import ma.almobadara.backend.dto.administration.CacheAdUserDTO;
import ma.almobadara.backend.dto.administration.TagDTO;
import ma.almobadara.backend.dto.aideComplemenatire.AideComplementaireBeneficiareDto;
import ma.almobadara.backend.dto.beneficiary.*;
import ma.almobadara.backend.dto.communs.DocumentAndEntityDto;
import ma.almobadara.backend.dto.communs.DocumentDTO;
import ma.almobadara.backend.dto.donor.DonorDTO;
import ma.almobadara.backend.dto.exportentities.BeneficiaryExportDTO;
import ma.almobadara.backend.dto.exportentities.ExportFileDTO;
import ma.almobadara.backend.dto.family.FamilyMemberDTO;
import ma.almobadara.backend.dto.getbeneficiary.GetListDTO;
import ma.almobadara.backend.dto.getbeneficiary.GetServiceDTO;
import ma.almobadara.backend.dto.referentiel.*;
import ma.almobadara.backend.dto.request.ServiceAndStatus;
import ma.almobadara.backend.dto.service.ServicesDTO;
import ma.almobadara.backend.dto.takenInCharge.GetBeneficiariesForTakeInchargeDTO;
import ma.almobadara.backend.dto.takenInCharge.TakenInChargeBeneficiaryDTO;
import ma.almobadara.backend.dto.takenInCharge.TakenInChargeDonorDTO;
import ma.almobadara.backend.enumeration.BeneficiaryStatus;
import ma.almobadara.backend.enumeration.EntitiesToExport.BeneficiaryExportHeaders;
import ma.almobadara.backend.enumeration.RoleCode;
import ma.almobadara.backend.exceptions.FunctionalException;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.mapper.*;
import ma.almobadara.backend.model.administration.*;
import ma.almobadara.backend.model.aideComplemenatire.AideComplementaireDonorBeneficiary;
import ma.almobadara.backend.model.beneficiary.*;
import ma.almobadara.backend.model.family.DocumentFamily;
import ma.almobadara.backend.model.family.Family;
import ma.almobadara.backend.model.family.FamilyMember;
import ma.almobadara.backend.model.service.Services;
import ma.almobadara.backend.model.takenInCharge.TakenInCharge;
import ma.almobadara.backend.repository.administration.AssistantRepository;
import ma.almobadara.backend.repository.administration.CacheAdUserRepository;
import ma.almobadara.backend.repository.administration.TaggableRepository;
import ma.almobadara.backend.repository.administration.ZoneRepository;
import ma.almobadara.backend.repository.aideComplemenatire.AideComplementaireDonorBeneficiaryRepository;
import ma.almobadara.backend.repository.beneficiary.*;
import ma.almobadara.backend.repository.communs.DocumentDonorRepository;
import ma.almobadara.backend.repository.communs.DocumentRepository;
import ma.almobadara.backend.repository.family.FamilyDocumentRepository;
import ma.almobadara.backend.repository.family.FamilyMemberRepository;
import ma.almobadara.backend.repository.family.FamilyRepository;
import ma.almobadara.backend.repository.services.ServicesRepository;
import ma.almobadara.backend.repository.takenInCharge.TakenInChargeBeneficiaryRepository;
import ma.almobadara.backend.repository.takenInCharge.TakenInChargeRepository;
import ma.almobadara.backend.service.administration.TokenImpersonationService;
import jakarta.servlet.http.HttpServletRequest;
import ma.almobadara.backend.service.communs.DocumentService;
import ma.almobadara.backend.service.communs.ExportService;
import ma.almobadara.backend.service.communs.MailSenderService;
import ma.almobadara.backend.service.donor.DonorService;
import ma.almobadara.backend.service.donor.MinioService;
import ma.almobadara.backend.util.times.TimeWatch;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.apache.commons.io.FilenameUtils;
import org.hibernate.Hibernate;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import static ma.almobadara.backend.service.administration.CacheAdUserService.getRoleFromJwt;
import static ma.almobadara.backend.service.administration.CacheAdUserService.getUsernameFromJwt;
import static ma.almobadara.backend.util.constants.GlobalConstants.*;


@RequiredArgsConstructor
@Slf4j
@Service
public class BeneficiaryService {
    @Autowired
    private FamilyRepository familyRepository;
    @Autowired
    private TakenInChargeRepository takenInChargeRepository;
    @Autowired
    private TakenInChargeBeneficiaryRepository takenInChargeBeneficiaryRepository;
    @Autowired
    private ServicesRepository servicesRepository;
    private final AssistantRepository assistantRepository;
    private final HistoryBeneficiaryRepository historyBeneficiaryRepository;
    private final TokenImpersonationService tokenImpersonationService;
    private final ZoneRepository zoneRepository;
    private final HttpServletRequest request;
   private final AideComplementaireDonorBeneficiaryRepository aideComplementaireDonorBeneficiaryRepository;
    private final BeneficiaryMapper beneficiaryMapper;
    private final EducationMapper educationMapper;
    private final ScholarshipBeneficiaryMapper scholarshipMapper;
    private final PersonRepository personRepository;
    private final FamilyMemberRepository familyMemberRepository;
    private final BeneficiaryRepository beneficiaryRepository;
    private final DocumentBeneficiaryRepository documentBeneficiaryRepository;
    private final FamilyDocumentRepository familyDocumentRepository;
    private final BeneficiaryYearCountRepository beneficiaryYearCountRepository;
    private final MinioService minioService;
    private final EducationRepository educationRepository;
    private final ScholarshipBeneficiaryRepository scholarshipRepository;
    private final EpsResidentRepository epsResidentRepository;
    private final EpsResidentMapper epsResidentMapper;
    private final DiseaseTreatmentRepository diseaseTreatmentRepository;
    private final BeneficiaryHandicapRepository beneficiaryHandicapRepository;
    private final DiseaseTreatmentMapper diseaseTreatmentMapper;
    private final BeneficiaryHandicapMapper beneficiaryHandicapMapper;
    private final BeneficiaryAllergyMapper beneficiaryAllergyMapper;
    private final BeneficiaryDiseasesMapper beneficiaryDiseasesMapper;
    private final BeneficiaryServiceRepository beneficiaryServiceRepository;
    private final BeneficiaryAllergyRepository beneficiaryAllergyRepository;
    private final BeneficiaryDiseaseRepository beneficiaryDiseaseRepository;
    private final GlassesRepository glassesRepository;
    private final RefFeignClient refFeignClient;
    private final DonorService donorService;
    private final RefController refController;
    private final EntityManager entityManager;
    private final AuditApplicationService auditApplicationService;
    private final ExportService exportService;
    private final TaggableRepository taggableRepository;
    private final BeneficiaryStatutRepository beneficiaryStatutRepository;
    private final HistoryBeneficiaryService historyBeneficiaryService;
    private final MailSenderService mailSenderService;
    private final CacheAdUserRepository cacheAdUserRepository;
    private final DocumentMapper documentMapper;
    private final DocumentRepository documentRepository;
    private final DocumentDonorRepository documentDonorRepository;
    private final ServicesMapper servicesMapper;

    @Autowired
    private DocumentService documentService;
    private final ExecutorService executorService = Executors.newCachedThreadPool();

    @Value("${minio.beneficiariesFolder}")
    private String beneficiariesFolder;

    @Value("${minio.profilePicture.folder}")
    private String folderPathPicture;

    @Value("${minio.profilePicture.abv}")
    private String abv;

    @Transactional
    public List<AideComplementaireBeneficiareDto> getAideComplementaireForBeneficiare(Long id){
        List<AideComplementaireDonorBeneficiary> aideComplementaireDonorBeneficiaries=aideComplementaireDonorBeneficiaryRepository.findAideComplementaireDonorBeneficiariesBybeneficiary_id(id);
        List<AideComplementaireBeneficiareDto> beneficiareDtos=aideComplementaireDonorBeneficiaries.stream().filter(aideComplementaireDonorBeneficiary -> {
            if(Objects.equals(aideComplementaireDonorBeneficiary.getAideComplementaire().getStatut(), "executer") ||Objects.equals(aideComplementaireDonorBeneficiary.getAideComplementaire().getStatut(), "cloturer")  ){
                return true;
            }
            return false;
        }).map(aideComplementaireDonorBeneficiary -> {
            AideComplementaireBeneficiareDto aideComplementaireBeneficiareDto=new AideComplementaireBeneficiareDto();
            aideComplementaireBeneficiareDto.setName(aideComplementaireDonorBeneficiary.getAideComplementaire().getName());
            aideComplementaireBeneficiareDto.setAmount(aideComplementaireDonorBeneficiary.getMontantAffecter());
            aideComplementaireBeneficiareDto.setId(aideComplementaireDonorBeneficiary.getAideComplementaire().getId());
            aideComplementaireBeneficiareDto.setExecuionDate(aideComplementaireDonorBeneficiary.getAideComplementaire().getDateExecution());
            aideComplementaireBeneficiareDto.setServices(aideComplementaireDonorBeneficiary.getAideComplementaire().getService());
            aideComplementaireBeneficiareDto.setStatut(aideComplementaireDonorBeneficiary.getAideComplementaire().getStatut());
            return aideComplementaireBeneficiareDto;
        }).toList();
        return beneficiareDtos;
    }

    @Transactional
    public AddedBeneficiaryResponse addBeneficiary(BeneficiaryAddDTO beneficiaryAddDTO) throws TechnicalException {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service Beneficiary {}", beneficiaryAddDTO);
        Person person;
        Beneficiary existingBeneficiaryfordto = new Beneficiary();
        String OldAuditBenefeciary = null;
        if (beneficiaryAddDTO.getOldBeneficiary() != null && beneficiaryAddDTO.getOldBeneficiary()) {
            if (beneficiaryAddDTO.getCodeBeneficiary() != null) {
                Optional<Beneficiary> beneficiaryOptional = beneficiaryRepository.findByCodeBeneficiary(beneficiaryAddDTO.getCodeBeneficiary());

                if (beneficiaryOptional.isPresent()) {
                    Beneficiary existingBeneficiary = beneficiaryOptional.get();

                    // Check if the existing beneficiary is the same as the one being updated (compare IDs or another unique field)
                    if (beneficiaryAddDTO.getId() == null || !beneficiaryAddDTO.getId().equals(existingBeneficiary.getId())) {
                        throw new TechnicalException("This candidate code already exists");
                    }
                }
            }

            if (beneficiaryAddDTO.getAccountingCode() != null) {
                Optional<Beneficiary> beneficiaryOptional = beneficiaryRepository.findByAccountingCode(beneficiaryAddDTO.getAccountingCode());

                if (beneficiaryOptional.isPresent()) {
                    Beneficiary existingBeneficiary = beneficiaryOptional.get();

                    // Check if the existing beneficiary is the same as the one being updated (compare IDs or another unique field)
                    if (beneficiaryAddDTO.getId() == null || !beneficiaryAddDTO.getId().equals(existingBeneficiary.getId())) {
                        throw new TechnicalException("This accounting code already exists");
                    }
                }
            }
        } else {
            beneficiaryAddDTO.setCodeBeneficiary(null);
            beneficiaryAddDTO.setAccountingCode(null);
        }
        if (beneficiaryAddDTO.getId() != null) {
            //for audit
            StringBuilder familleInfomartion = null;
            Optional<Beneficiary> beneficiaryOptional = beneficiaryRepository.findById(beneficiaryAddDTO.getId());
            Beneficiary existingBeneficiary = beneficiaryOptional.orElseThrow(() -> new TechnicalException("Beneficiary not found"));
            String ids = existingBeneficiary.getPerson().getTypePriseEnChargeIdsList();
            List<String> priseEnchargeforAudit = List.of();
            if (ids != null) {
                String[] idsArray = ids.split(",");
                List<Long> idsLong = Arrays.stream(idsArray)
                        .map(Long::parseLong)
                        .toList();
                priseEnchargeforAudit = getPriseEnchargeforAudit(idsLong);
            }


            Map<String, String> properties = prepareBeneficiaryForAudit(existingBeneficiary);
            if (!existingBeneficiary.getIndependent()) {
                Family family = existingBeneficiary.getPerson().getFamilyMember().getFamily();

                familleInfomartion = new StringBuilder(family.getCode());
                for (FamilyMember familyMember : family.getFamilyMembers()) {
                    if (familyMember.isTutor()) {
                        familleInfomartion.append(" - ").append(familyMember.getPerson().getFirstName()).append(" ").append(familyMember.getPerson().getLastName());
                    }
                }
            }

            OldAuditBenefeciary = existingBeneficiary.getAudit(properties, familleInfomartion, priseEnchargeforAudit);


            Hibernate.initialize(existingBeneficiary.getEpsResidents()); // Initialiser la collection
            Hibernate.initialize(existingBeneficiary.getBeneficiaryServices());

            // Create a new instance of Person and copy properties from the existing one
            Person existingPerson = new Person();
            BeanUtils.copyProperties(existingBeneficiary.getPerson(), existingPerson);
            existingBeneficiary.setPerson(existingPerson); // Set the new Person instance to existingFamilyMember


            BeanUtils.copyProperties(existingBeneficiary, existingBeneficiaryfordto);
            existingBeneficiaryfordto.setRqComplete(existingBeneficiary.getRqComplete());

            // Copy collections
            existingBeneficiaryfordto.setEpsResidents(new ArrayList<>());
            for (EpsResident epsResident : existingBeneficiary.getEpsResidents()) {
                EpsResident newEpsResident = new EpsResident();
                BeanUtils.copyProperties(epsResident, newEpsResident);
                existingBeneficiaryfordto.getEpsResidents().add(newEpsResident);
            }

            existingBeneficiaryfordto.setBeneficiaryServices(new ArrayList<>());
            for (ma.almobadara.backend.model.beneficiary.BeneficiaryService beneficiaryService : existingBeneficiary.getBeneficiaryServices()) {
                ma.almobadara.backend.model.beneficiary.BeneficiaryService newBeneficiaryService = new ma.almobadara.backend.model.beneficiary.BeneficiaryService();
                BeanUtils.copyProperties(beneficiaryService, newBeneficiaryService);
                existingBeneficiaryfordto.getBeneficiaryServices().add(newBeneficiaryService);
            }

        }

        if (beneficiaryAddDTO.getId() != null) {
            personRepository.findById(beneficiaryAddDTO.getPersonId()).orElseThrow(() -> new TechnicalException(PERSON_NOT_FOUND));
        }

        person = beneficiaryMapper.mapBeneficiaryAddDTOToPerson(beneficiaryAddDTO);

        if (beneficiaryAddDTO.getTypePriseEnChargeIds() != null && !beneficiaryAddDTO.getTypePriseEnChargeIds().isEmpty()) {
            StringBuilder priseEnChargesString = new StringBuilder();
            int listSize = beneficiaryAddDTO.getTypePriseEnChargeIds().size();
            int count = 0;

            for (Long pr : beneficiaryAddDTO.getTypePriseEnChargeIds()) {
                TypePriseEnChargeDTO typePriseEnChargeDTO = refFeignClient.getParTypePriseEnCharge(pr);
                priseEnChargesString.append(typePriseEnChargeDTO.getId());
                count++;
                if (count < listSize) {
                    priseEnChargesString.append(",");
                }
            }

            person.setTypePriseEnChargeIdsList(priseEnChargesString.toString());
        }
        // we can do the same for the case if it was educated
        if (beneficiaryAddDTO.getPersonId() != null) {
            // we should find this person and see if he is educated or not
            Person person1 = personRepository.findById(beneficiaryAddDTO.getPersonId()).orElseThrow(() -> new TechnicalException(PERSON_NOT_FOUND));
            if (person1.isEducated()) {
                person.setEducated(true);
                if (person1.getSchoolLevelId() != null) {
                    person.setSchoolLevelId(person1.getSchoolLevelId());
                }
                if (person1.getSchoolName() != null) {
                    person.setSchoolName(person1.getSchoolName());
                }
                if (person1.getSchoolNameAr() != null) {
                    person.setSchoolNameAr(person1.getSchoolNameAr());
                }

            }
        }

        var beneficiary = beneficiaryMapper.mapBeneficiaryAddDTOToBeneficiary(beneficiaryAddDTO);

        if (beneficiaryAddDTO.getId() != null && existingBeneficiaryfordto.getRqComplete() != null) {
            beneficiary.setRqComplete(existingBeneficiaryfordto.getRqComplete());
        }

        if (beneficiaryAddDTO.getId() == null) {
            String code = generatePrecandidatCode(beneficiaryAddDTO.getZoneId());
            beneficiary.setCode(code);
            BeneficiaryStatut beneficiaryStatut;
            if (beneficiaryAddDTO.getBeneficiaryStatusId() != null) {
                beneficiaryStatut = new BeneficiaryStatut(beneficiaryAddDTO.getBeneficiaryStatusId());
            } else {
                beneficiaryStatut = new BeneficiaryStatut(BeneficiaryStatus.CANDIDAT_INITIAL.getId());
            }
            beneficiary.setBeneficiaryStatut(beneficiaryStatut);
        }

        if (beneficiaryAddDTO.getPersonId() != null) {
            person.setId(beneficiaryAddDTO.getPersonId());
        }
        if (beneficiaryAddDTO.getZoneId() != null) {
            Zone zone = new Zone();
            zone.setId(beneficiaryAddDTO.getZoneId());
            Optional<Zone> zone1 = zoneRepository.findById(zone.getId());
            beneficiary.setZone(zone1.get());

        }
        if (beneficiaryAddDTO.getSousZoneId() != null) {
            SousZone sousZone = new SousZone();
            sousZone.setId(beneficiaryAddDTO.getSousZoneId());
            beneficiary.setSousZone(sousZone);

        }

        if (beneficiaryAddDTO.getId() != null) {
            Optional<Beneficiary> beneficiaryOptional = beneficiaryRepository.findById(beneficiaryAddDTO.getId());
            beneficiary.setId(beneficiaryOptional.get().getId());
            person.setId(beneficiaryAddDTO.getPersonId());
            beneficiary.setBeneficiaryStatut(existingBeneficiaryfordto.getBeneficiaryStatut());
        }
        StringBuilder familleInformationString = new StringBuilder();
        saveBeneficiaryPicture(beneficiaryAddDTO.getPicture(), person, beneficiary.getCode());

        if (!beneficiaryAddDTO.getIndependent()) {
            FamilyMember familyMember = familyMemberRepository.findByPersonId(beneficiaryAddDTO.getPersonId()).orElseThrow(() -> new TechnicalException(PERSON_NOT_FOUND));
            Family family = familyMember.getFamily();
            familleInformationString.append(family.getCode()).append(" - ");
            for (FamilyMember familyMember1 : family.getFamilyMembers()) {
                if (familyMember1.isTutor()) {
                    familleInformationString.append(familyMember1.getPerson().getFirstName() + " " + familyMember1.getPerson().getLastName());

                }
            }
        }
        person = personRepository.save(person);
        beneficiary.setPerson(person);
        beneficiary.setHasRapport(false);


        beneficiary = beneficiaryRepository.save(beneficiary);
       // taggableRepository.deleteAllByTaggableIdAndTaggableType(beneficiary.getId(), "beneficiary");
        Beneficiary finalBeneficiary = beneficiary;
        taggableRepository.deleteAllByTaggableIdAndTaggableType(finalBeneficiary.getId(),"beneficiary");
        if(beneficiaryAddDTO.getTags()!=null){
            beneficiaryAddDTO.getTags().stream().forEach(tagDTO -> {
                Taggable taggble=new Taggable();
                Tag tag=new Tag();
                tag.setId(tagDTO.getId());
                taggble.setTag(tag);
                taggble.setTaggableId(finalBeneficiary.getId());
                taggble.setTaggableType("beneficiary");
                taggableRepository.save(taggble);
            });
        }

        //saveServices(beneficiary, beneficiaryAddDTO);
        saveEps(beneficiary, beneficiaryAddDTO);

        AddedBeneficiaryResponse response = new AddedBeneficiaryResponse();
        response.setId(beneficiary.getId());
        response.setPersonId(beneficiary.getPerson().getId());
        response.setCode(beneficiary.getCode());
        Map<String, String> properties = prepareBeneficiaryForAudit(beneficiary);
        List<String> priseEnchargeforAudit = getPriseEnchargeforAudit(beneficiaryAddDTO.getTypePriseEnChargeIds());
        String newBeneficiaryAudit = beneficiary.getAudit(properties, familleInformationString, priseEnchargeforAudit);

        if (beneficiaryAddDTO.getId() != null) {
            auditApplicationService.audit("Modification des informations personnelles du bénéficiare : " + beneficiary.getCode(), getUsernameFromJwt(), "Modifier un Beneficiaire",
                    OldAuditBenefeciary, newBeneficiaryAudit, BENEFICIAIRE, UPDATE);
        } else {
            auditApplicationService.audit("Ajout d'un nouveau  Beneficiare : " + beneficiary.getCode(), getUsernameFromJwt(), "Add Family Member",
                    null, newBeneficiaryAudit, BENEFICIAIRE, CREATE);
            historyBeneficiaryService.saveHistoryBeneficiaryStatus(beneficiary.getBeneficiaryStatut().getId(), beneficiary.getId(), getUsernameFromJwt());
        }


        log.debug("End service Add Beneficiary, took {}", watch.toMS());
        return response;
    }

    private Map<String, String> prepareBeneficiaryForAudit(Beneficiary beneficiary) {
        Map<String, String> beneficiaryMap = new LinkedHashMap<>();
        if (beneficiary.getPerson().getTypeIdentityId() != null) {
            TypeIdentityDTO typeIdentityDTO = refFeignClient.getParTypeIdentity(beneficiary.getPerson().getTypeIdentityId());
            beneficiaryMap.put("typeIdentity", typeIdentityDTO.getName());
        } else {
            beneficiaryMap.put("typeIdentity", null);
        }
        if (beneficiary.getPerson().getAccommodationNatureId() != null) {
            AccommodationNatureDTO accommodationNatureDTO = refFeignClient.getParAccommodationNature(beneficiary.getPerson().getAccommodationNatureId());
            beneficiaryMap.put("accommodationNature", accommodationNatureDTO.getName());
        } else {
            beneficiaryMap.put("accommodationNature", null);
        }
        if (beneficiary.getPerson().getAccommodationTypeId() != null) {
            AccommodationTypeDTO accommodationTypeDTO = refFeignClient.getMetAccommodationType(beneficiary.getPerson().getAccommodationTypeId());
            beneficiaryMap.put("accommodationType", accommodationTypeDTO.getName());
        } else {
            beneficiaryMap.put("accommodationType", null);
        }
        if (beneficiary.getPerson().getCategoryBeneficiaryId() != null) {
            CategoryBeneficiaryDTO categoryBeneficiary = refFeignClient.getCategoryBeneficiary(beneficiary.getPerson().getCategoryBeneficiaryId());
            beneficiaryMap.put("category", categoryBeneficiary.getName());
        } else {
            beneficiaryMap.put("category", null);
        }
        if (beneficiary.getPerson().getProfessionId() != null) {
            ProfessionDTO profession = refFeignClient.getMetProfession(beneficiary.getPerson().getProfessionId());
            beneficiaryMap.put("profession", profession.getName());
        } else {
            beneficiaryMap.put("profession", null);
        }
        if (beneficiary.getPerson().getSourceBeneficiaryId() != null) {
            SourceBeneficiaryDTO sourceBeneficiaryDTO = refFeignClient.getSourceBeneficiary(beneficiary.getPerson().getSourceBeneficiaryId());
            beneficiaryMap.put("sourceBeneficiaira", sourceBeneficiaryDTO.getName());
        } else {
            beneficiaryMap.put("sourceBeneficiaira", null);
        }
        if (beneficiary.getPerson().getTypeKafalatId() != null) {
            TypeKafalatDTO typeKafalatDTO = refFeignClient.getParTypeKafalat(beneficiary.getPerson().getTypeKafalatId());
            beneficiaryMap.put("typeKafalat", typeKafalatDTO.getName());
        } else {
            beneficiaryMap.put("typeKafalat", null);
        }


        return beneficiaryMap;
    }

    private List<String> getPriseEnchargeforAudit(List<Long> Ids) {
        List<String> priseEnchargeforAudit = new ArrayList<>();

        if (Ids == null) {
            return priseEnchargeforAudit;
        }

        for (Long id : Ids) {
            TypePriseEnChargeDTO typePriseEnChargeDTO = refFeignClient.getParTypePriseEnCharge(id);
            priseEnchargeforAudit.add(typePriseEnChargeDTO.getName());
        }
        return priseEnchargeforAudit;
    }


    public AddedBeneficiaryResponse addSanitaryToBeneficiary(BeneficiarySanitary beneficiarySanitary) {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service addSanitaryToBeneficiary {}", beneficiarySanitary);

        // Ensure that the beneficiaryId is not null
        if (beneficiarySanitary.getBeneficiaryId() == null) {
            log.error("Beneficiary ID is null");
            return AddedBeneficiaryResponse.builder()
                    .id(null)
                    .code(null)
                    .personId(null)
                    .age(null) // Ajouter l'âge si nécessaire
                    .build();
        }

        // Retrieve the beneficiary or return an empty response if it does not exist
        Beneficiary beneficiary = beneficiaryRepository.findById(beneficiarySanitary.getBeneficiaryId())
                .orElse(null);


        // Handle case where the beneficiary does not exist in the database
        if (beneficiary == null) {
            log.error("Beneficiary not found for ID {}", beneficiarySanitary.getBeneficiaryId());
            return AddedBeneficiaryResponse.builder()
                    .id(null)
                    .code(null)
                    .personId(null)
                    .age(null) // Ajouter l'âge si nécessaire
                    .build();
        }

        // Store the old beneficiary data before making changes (if needed for comparison or audit)
        Beneficiary oldBeneficiary = Beneficiary.builder()
                .glasses(beneficiary.getGlasses())
                .diseaseTreatments(beneficiary.getDiseaseTreatments())
                .handicapped(beneficiary.getHandicapped())
                .beneficiaryAllergies(beneficiary.getBeneficiaryAllergies())
                .beneficiaryDiseases(beneficiary.getBeneficiaryDiseases())
                .build();
        boolean glass = false;
        if (oldBeneficiary.getGlasses() != null) {
            glass = oldBeneficiary.getGlasses().getUseGlasses();
        }

        List<String> oldAllergies = oldBeneficiary.getBeneficiaryAllergies().stream().map((allergyBeneficiaryAddDto) -> {
                    AllergiesDTO allergiesDTO = refFeignClient.getMetAllergies(allergyBeneficiaryAddDto.getAllergyId());
                    return allergiesDTO.getName();
                })
                .toList();
        List<String> oldDiseases = oldBeneficiary.getBeneficiaryDiseases().stream()
                .map((diseaseBeneficiaryAddDto) -> {
                    DiseasesDTO diseasesDTO = refFeignClient.getMetDiseases(diseaseBeneficiaryAddDto.getDiseaseId());
                    return diseasesDTO.getName();
                })
                .toList();
        BeneficiaryHandicap beneficiaryHandicap = null;
        if (!oldBeneficiary.getHandicapped().isEmpty()) {
            beneficiaryHandicap = oldBeneficiary.getHandicapped().get(0);
        }

        List<DiseaseTreatment> oldDiseaseTreatements = oldBeneficiary.getDiseaseTreatments();


        String oldAudit = convertToJson(glass, beneficiaryHandicap, oldAllergies, oldDiseases, oldDiseaseTreatements);

        List<DiseaseTreatment> diseaseTreatments = null;
        // Update the beneficiary's sanitary data
        if (beneficiarySanitary.getDiseaseTreatments() != null) {
            diseaseTreatments = saveDiseaseTreatment(beneficiary, beneficiarySanitary);
        }
        BeneficiaryHandicap handicapTypeDTO = saveHandicaps(beneficiary, beneficiarySanitary);
        List<String> allergies = saveAllergies(beneficiary, beneficiarySanitary);
        List<String> diseases = saveDiseases(beneficiary, beneficiarySanitary);
        Boolean hasGlasses = saveGlasses(beneficiary, beneficiarySanitary);


        String newAudit = convertToJson(hasGlasses, handicapTypeDTO, allergies, diseases, diseaseTreatments);
        beneficiaryRepository.save(beneficiary);
        auditApplicationService.audit("Modification d'Etat Sanitaire pour  Beneficiare : " + beneficiary.getCode(), getUsernameFromJwt(), "Add Family Member",
                oldAudit, newAudit, BENEFICIAIRE, UPDATE);


        // Calculate age if the birth date is available
        Integer age = null;
        if (beneficiary.getPerson() != null && beneficiary.getPerson().getBirthDate() != null) {
            age = calculateAge(beneficiary.getPerson().getBirthDate());
        }

        log.debug("End service Add Beneficiary, took {}", watch.toMS());

        // Build and return the response with additional information
        return AddedBeneficiaryResponse.builder()
                .id(beneficiary.getId())
                .code(beneficiary.getCode())
                .personId(beneficiary.getPerson() != null ? beneficiary.getPerson().getId() : null)
                .independent(beneficiary.getIndependent())
                .age(age) // Inclure l'âge dans la réponse
                .build();
    }

    public String convertToJson(Boolean hasGlasses, BeneficiaryHandicap handicapTypeDTO, List<String> allergies, List<String> diseases, List<DiseaseTreatment> diseaseTreatments) {
        Gson gson = new Gson();
        Map<String, Object> jsonMap = new LinkedHashMap<>();

        jsonMap.put("Porte des lunettes", hasGlasses ? "Oui" : "Non");
        Map<String, Object> handicap = new LinkedHashMap<>();
        if (handicapTypeDTO != null) {
            HandicapTypeDTO handicapTypeDTO1 = refFeignClient.getMetHandicapType(handicapTypeDTO.getHandicapTypeId());

            handicap.put("Type de handicap", handicapTypeDTO1.getName());
            handicap.put("Coût", handicapTypeDTO.getHandicapCost());
            handicap.put("Cause", handicapTypeDTO.getHandicapCause());
            handicap.put("Commentaire", handicapTypeDTO.getComment());
        } else {
            handicap.put("Type de handicap", "-");
            handicap.put("Coût", "-");
            handicap.put("Cause", "-");
            handicap.put("Commentaire", "-");
        }

        jsonMap.put("Personne handicap", handicap);

        if (allergies != null) {
            String allergiesString = String.join(",", allergies);
            jsonMap.put("Allergies", allergiesString);
        }

        if (diseases != null) {
            String diseasesString = String.join(",", diseases);
            jsonMap.put("Maladies", diseasesString);
        }

        String result = "";

        if (diseaseTreatments != null) {
            for (DiseaseTreatment diseaseTreatment : diseaseTreatments) {
                DiseaseTreatmentTypeDTO dto = refFeignClient.getMetDiseaseTreatmentType(diseaseTreatment.getTypeId());
                result += " | Traitement :" + dto.getName() + " - Coût :" + diseaseTreatment.getCost();
            }
        }
        result = result.replaceFirst("|", "");
        jsonMap.put("Traitementes", result);

        return gson.toJson(jsonMap);
    }


    // Utility method to calculate age
    private Integer calculateAge(Date birthDate) {
        LocalDate localBirthDate = convertToLocalDate(birthDate);
        Integer age = Period.between(localBirthDate, LocalDate.now()).getYears();
        return age;
    }

    private LocalDate convertToLocalDate(Date date) {
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }

    @Transactional
    public List<DiseaseTreatment> saveDiseaseTreatment(Beneficiary beneficiary, BeneficiarySanitary beneficiarySanitary) {
        List<Long> existingIds = new ArrayList<>();
        List<Long> updatedIds = new ArrayList<>();

        // Retrieve existing disease treatments from the database
        List<DiseaseTreatment> existingDiseaseTreatments = diseaseTreatmentRepository.findByBeneficiaryId(beneficiary.getId());

        // Collect existing IDs
        for (DiseaseTreatment diseaseTreatment : existingDiseaseTreatments) {
            existingIds.add(diseaseTreatment.getId());
        }
        List<DiseaseTreatment> diseaseTreatments = null;
        // Check if disease treatments are provided
        if (beneficiarySanitary.getDiseaseTreatments() != null && !beneficiarySanitary.getDiseaseTreatments().isEmpty()) {
            // Map DTOs to entities and save

            Iterable<DiseaseTreatment> mappedDiseaseTreatments = diseaseTreatmentMapper.diseaseTreatmentDTOToDiseaseTreatment(beneficiarySanitary.getDiseaseTreatments());
            for (DiseaseTreatment diseaseTreatment : mappedDiseaseTreatments) {
                diseaseTreatments = (List<DiseaseTreatment>) mappedDiseaseTreatments;
                diseaseTreatment.setBeneficiary(beneficiary);
                DiseaseTreatment savedDiseaseTreatment = diseaseTreatmentRepository.save(diseaseTreatment);
                updatedIds.add(savedDiseaseTreatment.getId());
            }

            // Delete disease treatments that are no longer in the list
            for (Long existingId : existingIds) {
                if (!updatedIds.contains(existingId)) {
                    if (diseaseTreatmentRepository.existsById(existingId)) {
                        diseaseTreatmentRepository.deleteById(existingId);
                    } else {
                        log.warn("DiseaseTreatment with ID {} does not exist.", existingId);
                    }
                }
            }
            beneficiary.setDiseaseTreatments((List) mappedDiseaseTreatments);
        } else {
            // If no disease treatments are provided, delete all existing ones
            for (Long existingId : existingIds) {
                if (diseaseTreatmentRepository.existsById(existingId)) {
                    diseaseTreatmentRepository.deleteById(existingId);
                } else {
                    log.warn("DiseaseTreatment with ID {} does not exist.", existingId);
                }
            }
        }
        if (beneficiarySanitary.getDiseaseTreatments() == null || beneficiarySanitary.getDiseaseTreatments().isEmpty()) {
            beneficiary.setDiseaseTreatments(List.of());
        }
        return diseaseTreatments;
    }


    @Transactional
    public BeneficiaryHandicap saveHandicaps(Beneficiary beneficiary, BeneficiarySanitary beneficiarySanitary) {
        List<Long> existingIds = new ArrayList<>();
        List<Long> updatedIds = new ArrayList<>();

        List<HandicapBeneficiaryAddDto> handicaps = beneficiarySanitary.getHandicapped();
        List<BeneficiaryHandicap> existingHandicaps = beneficiaryHandicapRepository.findByBeneficiaryId(beneficiary.getId());
        BeneficiaryHandicap existingHandicap = null;
        // Collect existing IDs
        for (BeneficiaryHandicap handicap : existingHandicaps) {
            existingIds.add(handicap.getId());
        }

        if (handicaps != null && !handicaps.isEmpty()) {
            // Map DTOs to entities and save or update them
            Iterable<BeneficiaryHandicap> mappedHandicaps = beneficiaryHandicapMapper.beneficiaryHandicapDtoToBeneficiaryHandicap(handicaps);
            for (BeneficiaryHandicap handicap : mappedHandicaps) {
                if (handicap.getId() != null) {
                    updatedIds.add(handicap.getId());
                }
                handicap.setBeneficiary(beneficiary);
                beneficiaryHandicapRepository.save(handicap);
                existingHandicap = handicap;
            }

            // Delete old handicaps that are no longer in the list
            for (Long existingId : existingIds) {
                if (!updatedIds.contains(existingId)) {
                    if (beneficiaryHandicapRepository.existsById(existingId)) {
                        beneficiaryHandicapRepository.deleteById(existingId);
                    } else {
                        log.warn("BeneficiaryHandicap with ID {} does not exist.", existingId);
                    }
                }
            }
            beneficiary.setHandicapped((List) mappedHandicaps);
        } else {
            // If no handicaps are provided, delete all existing ones
            for (Long existingId : existingIds) {
                if (beneficiaryHandicapRepository.existsById(existingId)) {
                    beneficiaryHandicapRepository.deleteById(existingId);
                } else {
                    log.warn("BeneficiaryHandicap with ID {} does not exist.", existingId);
                }
            }
        }
        if (handicaps == null || handicaps.isEmpty()) {
            beneficiary.setHandicapped(List.of());
        }
        return existingHandicap;
    }


    @Transactional
    public List<String> saveAllergies(Beneficiary beneficiary, BeneficiarySanitary beneficiarySanitary) {

        List<AllergyBeneficiaryAddDto> allergies = beneficiarySanitary.getBeneficiaryAllergies();
        List<String> allergyNames = null;
        if (allergies != null) {
            allergyNames = allergies.stream()
                    .map((allergyBeneficiaryAddDto) -> {
                        AllergiesDTO allergiesDTO = refFeignClient.getMetAllergies(allergyBeneficiaryAddDto.getAllergyId());
                        return allergiesDTO.getName();
                    })
                    .toList();
        }
        // Lists to keep track of existing and updated allergy IDs
        List<Long> existingIds = new ArrayList<>();
        List<Long> updatedIds = new ArrayList<>();

        List<BeneficiaryAllergy> existingAllergies = beneficiaryAllergyRepository.findByBeneficiaryId(beneficiary.getId());

        // Collect existing IDs
        for (BeneficiaryAllergy allergy : existingAllergies) {
            existingIds.add(allergy.getId());
        }

        if (allergies != null && !allergies.isEmpty()) {
            // Map the DTO allergies and save them to the database
            Iterable<BeneficiaryAllergy> mappedAllergies = beneficiaryAllergyMapper.beneficiaryAllergyDtoToBeneficiaryAllergy(allergies);
            for (BeneficiaryAllergy allergy : mappedAllergies) {
                allergy.setBeneficiary(beneficiary);
                BeneficiaryAllergy savedAllergy = beneficiaryAllergyRepository.save(allergy);
                updatedIds.add(savedAllergy.getId());
            }

            // Delete allergies that were not included in the request
            for (Long existingId : existingIds) {
                if (!updatedIds.contains(existingId)) {
                    if (beneficiaryAllergyRepository.existsById(existingId)) {
                        beneficiaryAllergyRepository.deleteById(existingId);
                    } else {
                        log.warn("AllergyBeneficiary with ID {} does not exist.", existingId);
                    }
                }
            }
            beneficiary.setBeneficiaryAllergies((List) mappedAllergies);
        } else {
            // If no allergies are provided, delete all existing ones
            for (BeneficiaryAllergy existingAllergy : existingAllergies) {
                if (beneficiaryAllergyRepository.existsById(existingAllergy.getId())) {
                    beneficiaryAllergyRepository.deleteById(existingAllergy.getId());
                } else {
                    log.warn("AllergyBeneficiary with ID {} does not exist.", existingAllergy.getId());
                }
            }
        }
        if (allergyNames == null || allergyNames.isEmpty()) {
            beneficiary.setBeneficiaryAllergies(List.of());
        }
        return allergyNames;
    }

    @Transactional
    public List<String> saveDiseases(Beneficiary beneficiary, BeneficiarySanitary beneficiarySanitary) {
        List<DiseaseBeneficiaryAddDto> diseases = beneficiarySanitary.getBeneficiaryDiseases();
        List<String> allergyNames = null;
        if (diseases != null) {
            allergyNames = diseases.stream()
                    .map((diseaseBeneficiaryAddDto) -> {
                        DiseasesDTO diseasesDTO = refFeignClient.getMetDiseases(diseaseBeneficiaryAddDto.getDiseaseId());
                        return diseasesDTO.getName();
                    })
                    .toList();
        }
        // Lists to track existing and updated disease IDs
        List<Long> existingIds = new ArrayList<>();
        List<Long> updatedIds = new ArrayList<>();

        // Retrieve existing diseases from the database
        List<BeneficiaryDisease> existingDiseases = beneficiaryDiseaseRepository.findByBeneficiaryId(beneficiary.getId());

        // Collect existing IDs
        for (BeneficiaryDisease disease : existingDiseases) {
            existingIds.add(disease.getId());
        }

        if (diseases != null && !diseases.isEmpty()) {
            // Map the DTO diseases to entities and save
            Iterable<BeneficiaryDisease> mappedDiseases = beneficiaryDiseasesMapper.beneficiaryDiseaseDtoToBeneficiaryDisease(diseases);
            for (BeneficiaryDisease disease : mappedDiseases) {
                disease.setBeneficiary(beneficiary);
                BeneficiaryDisease savedDisease = beneficiaryDiseaseRepository.save(disease);
                updatedIds.add(savedDisease.getId());
            }

            // Delete diseases that are no longer in the list
            for (Long existingId : existingIds) {
                if (!updatedIds.contains(existingId)) {
                    if (beneficiaryDiseaseRepository.existsById(existingId)) {
                        beneficiaryDiseaseRepository.deleteById(existingId);
                    } else {
                        log.warn("BeneficiaryDisease with ID {} does not exist.", existingId);
                    }
                }
            }
            beneficiary.setBeneficiaryDiseases((List) mappedDiseases);


        } else {
            // If no diseases are provided, delete all existing ones
            for (Long existingId : existingIds) {
                if (beneficiaryDiseaseRepository.existsById(existingId)) {
                    beneficiaryDiseaseRepository.deleteById(existingId);
                } else {
                    log.warn("BeneficiaryDisease with ID {} does not exist.", existingId);
                }
            }
        }
        if (allergyNames == null || allergyNames.isEmpty()) {
            beneficiary.setBeneficiaryDiseases(List.of());
        }
        return allergyNames;
    }

    @Transactional
    protected Boolean saveGlasses(Beneficiary beneficiary, BeneficiarySanitary beneficiarySanitary) {
        GlassesDto glassesDto = beneficiarySanitary.getGlasses();
        if (glassesDto != null) {
            glassesRepository.deleteByBeneficiaryId(beneficiary.getId());

            // Enregistrez les nouvelles lunettes
            Glasses glasses = Glasses.builder()
                    .useGlasses(glassesDto.getUseGlasses())
                    .beneficiary(beneficiary)
                    .build();
            glassesRepository.save(glasses);

        }
        return glassesDto.getUseGlasses();
    }

    public String generatePrecandidatCode(Long zoneId) {
        String code;
        long count = beneficiaryRepository.count();
        do {
            count++;
            Zone zone = zoneRepository.findById(zoneId).orElseThrow(() -> new IllegalArgumentException("Zone not found"));
            code = "1" + String.format("%06d", count) + "-" + zone.getCode();
        } while (beneficiaryRepository.existsByCode(code));

        return code;
    }


    public String generateBeneficiaryCode(Beneficiary beneficiary) {
        String code = "9"; // Prefix '9'

        // Extract year from the addedYear field
        String addedYear = beneficiary.getAddedYear();
        String year = addedYear.substring(0, 4);

        // Define statuses for beneficiaries
        List<Long> statuses = Arrays.asList(
                BeneficiaryStatus.BENEFICIAIRE_ACTIF.getId(),
                BeneficiaryStatus.BENEFICIAIRE_REJETE.getId(),
                BeneficiaryStatus.BENEFICIAIRE_ANCIEN.getId()
        );

        // Fetch the count for the current year and statuses
        Long count = beneficiaryRepository.countByAddedYearAndBeneficiaryStatutIdIn(addedYear, statuses);

        // Increment the count for the new beneficiary
        count = (count == null) ? 1 : count + 1;

        // Format the order number as 5 digits (e.g., 00001)
        String orderNumber = String.format("%05d", count);

        // Build the beneficiary code
        code += year + orderNumber;

        return code;
    }


    private void saveBeneficiaryPicture(MultipartFile picture, Person person, String beneficiaryCode) {

        if (picture != null) {
            String beneficiaryPath = beneficiariesFolder + person.getLastName().toUpperCase() + "-" + person.getFirstName().substring(0, 1).toUpperCase() + person.getFirstName().substring(1) + "_" + beneficiaryCode;

            Instant instant = Instant.now();
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.BASIC_ISO_DATE;
            String fileName = person.getLastName().toUpperCase() + "-" + person.getFirstName().substring(0, 1).toUpperCase() + person.getFirstName().substring(1) + "_" + abv + "_" + instant.atZone(ZoneId.systemDefault()).toLocalDate().format(dateTimeFormatter) + "." + FilenameUtils.getExtension(picture.getOriginalFilename());
            person.setPictureUrl(beneficiaryPath + "/" + folderPathPicture + "/" + fileName);
            minioService.WriteToMinIO(picture, beneficiaryPath + "/" + folderPathPicture + "/", fileName);
        }
    }

    @Transactional
    public void saveEducations(Beneficiary beneficiary, BeneficiaryEducation beneficiaryEducation) {
        List<EducationDTO> educationDTOs = beneficiaryEducation.getEducations();

        // Lists to track existing and updated education IDs
        List<Long> existingIds = new ArrayList<>();
        List<Long> updatedIds = new ArrayList<>();

        // Retrieve existing educations from the database
        List<Education> existingEducations = educationRepository.findByBeneficiaryId(beneficiary.getId());

        // Collect existing IDs
        for (Education education : existingEducations) {
            existingIds.add(education.getId());
        }

        if (educationDTOs != null && !educationDTOs.isEmpty()) {
            // Map DTOs to entities and save
            Iterable<Education> mappedEducations = educationMapper.educationDTOToEducation(educationDTOs);
            for (Education education : mappedEducations) {
                education.setBeneficiary(beneficiary);
                Education savedEducation = educationRepository.save(education);
                updatedIds.add(savedEducation.getId());
            }

            // Delete educations that are no longer in the list
            for (Long existingId : existingIds) {
                if (!updatedIds.contains(existingId)) {
                    if (educationRepository.existsById(existingId)) {
                        educationRepository.deleteById(existingId);
                    } else {
                        log.warn("Education with ID {} does not exist.", existingId);
                    }
                }
            }

            beneficiary.setEducations((List<Education>) mappedEducations);
        } else {
            // If no educations are provided, delete all existing ones
            for (Long existingId : existingIds) {
                if (educationRepository.existsById(existingId)) {
                    educationRepository.deleteById(existingId);
                } else {
                    log.warn("Education with ID {} does not exist.", existingId);
                }
            }
            beneficiary.setEducations(Collections.emptyList());
        }

    }


    @Transactional
    public void saveScholarships(Beneficiary beneficiary, BeneficiaryEducation beneficiaryEducation) {
        List<ScholarshipBeneficiaryDTO> scholarshipBeneficiaries = beneficiaryEducation.getScholarshipBeneficiaries();

        // Lists to track existing and updated scholarship IDs
        List<Long> existingIds = new ArrayList<>();
        List<Long> updatedIds = new ArrayList<>();

        // Retrieve existing scholarships for the beneficiary
        List<ScholarshipBeneficiary> existingScholarships = scholarshipRepository.findByBeneficiaryId(beneficiary.getId());

        // Collect existing IDs
        for (ScholarshipBeneficiary scholarship : existingScholarships) {
            existingIds.add(scholarship.getId());
        }

        if (scholarshipBeneficiaries != null && !scholarshipBeneficiaries.isEmpty()) {
            // Map DTOs to entities and save
            Iterable<ScholarshipBeneficiary> mappedScholarships = scholarshipMapper.scholarshipBeneficiaryDTOToScholarshipBeneficiary(scholarshipBeneficiaries);
            for (ScholarshipBeneficiary scholarship : mappedScholarships) {
                scholarship.setBeneficiary(beneficiary);
                ScholarshipBeneficiary savedScholarship = scholarshipRepository.save(scholarship);
                updatedIds.add(savedScholarship.getId());
            }

            // Delete scholarships that are no longer in the list
            for (Long existingId : existingIds) {
                if (!updatedIds.contains(existingId)) {
                    if (scholarshipRepository.existsById(existingId)) {
                        scholarshipRepository.deleteById(existingId);
                    } else {
                        log.warn("ScholarshipBeneficiary with ID {} does not exist.", existingId);
                    }
                }
            }

            beneficiary.setScholarshipBeneficiaries((List<ScholarshipBeneficiary>) mappedScholarships);
        } else {
            // If no scholarships are provided, delete all existing ones
            for (Long existingId : existingIds) {
                if (scholarshipRepository.existsById(existingId)) {
                    scholarshipRepository.deleteById(existingId);
                } else {
                    log.warn("ScholarshipBeneficiary with ID {} does not exist.", existingId);
                }
            }
            beneficiary.setScholarshipBeneficiaries(Collections.emptyList());
        }

    }


    public void saveEps(Beneficiary savedBeneficiary, BeneficiaryAddDTO beneficiary) {

        ArrayList<Long> existingIds = new ArrayList<>();
        ArrayList<Long> updatedIds = new ArrayList<>();

        if (beneficiary.getId() != null) {
            if (beneficiary.getId() != null) {
                // Get ids of existing eps for the beneficiary in the database
                List<EpsResident> epsResidents = (List<EpsResident>) epsResidentRepository.findByBeneficiaryId(beneficiary.getId());
                for (EpsResident epsResident : epsResidents) {
                    existingIds.add(epsResident.getId());
                }
            }
        }

        if (beneficiary.getEpsResidents() != null) {

            Iterable<EpsResident> epsResidentIterable = epsResidentMapper.epsResidentDTOToEpsResident(beneficiary.getEpsResidents());
            for (EpsResident epsResident : epsResidentIterable) {
                if (epsResident.getId() != null) {
                    updatedIds.add(epsResident.getId());
                }
                epsResident.setBeneficiary(savedBeneficiary);
                epsResidentRepository.save(epsResident);
            }
        }

        if (beneficiary.getId() != null) {
            // Deleting Eps that were not sent in the request
            for (int i = 0; i < existingIds.size(); i++) {
                if (!updatedIds.contains(existingIds.get(i))) {
                    epsResidentRepository.deleteById(existingIds.get(i));
                }
            }
        }
    }

    public void saveServices(Beneficiary savedBeneficiary, BeneficiaryAddDTO beneficiary) {
        List<ServiceAndStatus> serviceAndStatuses = beneficiary.getBeneficiaryServices();

        ArrayList<Long> existingIds = new ArrayList<>();
        ArrayList<Long> updatedIds = new ArrayList<>();

        if (serviceAndStatuses != null) {

            if (beneficiary.getId() != null) {
                // Get ids of existing eps for the beneficiary in the database
                var beneficiaryServices = (List<ma.almobadara.backend.model.beneficiary.BeneficiaryService>) beneficiaryServiceRepository.findByBeneficiaryId(beneficiary.getId());
                for (ma.almobadara.backend.model.beneficiary.BeneficiaryService beneficiaryService : beneficiaryServices) {
                    existingIds.add(beneficiaryService.getId());
                }
            }

            for (ServiceAndStatus serviceAndStatus : serviceAndStatuses) {
                if (serviceAndStatus.getId() != null) {
                    updatedIds.add(serviceAndStatus.getId());
                }

                Optional<ServiceDTO> serviceDTO = Optional.ofNullable(serviceAndStatus.getService());
//				Optional<ServiceDTO> service = serviceRepository.findById(serviceDTO.getId());
                if (serviceDTO.isPresent()) {
                    StatusDTO status = serviceAndStatus.getStatus();
                    ma.almobadara.backend.model.beneficiary.BeneficiaryService beneficiaryService = new ma.almobadara.backend.model.beneficiary.BeneficiaryService(serviceAndStatus.getId(), savedBeneficiary, serviceDTO.get().getId(), status.getId());
                    beneficiaryServiceRepository.save(beneficiaryService);
                }

            }

            if (beneficiary.getId() != null) {
                // Deleting Eps that were not sent in the request
                for (int i = 0; i < existingIds.size(); i++) {
                    if (!updatedIds.contains(existingIds.get(i))) {
                        beneficiaryServiceRepository.deleteById(existingIds.get(i));
                    }
                }
            }
        } else {

            if (beneficiary.getId() != null) {
                // Get ids of existing eps for the beneficiary in the database
                var beneficiaryServices = beneficiaryServiceRepository.findByBeneficiaryId(beneficiary.getId());
                for (ma.almobadara.backend.model.beneficiary.BeneficiaryService beneficiaryService : beneficiaryServices) {
                    beneficiaryServiceRepository.deleteById(beneficiaryService.getId());
                }
            }

        }
    }

    public AddedBeneficiaryResponse addEducationToBeneficiary(BeneficiaryEducation beneficiaryEducation) {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service addEducationToBeneficiary {}", beneficiaryEducation);

        // Vérifier que l'ID du bénéficiaire est non nul
        if (beneficiaryEducation.getBeneficiaryId() == null) {
            return AddedBeneficiaryResponse.builder().build();
        }

        // Récupérer le bénéficiaire
        Beneficiary beneficiary = beneficiaryRepository.findById(beneficiaryEducation.getBeneficiaryId())
                .orElse(Beneficiary.builder().build());

        // Assurer que le bénéficiaire existe
        if (beneficiary.getId() == null) {
            return AddedBeneficiaryResponse.builder().build();
        }

        Map<String, String> oldValues = new LinkedHashMap<>();


        // Récupérer la personne associée au bénéficiaire
        Person person = beneficiary.getPerson();
        if (person.isEducated()) {
            SchoolLevelDTO fullSchoolLevelOldDTO = null;
            // Sauvegarder les modifications du bénéficiaire
            if (beneficiaryEducation.isEducated()) {
                fullSchoolLevelOldDTO = refFeignClient.getParSchoolLevel(person.getSchoolLevelId());
            }

            Beneficiary beneficiary1 = beneficiaryRepository.findById(beneficiaryEducation.getBeneficiaryId()).get();

            // Mettre à jour les informations d'éducation dans la personne
            oldValues.put("Educated", String.valueOf(person.isEducated()));
            oldValues.put("Historique", beneficiary1.getEducations() != null ? String.valueOf(beneficiary1.getEducations().size()) : "0");
            oldValues.put("bourses", beneficiary1.getScholarshipBeneficiaries() != null ? String.valueOf(beneficiary1.getScholarshipBeneficiaries().size()) : "0");
            oldValues.put("schoolName", person.getSchoolName() != null ? person.getSchoolName() : "-");
            oldValues.put("schoolNameAr", person.getSchoolNameAr() != null ? person.getSchoolNameAr() : "-");
            oldValues.put("Niveau", (fullSchoolLevelOldDTO != null && fullSchoolLevelOldDTO.getType() != null) ? fullSchoolLevelOldDTO.getType() : "-");
            oldValues.put("Class", (fullSchoolLevelOldDTO != null && fullSchoolLevelOldDTO.getName() != null) ? fullSchoolLevelOldDTO.getName() : "-");
        } else {
            oldValues.put("Educated", "false");
            oldValues.put("Historique", "0");
            oldValues.put("bourses", "0");
            oldValues.put("schoolName", "-");
            oldValues.put("schoolNameAr", "-");
            oldValues.put("Niveau", "-");
            oldValues.put("Class", "-");
        }
        if (!beneficiaryEducation.isEducated()) {
            person.setSchoolLevelId(null);
            person.setSchoolName(null);
            person.setSchoolNameAr(null);
            person.setEducated(false);
            Map<String, String> params = new LinkedHashMap<>();
            params.put("Educated", "false");
            params.put("Historique", "0");
            params.put("bourses", "0");
            params.put("schoolName", "-");
            params.put("schoolNameAr", "-");
            params.put("Niveau", "-");
            params.put("Class", "-");
            auditApplicationService.audit("Ajout d'un Education pour  Beneficiare : " + beneficiary.getCode(), getUsernameFromJwt(), "Add Family Member",
                    beneficiaryEducation.getAudit(oldValues), beneficiaryEducation.getAudit(params), BENEFICIAIRE, UPDATE);
            return AddedBeneficiaryResponse.builder()
                    .id(beneficiary.getId())
                    .code(beneficiary.getCode())
                    .personId(beneficiary.getPerson().getId())
                    .build();
        }
        if (person != null) {


            person.setSchoolLevelId(beneficiaryEducation.getSchoolLevelId());
            person.setSchoolName(beneficiaryEducation.getSchoolName());
            person.setSchoolNameAr(beneficiaryEducation.getSchoolNameAr());
            person.setEducated(beneficiaryEducation.isEducated());

            // Enregistrer la personne mise à jour
            personRepository.save(person);
        }

        // Enregistrer les informations d'éducation et de bourses dans le bénéficiaire
        saveEducations(beneficiary, beneficiaryEducation);
        saveScholarships(beneficiary, beneficiaryEducation);

        SchoolLevelDTO fullSchoolLevelDTO = null;
        // Sauvegarder les modifications du bénéficiaire
        if (beneficiaryEducation.isEducated()) {
            fullSchoolLevelDTO = refFeignClient.getParSchoolLevel(beneficiaryEducation.getSchoolLevelId());
        }


        beneficiaryRepository.save(beneficiary);
        Map<String, String> params = new LinkedHashMap<>();
        params.put("Educated", String.valueOf(beneficiaryEducation.isEducated()));
        params.put("Historique", beneficiaryEducation.getEducations() != null ? String.valueOf(beneficiaryEducation.getEducations().size()) : "0");
        params.put("schoolName", beneficiaryEducation.getSchoolName() != null ? beneficiaryEducation.getSchoolName() : "-");
        params.put("schoolNameAr", beneficiaryEducation.getSchoolNameAr() != null ? beneficiaryEducation.getSchoolNameAr() : "-");
        params.put("bourses", beneficiaryEducation.getScholarshipBeneficiaries() != null ? String.valueOf(beneficiaryEducation.getScholarshipBeneficiaries().size()) : "0");
        params.put("Niveau", (fullSchoolLevelDTO != null && fullSchoolLevelDTO.getType() != null) ? fullSchoolLevelDTO.getType() : "-");
        params.put("Class", (fullSchoolLevelDTO != null && fullSchoolLevelDTO.getName() != null) ? fullSchoolLevelDTO.getName() : "-");


        auditApplicationService.audit("Modification d'un Education pour  Beneficiare : " + beneficiary.getCode(), getUsernameFromJwt(), "Add Family Member",
                beneficiaryEducation.getAudit(oldValues), beneficiaryEducation.getAudit(params), BENEFICIAIRE, UPDATE);

        log.debug("End service Add Beneficiary, took {}", watch.toMS());

        // Construire et retourner la réponse
        return AddedBeneficiaryResponse.builder()
                .id(beneficiary.getId())
                .code(beneficiary.getCode())
                .personId(beneficiary.getPerson().getId())
                .build();
    }

    public void makeBeneficiaryNotEducated(Long id) {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service makeBeneficiaryNotEducated {}", id);
        // Récupérer le bénéficiaire
        Beneficiary beneficiary = beneficiaryRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Beneficiary not found with id: " + id));
        // Récupérer la personne associée au bénéficiaire
        Person person = beneficiary.getPerson();
        if (person != null) {
            person.setEducated(false);
            person.setSchoolLevelId(null);
            person.setSchoolName(null);
            person.setSchoolNameAr(null);

            // Enregistrer la personne mise à jour
            personRepository.save(person);
        }
        log.debug("End service makeBeneficiaryNotEducated, took {}", watch.toMS());
    }


    public AddedBeneficiaryResponse addPieceJointeToBeneficiary(BeneficiaryPieceJointe beneficiaryPieceJointe) throws TechnicalException, IOException {
        Long beneficiaryId = beneficiaryPieceJointe.getBeneficiaryId();
        Beneficiary beneficiary = beneficiaryRepository.findById(beneficiaryId)
                .orElseThrow(() -> new RuntimeException("Beneficiary not found with id: " + beneficiaryId));
        Map<String, Object> jsonMap = new LinkedHashMap<>();

        if (beneficiaryPieceJointe.getCin() != null) {
            jsonMap.put("Carte d'Identité Nationale", beneficiaryPieceJointe.getCin().getAudit());
            addDocumentToBeneficiary(beneficiary, beneficiaryPieceJointe.getCin(), "beneficiary");

        }
        if (beneficiaryPieceJointe.getDeseasedAct() != null) {
            jsonMap.put("Certificat de décès", beneficiaryPieceJointe.getDeseasedAct().getAudit());
            addDocumentToBeneficiary(beneficiary, beneficiaryPieceJointe.getDeseasedAct(), "beneficiary");


        }
        if (beneficiaryPieceJointe.getBirthAct() != null) {
            jsonMap.put("Extrait de naissance", beneficiaryPieceJointe.getBirthAct().getAudit());
            addDocumentToBeneficiary(beneficiary, beneficiaryPieceJointe.getBirthAct(), "beneficiary");

        }
        if (beneficiaryPieceJointe.getMedicalCertificate() != null) {
            jsonMap.put("Certificat médical", beneficiaryPieceJointe.getMedicalCertificate().getAudit());
            addDocumentToBeneficiary(beneficiary, beneficiaryPieceJointe.getMedicalCertificate(), "beneficiary");


        }
        if (beneficiaryPieceJointe.getFamillyCertificate() != null) {
            jsonMap.put("Attestation de prise en charge familiale", beneficiaryPieceJointe.getFamillyCertificate().getAudit());
            addDocumentToBeneficiary(beneficiary, beneficiaryPieceJointe.getFamillyCertificate(), "beneficiary");


        }
        if (beneficiaryPieceJointe.getSchoolCertificate() != null) {
            jsonMap.put("Attestation scolaire", beneficiaryPieceJointe.getSchoolCertificate().getAudit());
            addDocumentToBeneficiary(beneficiary, beneficiaryPieceJointe.getSchoolCertificate(), "beneficiary");


        }
        Gson gson = new Gson();


        auditApplicationService.audit("Ajout d'Etat Sanitaire pour  Beneficiare : " + beneficiary.getCode(), getUsernameFromJwt(), "Add Family Member",
                null, gson.toJson(jsonMap), BENEFICIAIRE, CREATE);
        beneficiaryRepository.save(beneficiary);

        return AddedBeneficiaryResponse.builder()
                .id(beneficiary.getId())
                .code(beneficiary.getCode())
                .personId(beneficiary.getPerson() != null ? beneficiary.getPerson().getId() : null)
                .build();
    }

    private void addDocumentToBeneficiary(Beneficiary beneficiary, DocumentBeneficiaryAddDto documentDto, String entityType) throws TechnicalException, IOException {
        DocumentAndEntityDto documentAndEntityDto = new DocumentAndEntityDto();
        DocumentDTO documentDTO = new DocumentDTO();

        documentDTO.setLabel(documentDto.getLabel());
        documentDTO.setDocumentDate(documentDto.getDocumentDate());
        documentDTO.setExpiryDate(documentDto.getExpiryDate());
        documentDTO.setComment(documentDto.getComment());
        documentDTO.setFileUrl(documentDto.getFileUrl());
        documentDTO.setFileName(documentDto.getFileUrl());
        documentDTO.setFile(documentDto.getFile());
        documentDTO.setType(documentDto.getType());


        documentAndEntityDto.setDocumentDTO(documentDTO);
        documentAndEntityDto.setEntityId(beneficiary.getId());
        documentAndEntityDto.setEntityType(entityType);

        DocumentDTO newDocumentDTO = documentService.addDocument(documentAndEntityDto);

        DocumentBeneficiary documentBeneficiary = DocumentBeneficiary.builder()
                .beneficiary(beneficiary)
                .document(documentMapper.documentToModelToModel(newDocumentDTO))
                .build();

        documentBeneficiaryRepository.save(documentBeneficiary);
    }

    private String convertMapToJsonString(Map<String, String> map) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.writeValueAsString(map);
        } catch (Exception e) {
            // Gérer les exceptions de sérialisation JSON ici
            e.printStackTrace();
            return "{}"; // Retourne une chaîne JSON vide en cas d'erreur
        }
    }

    public Page<GetListDTO> getAllBeneficiaries(Optional<String> criteria, Optional<String> value1, Optional<String> value2,
                                                String searchByNom, String lastNameAr, Boolean searchByTypeBeneficiaire,
                                                String searchByService, String searchByStatut, String searchByNumTel,
                                                Date minDate, Date maxDate, Boolean isCandidateInitial, Boolean isValidateAssistant, Boolean isValidateKafalat, Boolean isOldBeneficiary, Boolean isBeneficiaryRejete, Boolean isBeneficiaryArchived, Boolean isCandidateRejete,
                                                Boolean isBenefeiciaryWaiting, Boolean isBeneficiaryActif,Long searchByTagId,Long searchByZoneId,
                                                Optional<Integer> page) {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service Get All Beneficiaries");


        Pageable pageable = createPageable(page);

        Boolean isCandidate = false;
        Map<String, String> searchParams = collectSearchParams(searchByNom, lastNameAr, searchByTypeBeneficiaire, searchByService, searchByStatut, searchByNumTel, minDate, maxDate);
        String jsonSearchParams = convertMapToJsonString(searchParams);

        Page<Beneficiary> listBeneficiaries = findBeneficiaries(criteria, value1, value2, searchByNom, lastNameAr, searchByTypeBeneficiaire, searchByService, searchByStatut, searchByNumTel, minDate, maxDate, isCandidate, isCandidateInitial, isValidateAssistant, isValidateKafalat, isOldBeneficiary, isBeneficiaryRejete, isBeneficiaryArchived, isCandidateRejete, isBenefeiciaryWaiting, isBeneficiaryActif,searchByTagId,searchByZoneId, pageable);

        auditBeneficiaries(jsonSearchParams, searchByNom, lastNameAr, searchByTypeBeneficiaire, searchByService, searchByStatut, searchByNumTel, minDate, maxDate, listBeneficiaries, isCandidate, isCandidateInitial, isValidateAssistant, isValidateKafalat, isOldBeneficiary, isBeneficiaryRejete, isBeneficiaryArchived, isCandidateRejete, isBenefeiciaryWaiting, isBeneficiaryActif);
        ;


        listBeneficiaries.forEach(beneficiary -> {
            beneficiary.checkAndUpdateStatus();
            beneficiaryRepository.save(beneficiary);
        });

        List<GetListDTO> beneficiaries = mapToDTOs(listBeneficiaries);

        log.debug("End service Get All Beneficiaries, took {}", watch.toMS());
        return new PageImpl<>(beneficiaries, pageable, listBeneficiaries.getTotalElements());
    }

    public Page<GetListDTO> getAllCandidates(Optional<String> criteria, Optional<String> value1, Optional<String> value2,
                                             String searchByNom, String lastNameAr, Boolean searchByTypeBeneficiaire,
                                             String searchByService, String searchByStatut, String searchByNumTel,
                                             Date minDate, Boolean isCandidateInitial, Boolean isValidateAssistant, Boolean isValidateKafalat, Boolean isCandidate, Date maxDate, Boolean isOldBeneficiary, Boolean isBeneficiaryRejete, Boolean isBeneficiaryArchived, Boolean isCandidateRejete, Boolean isBenefeiciaryWaiting,Long searchByTagId, Boolean isBeneficiaryActif,Long searchByZoneId, Optional<Integer> page) {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service Get All Candidates");

        Pageable pageable = createPageable(page);

        Map<String, String> searchParams = collectSearchParams(searchByNom, lastNameAr, searchByTypeBeneficiaire, searchByService, searchByStatut, searchByNumTel, minDate, maxDate);
        String jsonSearchParams = convertMapToJsonString(searchParams);

        Page<Beneficiary> listBeneficiaries = findBeneficiaries(criteria, value1, value2, searchByNom, lastNameAr, searchByTypeBeneficiaire, searchByService, searchByStatut, searchByNumTel, minDate, maxDate, isCandidate, isCandidateInitial, isValidateAssistant, isValidateKafalat, isOldBeneficiary, isBeneficiaryRejete, isBeneficiaryArchived, isCandidateRejete, isBenefeiciaryWaiting, isBeneficiaryActif,searchByTagId,searchByZoneId, pageable);

        auditBeneficiaries(jsonSearchParams, searchByNom, lastNameAr, searchByTypeBeneficiaire, searchByService, searchByStatut, searchByNumTel, minDate, maxDate, listBeneficiaries, isCandidate, isCandidateInitial, isValidateAssistant, isValidateKafalat, isOldBeneficiary, isBeneficiaryRejete, isBeneficiaryArchived, isCandidateRejete, isBenefeiciaryWaiting, isBeneficiaryActif);
        ;

        // Filter the beneficiaries based on the specified statuses
        List<GetListDTO> candidates = mapToDTOs(listBeneficiaries);

        // Filtrer les DTOs basés sur les statuts spécifiés
        //List<GetListDTO> candidates = filterCandidates(dtoList,  isCandidateInitial, isValidateAssistant, isValidateKafalat);

        //List<Long> statusIds = Arrays.asList(1L, 2L, 3L);
        //var totalCandidatesElements = beneficiaryRepository.countByArchivedFalseAndBeneficiaryStatutIn(statusIds);

        log.debug("End service Get All Candidates, took {}", watch.toMS());
        return new PageImpl<>(candidates, pageable, listBeneficiaries.getTotalElements());
    }

    public Page<GetListDTO> getAllOldBeneficiaries(Optional<String> criteria, Optional<String> value1, Optional<String> value2,
                                                   String searchByNom, String lastNameAr, Boolean searchByTypeBeneficiaire,
                                                   String searchByService, String searchByStatut, String searchByNumTel,
                                                   Date minDate, Date maxDate, Boolean isCandidateInitial, Boolean isValidateAssistant, Boolean isValidateKafalat, Boolean isOldBeneficiary, Boolean isBeneficiaryRejete, Boolean isBeneficiaryArchived, Boolean isCandidateRejete, Boolean isBenefeiciaryWaiting, Boolean isBeneficiaryActif,Long seachByTagId,Long searchByZoneId, Optional<Integer> page) {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service Get All Beneficiaries Archived");

        Pageable pageable = createPageable(page);

        Boolean isCandidate = false;
        Map<String, String> searchParams = collectSearchParams(searchByNom, lastNameAr, searchByTypeBeneficiaire, searchByService, searchByStatut, searchByNumTel, minDate, maxDate);
        String jsonSearchParams = convertMapToJsonString(searchParams);

        Page<Beneficiary> listBeneficiaries = findBeneficiaries(criteria, value1, value2, searchByNom, lastNameAr, searchByTypeBeneficiaire, searchByService, searchByStatut, searchByNumTel, minDate, maxDate, isCandidate, isCandidateInitial, isValidateAssistant, isValidateKafalat, isOldBeneficiary, isBeneficiaryRejete, isBeneficiaryArchived, isCandidateRejete, isBenefeiciaryWaiting, isBeneficiaryActif,seachByTagId,searchByZoneId, pageable);

        auditBeneficiaries(jsonSearchParams, searchByNom, lastNameAr, searchByTypeBeneficiaire, searchByService, searchByStatut, searchByNumTel, minDate, maxDate, listBeneficiaries, isCandidate, isCandidateInitial, isValidateAssistant, isValidateKafalat, isOldBeneficiary, isBeneficiaryRejete, isBeneficiaryArchived, isCandidateRejete, isBenefeiciaryWaiting, isBeneficiaryActif);

        List<GetListDTO> beneficiaries = mapToDTOs(listBeneficiaries);

        log.debug("End service Get All Beneficiaries, took {}", watch.toMS());
        return new PageImpl<>(beneficiaries, pageable, listBeneficiaries.getTotalElements());
    }

    private List<GetListDTO> filterCandidates(
            List<GetListDTO> dtoList,
            Boolean isCandidateInitial,
            Boolean isValidateAssistant,
            Boolean isValidateKafalat
    ) {
        return dtoList.stream()
                .filter(dto -> {
                    Long statusId = dto.getBeneficiaryStatutId();
                    if (Boolean.TRUE.equals(isCandidateInitial)) {
                        return BeneficiaryStatus.CANDIDAT_INITIAL.getId().equals(statusId);
                    } else if (Boolean.TRUE.equals(isValidateAssistant)) {
                        return BeneficiaryStatus.CANDIDAT_VALIDER_ASSISTANCE.getId().equals(statusId);
                    } else if (Boolean.TRUE.equals(isValidateKafalat)) {
                        return BeneficiaryStatus.CANDIDAT_VALIDER_KAFALAT.getId().equals(statusId);
                    } else {
                        return BeneficiaryStatus.CANDIDAT_INITIAL.getId().equals(statusId) ||
                                BeneficiaryStatus.CANDIDAT_VALIDER_ASSISTANCE.getId().equals(statusId) ||
                                BeneficiaryStatus.CANDIDAT_VALIDER_KAFALAT.getId().equals(statusId) ||
                                BeneficiaryStatus.CANDIDAT_A_COMPLETER_PAR_ASSISTANCE.getId().equals(statusId);
                    }
                })
                .collect(Collectors.toList());
    }


    private Pageable createPageable(Optional<Integer> page) {
        int pageNumber = page.orElse(0);
        int pageSize = 10;
        Sort.Direction sortDirection = Sort.Direction.DESC;
        String sortBy = "modifiedAt";
        return PageRequest.of(pageNumber, pageSize, Sort.by(sortDirection, sortBy));
    }

    private Map<String, String> collectSearchParams(String searchByNom, String lastNameAr, Boolean searchByTypeBeneficiaire,
                                                    String searchByService, String searchByStatut, String searchByNumTel,
                                                    Date minDate, Date maxDate) {
        Map<String, String> searchParams = new LinkedHashMap<>();
        if (searchByNom != null) searchParams.put("Nom du bénéficiaire", searchByNom);
        if (lastNameAr != null) searchParams.put("Nom arabe du bénéficiaire", lastNameAr);
        if (searchByTypeBeneficiaire != null)
            searchParams.put("Type du bénéficiaire", searchByTypeBeneficiaire ? "Indépendant" : "Membre de Famille");
        if (searchByService != null)
            searchParams.put("Service", refFeignClient.getMetService(Long.valueOf(searchByService)).getName());
        if (searchByStatut != null)
            searchParams.put("Statut", refFeignClient.getParStatus(Long.valueOf(searchByStatut)).getName());
        if (searchByNumTel != null) searchParams.put("Numéro Téléphone", searchByNumTel);
        if (minDate != null) searchParams.put("Date de naissance minimale", minDate.toString());
        if (maxDate != null) searchParams.put("Date de naissance maximale", maxDate.toString());
        return searchParams;
    }

    private Page<Beneficiary> findBeneficiaries(Optional<String> criteria, Optional<String> value1, Optional<String> value2,
                                                String searchByNom, String lastNameAr, Boolean searchByTypeBeneficiaire,
                                                String searchByService, String searchByStatut, String searchByNumTel,
                                                Date minDate, Date maxDate, Boolean isCandidate, Boolean isCandidateInitial,
                                                Boolean isValidateAssistant, Boolean isValidateKafalat, Boolean isOldBeneficiary,
                                                Boolean isBeneficiaryRejete, Boolean isBeneficiaryArchived, Boolean isCandidateRejete,
                                                Boolean isBenefeiciaryWaiting, Boolean isBeneficiaryActif,Long searchByTagId,Long searchByZoneId, Pageable pageable) {

        List<AssistantZone> assistantZones= List.of();
        CacheAdUser cacheAdUser = getRoleFromJwt();
        // Get impersonation token from request header
        String impersonationToken = request.getHeader("Impersonation-Token");
        CacheAdUserDTO cacheAdUser1 = null;
        if (impersonationToken != null && !impersonationToken.isEmpty()) {
            cacheAdUser1 = tokenImpersonationService.getImpersonatedUser(impersonationToken);
        }
        if (cacheAdUser1 != null && cacheAdUser1.getRole().getCode().equals(RoleCode.ASSISTANT.getCode())) {
            Assistant assistant = assistantRepository.findByCacheAdUserId(cacheAdUser1.getId());
            if (assistant != null) {
                if (!assistant.getAssistantZones().isEmpty()) {
                    assistantZones = assistant.getAssistantZones();
                } else {
                    return new PageImpl<>(Collections.emptyList(), pageable, 0);
                }
            } else {
                return new PageImpl<>(Collections.emptyList(), pageable, 0);
            }
        }
        else if (cacheAdUser != null && cacheAdUser.getRole().getCode().equals(RoleCode.ASSISTANT.getCode())) {
            Assistant assistant = assistantRepository.findByCacheAdUserId(cacheAdUser.getId());
            if (assistant != null) {
                if (!assistant.getAssistantZones().isEmpty()) {
                    assistantZones = assistant.getAssistantZones();
                } else {
                    return new PageImpl<>(Collections.emptyList(), pageable, 0);
                }
            } else {
                return new PageImpl<>(Collections.emptyList(), pageable, 0);
            }
        }

        if (criteria.isPresent() && value1.isPresent()) {
            return searchBeneficiariesByCriteria(criteria.get(), value1, value2, pageable);
        } else if (searchByNom != null || lastNameAr != null || searchByTypeBeneficiaire != null || searchByService != null ||
                searchByStatut != null || searchByNumTel != null || minDate != null || maxDate != null || isCandidate != null
                || isCandidateInitial != null || isValidateAssistant != null || isValidateKafalat != null || isOldBeneficiary != null
                || isBeneficiaryRejete != null || isBeneficiaryArchived != null || isCandidateRejete != null || isBenefeiciaryWaiting != null || isBeneficiaryActif != null || searchByTagId != null || searchByZoneId != null) {
            return filterBeneficiaries(searchByZoneId==null?assistantZones.stream()
                    .map(assistantZone -> assistantZone.getZone().getId())
                    .collect(Collectors.toList()):List.of(searchByZoneId), searchByNom, lastNameAr, searchByTypeBeneficiaire, searchByService, searchByStatut, searchByNumTel, minDate, maxDate, isCandidate, isCandidateInitial, isValidateAssistant, isValidateKafalat, isOldBeneficiary, isBeneficiaryRejete, isBeneficiaryArchived, isCandidateRejete, isBenefeiciaryWaiting, isBeneficiaryActif,searchByTagId, pageable);
        } else {
            if (assistantZones != null) {
                return beneficiaryRepository.findBeneficiaryByArchivedIsFalseAndZoneIdIn(pageable,  assistantZones.stream()
                        .map(assistantZone -> assistantZone.getZone().getId())
                        .collect(Collectors.toList()));
            } else {
                return beneficiaryRepository.findBeneficiaryByArchivedIsFalse(pageable);
            }

        }
    }

    private Page<Beneficiary> searchBeneficiariesByCriteria(String criteria, Optional<String> value1, Optional<String> value2, Pageable pageable) {
        if (criteria.equals("name")) {
            String firstName = value1.orElse("");
            String lastName = value2.orElse("");
            return beneficiaryRepository.searchBeneficiaryByName(firstName, lastName, pageable);
        }
        return Page.empty(pageable);
    }

    private void auditBeneficiaries(String jsonSearchParams, String searchByNom, String lastNameAr, Boolean searchByTypeBeneficiaire,
                                    String searchByService, String searchByStatut, String searchByNumTel, Date minDate,
                                    Date maxDate, Page<Beneficiary> listBeneficiaries, Boolean isCandidate, Boolean isCandidateInitial,
                                    Boolean isValidateAssistant, Boolean isValidateKafalat, Boolean isOldBeneficiary, Boolean isBeneficiaryRejete,
                                    Boolean isBeneficiaryArchived, Boolean isCandidateRejete, Boolean isBenefeiciaryWaiting, Boolean isBeneficiaryActif) {

        String auditMessage = searchByNom != null || lastNameAr != null || searchByTypeBeneficiaire != null ||
                searchByService != null || searchByStatut != null || searchByNumTel != null ||
                minDate != null || maxDate != null
                ? "Recherche par filtre dans liste des "
                : "Consultation de la liste des ";

        StringBuilder details = new StringBuilder();

        // Append statuses if any are true
        if (Boolean.TRUE.equals(isCandidateInitial)) details.append("Candidates Initials ");
        else if (Boolean.TRUE.equals(isValidateAssistant)) details.append("Candidates Validé par Assistant ");
        else if (Boolean.TRUE.equals(isValidateKafalat)) details.append("Candidates Validé pour Kafalat ");
        else if (Boolean.TRUE.equals(isCandidate)) details.append("Candidates ");
        else if (Boolean.TRUE.equals(isBeneficiaryRejete)) details.append("Bénéficiaires Rejeté ");
        else if (Boolean.TRUE.equals(isBeneficiaryArchived)) details.append("Bénéficiaires Archivé ");
        else if (Boolean.TRUE.equals(isCandidateRejete)) details.append("Candidates Rejeté");
        else if (Boolean.TRUE.equals(isBenefeiciaryWaiting)) details.append("Bénéficiaires En Attente ");
        else if (Boolean.TRUE.equals(isBeneficiaryActif)) details.append("Bénéficiaires Actif");

        // Add status details if applicable
        String detailsMessage = details.length() > 0 ? " " + details.toString().trim() : "";
        if (auditMessage.equals("Recherche par filtre dans liste des ")) {
            auditApplicationService.audit(auditMessage + detailsMessage, getUsernameFromJwt(), "Liste des bénéficiaires",
                    jsonSearchParams, null, BENEFICIAIRE, CONSULTATION);
        } else {
            auditApplicationService.audit(auditMessage + detailsMessage, getUsernameFromJwt(), "Liste des bénéficiaires",
                    null, null, BENEFICIAIRE, CONSULTATION);
        }

    }


    private List<GetListDTO> mapToDTOs(Page<Beneficiary> listBeneficiaries) {
        // Convert Iterable to Stream
        Iterable<GetListDTO> getListDTOIterable = beneficiaryMapper.beneficiaryToGetListDTO(listBeneficiaries);
        List<GetListDTO> beneficiaries = StreamSupport.stream(getListDTOIterable.spliterator(), false)
                .peek(dto -> {
                    // Assuming Beneficiary has a `getZone()` method that returns a Zone object.
                    Beneficiary beneficiary = beneficiaryRepository.findById(dto.getId()).orElseThrow(); // Implement this method to get full Beneficiary details if not available
                    if (beneficiary.getZone() != null) {
                        dto.setZoneCode(beneficiary.getZone().getCode());
                        dto.setZoneName(beneficiary.getZone().getName());
                        dto.setZoneNameAr(beneficiary.getZone().getNameAr());
                    }
                    if (beneficiary.getBeneficiaryStatut() != null) {
                        dto.setBeneficiaryStatutId(beneficiary.getBeneficiaryStatut().getId());
                    }
                    dto.setIdentityCode(beneficiary.getPerson().getIdentityCode());
                    if (Boolean.FALSE.equals(beneficiary.getIndependent())) {
                        FamilyMember familyMember = familyMemberRepository.findByPersonId(beneficiary.getPerson().getId()).orElseThrow();
                        List<FamilyMember> familyMemberList = familyMemberRepository.findAllByFamily(familyMember.getFamily());

                        dto.setFamilyPhoneNumber(familyMember.getFamily().getPhoneNumberFamily());

                        familyMemberList.stream()
                                .filter(FamilyMember::isTutor)
                                .findFirst()
                                .ifPresent(tutorMember -> {
                                    dto.setFamilyIdentityCode(tutorMember.getPerson().getIdentityCode());
                                });

                    }
                    List<Taggable> taggables = taggableRepository.findByTaggableIdAndTaggableType(beneficiary.getId(),"beneficiary");
                    List<TagDTO> tags = taggables.stream()
                            .map(taggable -> {
                                TagDTO tagDTO = new TagDTO();
                                tagDTO.setId(taggable.getTag().getId());
                                tagDTO.setName(taggable.getTag().getName());
                                tagDTO.setColor(taggable.getTag().getColor());
                                return tagDTO;
                            })
                            .collect(Collectors.toList());
                    dto.setTags(tags);
                })
                .toList();

        return beneficiaries.stream().map(this::enhanceBeneficiary).collect(Collectors.toList());
    }


    private GetListDTO enhanceBeneficiary(GetListDTO beneficiary) {
        if (beneficiary.getCityId() != null) {
            CityDTO cityDTO = refFeignClient.getParCity(beneficiary.getCityId());
            beneficiary.setCity(cityDTO.getName());
            beneficiary.setRegion(cityDTO.getRegion().getName());
        }
        if (!beneficiary.getServices().isEmpty()) {
            for (GetServiceDTO getServiceDTO : beneficiary.getServices()) {
                if (getServiceDTO.getServiceId() != null) {
                    ServiceDTO serviceDTO = refFeignClient.getMetService(getServiceDTO.getServiceId());
                    getServiceDTO.setService(serviceDTO.getName());
                    getServiceDTO.setCategoryId(serviceDTO.getCategory().getId());
                    getServiceDTO.setCategory(serviceDTO.getCategory().getName());
                }
                if (getServiceDTO.getStatusId() != null) {
                    StatusDTO statusDTO = refFeignClient.getParStatus(getServiceDTO.getStatusId());
                    getServiceDTO.setStatus(statusDTO.getName());
                }
            }
        }
        return beneficiary;
    }

    public Page<Beneficiary> filterBeneficiaries(List<Long> zoneIds, String searchByNom, String lastNameAr, Boolean searchByTypeBeneficiaire,
                                                 String searchByService, String searchByStatut, String searchByNumTel,
                                                 Date minDate, Date maxDate, Boolean isCandidate, Boolean isCandidateInitial, Boolean isValidateAssistant, Boolean isValidateKafalat,
                                                 Boolean isOldBeneficiary, Boolean isBeneficiaryRejete, Boolean isBeneficiaryArchived, Boolean isCandidateRejete,
                                                 Boolean isBenefeiciaryWaiting, Boolean isBeneficiaryActif,Long searchByTagId, Pageable pageable) {
        TypedQuery<Beneficiary> typedQuery = createBeneficiaryCriteriaQuery(zoneIds, searchByNom, lastNameAr, searchByTypeBeneficiaire,
                searchByService, searchByStatut, searchByNumTel, minDate, maxDate, isCandidate, isCandidateInitial, isValidateAssistant, isValidateKafalat, isOldBeneficiary, isBeneficiaryRejete, isBeneficiaryArchived, isCandidateRejete, isBenefeiciaryWaiting, isBeneficiaryActif,searchByTagId);
        long totalCount = typedQuery.getResultList().size();
        typedQuery.setFirstResult((int) pageable.getOffset());
        typedQuery.setMaxResults(pageable.getPageSize());
        List<Beneficiary> resultList = typedQuery.getResultList();
        return new PageImpl<>(resultList, pageable, totalCount);
    }


    public BeneficiaryDTO getBeneficiaryById(Long id) throws TechnicalException {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service Get Beneficiary by ID: {}", id);
        Optional<Beneficiary> beneficiary = beneficiaryRepository.findById(id);
        BeneficiaryDTO beneficiaryDTO = beneficiaryMapper.beneficiaryToBeneficiaryDTO(beneficiary.get());
        if (beneficiaryDTO.getTakenInChargeBeneficiaries() != null) {
            beneficiaryDTO.getTakenInChargeBeneficiaries().forEach(takenInChargeBeneficiaryDTO -> {
                if (takenInChargeBeneficiaryDTO.getTakenInCharge() != null && takenInChargeBeneficiaryDTO.getTakenInCharge().getId() != null) {
                    Optional<TakenInCharge> takenInCharge = takenInChargeRepository.findById(takenInChargeBeneficiaryDTO.getTakenInCharge().getId());
                    if (takenInCharge.isPresent()) {
                        ServicesDTO servicesDTO = servicesMapper.toDto(takenInCharge.get().getService());
                        takenInChargeBeneficiaryDTO.getTakenInCharge().setServices(servicesDTO);
                    }
                }
                {

                }
            });
        }
        if (!beneficiary.get().getIndependent()) {
            // Retrieve family ID
            Long familyId = beneficiary.get().getPerson().getFamilyMember().getFamily().getId();

            if (familyId != null && familyId != 0) {
                Optional<Family> familyOptional = familyRepository.findById(familyId);

                if (familyOptional.isPresent()) {
                    Family family = familyOptional.get();

                    // Set family tutor name
                    for (FamilyMember familyMember : family.getFamilyMembers()) {
                        if (familyMember.isTutor()) {
                            String tutorName = familyMember.getPerson().getLastName() + " " + familyMember.getPerson().getFirstName();
                            beneficiaryDTO.setTutorName(tutorName);
                            break; // Exit loop after finding the tutor
                        }
                    }

                    // Map family details to beneficiaryDTO
                    beneficiaryDTO.setAddressFamily(family.getAddressFamily());
                    beneficiaryDTO.setAddressFamilyAr(family.getAddressFamilyAr());
                    beneficiaryDTO.setPhoneNumberFamily(family.getPhoneNumberFamily());
                    beneficiaryDTO.setFamilyId(family.getId());
                    // get the accomodationTYpe and nature
                    if (family.getAccommodationTypeId() != null) {
                        AccommodationTypeDTO accommodationTypeDTO = refFeignClient.getMetAccommodationType(family.getAccommodationTypeId());
                        beneficiaryDTO.setAccommodationtypeFamily(accommodationTypeDTO.getName());

                    }

                    if (family.getAccommodationNatureId() != null) {
                        AccommodationNatureDTO accommodationNatureDTO = refFeignClient.getParAccommodationNature(family.getAccommodationNatureId());
                        beneficiaryDTO.setAccommodationNatureFamily(accommodationNatureDTO.getName());
                    }

                    if (family.getZone() != null && family.getZone().getId() != null) {
                        beneficiaryDTO.setZoneFamilyId(family.getZone().getId());
                        beneficiaryDTO.setZoneFamilyName(family.getZone().getName());
                        if (family.getSousZone() != null && family.getSousZone().getId() != null) {
                            beneficiaryDTO.setSousZoneFamilyId(family.getSousZone().getId());
                            beneficiaryDTO.setSousZoneFamilyName(family.getSousZone().getName());
                        }
                    }


                    // Set city, region, and country details
                    Long cityId = family.getCityId();
                    if (cityId != null && cityId != 0) {
                        CityDTO cityDTO = refFeignClient.getParCity(cityId);
                        CityWithRegionAndCountryDTO fullCountryDto = refController.getCityWithRegionAndCountry(cityId).getBody();
                        if (fullCountryDto != null) {
                            cityDTO.setRegion(fullCountryDto.getRegion());
                            beneficiaryDTO.setCityFamily(cityDTO);
                        }
                    }
                }
            }
        }


        //Setting BeneficiaryStatut info into statut field
        if (beneficiary.get().getBeneficiaryStatut() != null && beneficiary.get().getBeneficiaryStatut().getId() != null) {
            Optional<BeneficiaryStatut> beneficiaryStatut = beneficiaryStatutRepository.findById(beneficiary.get().getBeneficiaryStatut().getId());
            beneficiaryDTO.setStatut(beneficiaryStatut.get().getNameStatut());
        }
        //Setting the full city info
        CityDTO cityDTO = beneficiaryDTO.getPerson().getCity();
        if (cityDTO.getId() != null && cityDTO.getId() != 0) {
            CityDTO fullCityDTO = refFeignClient.getParCity(cityDTO.getId());
            beneficiaryDTO.getPerson().setCity(fullCityDTO);

            CityWithRegionAndCountryDTO fullCountryDto = refController.getCityWithRegionAndCountry(cityDTO.getId()).getBody();
            beneficiaryDTO.getPerson().setInfo(fullCountryDto);
        }
        //Setting the full type identity info
        TypeIdentityDTO typeIdentityDTO = beneficiaryDTO.getPerson().getTypeIdentity();
        if (typeIdentityDTO.getId() != null) {
            TypeIdentityDTO fullTypeIdentityDTO = refFeignClient.getParTypeIdentity(typeIdentityDTO.getId());
            beneficiaryDTO.getPerson().setTypeIdentity(fullTypeIdentityDTO);
        }
        //Setting the full school level info
        SchoolLevelDTO schoolLevelDTO = beneficiaryDTO.getPerson().getSchoolLevel();
        if (schoolLevelDTO.getId() != null) {
            SchoolLevelDTO fullSchoolLevelDTO = refFeignClient.getParSchoolLevel(schoolLevelDTO.getId());
            beneficiaryDTO.getPerson().setSchoolLevel(fullSchoolLevelDTO);
        }

        //Setting the full profession info
        ProfessionDTO professionDTO = beneficiaryDTO.getPerson().getProfession();
        if (professionDTO.getId() != null) {
            ProfessionDTO fullProfessionDTO = refFeignClient.getMetProfession(professionDTO.getId());
            beneficiaryDTO.getPerson().setProfession(fullProfessionDTO);
        }
        //Setting the full  AccommodationType info
        AccommodationTypeDTO accommodationTypeDTO = beneficiaryDTO.getPerson().getAccommodationType();
        if (accommodationTypeDTO.getId() != null) {
            AccommodationTypeDTO fullAccommodationTypeDTO = refFeignClient.getMetAccommodationType(accommodationTypeDTO.getId());
            beneficiaryDTO.getPerson().setAccommodationType(fullAccommodationTypeDTO);
        }
        // setting the full AccomodataionNature
        AccommodationNatureDTO accommodationNatureDTO = beneficiaryDTO.getPerson().getAccommodationNature();
        if (accommodationNatureDTO.getId() != null) {
            AccommodationNatureDTO fullAccommodationNatureDTO = refFeignClient.getParAccommodationNature(accommodationNatureDTO.getId());
            beneficiaryDTO.getPerson().setAccommodationNature(fullAccommodationNatureDTO);
        }

        CategoryBeneficiaryDTO categoryBeneficiary = beneficiaryDTO.getPerson().getCategoryBeneficiary();
        if (categoryBeneficiary.getId() != null) {
            CategoryBeneficiaryDTO fullCategoryBeneficiary = refFeignClient.getCategoryBeneficiary(categoryBeneficiary.getId());
            beneficiaryDTO.getPerson().setCategoryBeneficiary(fullCategoryBeneficiary);
        }

        //Setting the full  TypeKfafalat info
        TypeKafalatDTO typeKafalatDTO = beneficiaryDTO.getPerson().getTypeKafalat();
        if (typeKafalatDTO.getId() != null) {
            TypeKafalatDTO fullTypeKafalatDTO = refFeignClient.getParTypeKafalat(typeKafalatDTO.getId());
            beneficiaryDTO.getPerson().setTypeKafalat(fullTypeKafalatDTO);
        }

        SourceBeneficiaryDTO sourceBeneficiaryDTO = beneficiaryDTO.getPerson().getSourceBeneficiary();
        if (sourceBeneficiaryDTO.getId() != null) {
            SourceBeneficiaryDTO fullSourceBeneficiaryDTO = refFeignClient.getSourceBeneficiary(sourceBeneficiaryDTO.getId());
            beneficiaryDTO.getPerson().setSourceBeneficiary(fullSourceBeneficiaryDTO);
        }

        //Setting typePriseEnCharge full info
        if (beneficiary.get().getPerson().getTypePriseEnChargeIdsList() != null && !beneficiary.get().getPerson().getTypePriseEnChargeIdsList().isEmpty()) {
            String[] ids = beneficiary.get().getPerson().getTypePriseEnChargeIdsList().split(",");
            List<ServiceDTO> typePriseEnChargeDTOs = new ArrayList<>();
            List<Long> typePriseEnChargeIds = new ArrayList<>();
            for (String idStr : ids) {
                Long TpecId = Long.parseLong(idStr.trim());
                ServiceDTO typePriseEnChargeDTO = refFeignClient.getMetService(TpecId);
                if (typePriseEnChargeDTO != null) {
                    typePriseEnChargeDTOs.add(typePriseEnChargeDTO);
                    typePriseEnChargeIds.add(typePriseEnChargeDTO.getId());
                }
            }
            beneficiaryDTO.getPerson().setTypePriseEnCharges(typePriseEnChargeDTOs);
            beneficiaryDTO.getPerson().setTypePriseEnChargeIds(typePriseEnChargeIds);
        }

        //Setting allergies full info
        if (beneficiaryDTO.getAllergies() != null) {
            List<AllergiesDTO> allergyDTOS = beneficiaryDTO.getAllergies().stream().map((e) -> {
                AllergiesDTO allergyDTO = new AllergiesDTO();
                if (e.getId() != null) {
                    allergyDTO = refFeignClient.getMetAllergies(e.getId());
                }
                return allergyDTO;
            }).collect(Collectors.toList());
            beneficiaryDTO.setAllergies(allergyDTOS);
        }
        //Setting full diseases info
        if (beneficiaryDTO.getDiseases() != null) {
            List<DiseasesDTO> diseaseDTOS = beneficiaryDTO.getDiseases().stream().map((e) -> {
                DiseasesDTO diseaseDTO = new DiseasesDTO();
                if (e.getId() != null) {

                    diseaseDTO = refFeignClient.getMetDiseases(e.getId());

                }
                return diseaseDTO;
            }).collect(Collectors.toList());
            beneficiaryDTO.setDiseases(diseaseDTOS);
        }
        //Setting full handicap type info
        if (beneficiaryDTO.getHandicapped() != null) {
            Set<BeneficiaryHandicapDto> handicapDTOS = beneficiaryDTO.getHandicapped().stream().map((e) -> {
                HandicapTypeDTO handicapTypeDTO = e.getHandicapType();
                HandicapTypeDTO fullHandicapTypeDTO = new HandicapTypeDTO();
                if (handicapTypeDTO.getId() != null) {

                    fullHandicapTypeDTO = refFeignClient.getMetHandicapType(handicapTypeDTO.getId());

                    e.setHandicapType(fullHandicapTypeDTO);
                }
                return e;
            }).collect(Collectors.toSet());
            beneficiaryDTO.setHandicapped(handicapDTOS);
        }
        //Setting full services and statuses info
        if (beneficiaryDTO.getBeneficiaryServices() != null) {
            Set<BeneficiaryServiceDTO> beneficiaryServiceDTOS = beneficiaryDTO.getBeneficiaryServices().stream().map((e) -> {
                ServiceDTO serviceDTOs = e.getService();
                StatusDTO statusDTO = e.getStatus();
                ServiceDTO fullServiceDTO = new ServiceDTO();
                StatusDTO fullStatusDTO = new StatusDTO();
                if (serviceDTOs.getId() != null && statusDTO != null) {
                    fullServiceDTO = refFeignClient.getMetService(serviceDTOs.getId());
                    fullStatusDTO = refFeignClient.getParStatus(statusDTO.getId());

                    e.setService(fullServiceDTO);
                    e.setStatus(fullStatusDTO);
                }
                return e;
            }).collect(Collectors.toSet());
            beneficiaryDTO.setBeneficiaryServices(beneficiaryServiceDTOS);
        }
        //Setting the full education info
        if (beneficiaryDTO.getEducations() != null) {
            Set<EducationDTO> educationDTOS = beneficiaryDTO.getEducations().stream().map((e) -> {
                CityDTO cityDTO1 = e.getCity();
                SchoolYearDTO schoolYearDTO = e.getSchoolYear();
                SchoolLevelDTO schoolLevelDTO1 = e.getSchoolLevel();
                MajorDTO majorDTO = e.getMajor();
                HonorDTO honorDTO = e.getHonor();
                EducationSystemTypeDTO educationSystemTypeDTO = e.getEducationSystemType();
                CityDTO fullCityDTO = new CityDTO();
                SchoolLevelDTO fullSchoolLevelDTO = new SchoolLevelDTO();
                SchoolYearDTO fullSchoolYearDTO = new SchoolYearDTO();
                MajorDTO fullMajorDTO = new MajorDTO();
                HonorDTO fullHonorDTO = new HonorDTO();
                EducationSystemTypeDTO fullEducationSystemTypeDTO = new EducationSystemTypeDTO();
                if (cityDTO1.getId() != null && schoolLevelDTO1.getId() != null && schoolYearDTO.getId() != null && honorDTO.getId() != null && educationSystemTypeDTO.getId() != null) {

                    fullCityDTO = refFeignClient.getParCity(cityDTO1.getId());
                    fullSchoolYearDTO = refFeignClient.getParSchoolYear(schoolYearDTO.getId());
                    fullSchoolLevelDTO = refFeignClient.getParSchoolLevel(schoolLevelDTO1.getId());
                    fullHonorDTO = refFeignClient.getParHonor(honorDTO.getId());
                    fullEducationSystemTypeDTO = refFeignClient.getConsEducationSystemType(educationSystemTypeDTO.getId());


                    e.setCity(fullCityDTO);
                    e.setSchoolLevel(fullSchoolLevelDTO);
                    e.setSchoolYear(fullSchoolYearDTO);
                    e.setHonor(fullHonorDTO);
                    e.setEducationSystemType(fullEducationSystemTypeDTO);
                }
                if (majorDTO.getId() != null) {

                    fullMajorDTO = refFeignClient.getParMajor(majorDTO.getId());

                    e.setMajor(fullMajorDTO);
                }

                return e;
            }).collect(Collectors.toSet());
            beneficiaryDTO.setEducations(educationDTOS);
        }
        //Setting the full eps info
        if (!beneficiaryDTO.getEpsResidents().isEmpty()) {
            Set<EpsResidentDTO> epsResidentDTOS = beneficiaryDTO.getEpsResidents().stream().map((e) -> {
                EpsDTO epsDTO = e.getEps();
                EpsDTO fullEpsDTO = new EpsDTO();
                if (epsDTO.getId() != null) {

                    fullEpsDTO = refFeignClient.getMetEps(epsDTO.getId());

                    e.setEps(fullEpsDTO);
                }
                return e;
            }).collect(Collectors.toSet());
            beneficiaryDTO.setEpsResidents(epsResidentDTOS);
        }
        //Setting the full scholarship info
        if (!beneficiaryDTO.getScholarshipBeneficiaries().isEmpty()) {
            Set<ScholarshipBeneficiaryDTO> scholarshipBeneficiaryDTOS = beneficiaryDTO.getScholarshipBeneficiaries().stream().map((e) -> {
                ScholarshipDTO scholarshipDTO = e.getScholarship();
                ScholarshipDTO fullScholarshipDTO = new ScholarshipDTO();
                CurrencyDTO fullCurrencyDTO = new CurrencyDTO();
                if (scholarshipDTO.getId() != null) {
                    fullScholarshipDTO = refFeignClient.getMetScholarship(scholarshipDTO.getId());
                    e.setScholarship(fullScholarshipDTO);

                }
                if (e.getCurrency() != null && e.getCurrency().getId() != null) {

                    fullCurrencyDTO = refFeignClient.getParCurrency(scholarshipDTO.getId());
                    e.setCurrency(fullCurrencyDTO);

                }

                return e;
            }).collect(Collectors.toSet());
            beneficiaryDTO.setScholarshipBeneficiaries(scholarshipBeneficiaryDTOS);
        }
        //Setting the full disease treatment type info
        if (beneficiaryDTO.getDiseaseTreatments() != null) {
            Set<DiseaseTreatmentDTO> diseaseTreatmentDTOS = beneficiaryDTO.getDiseaseTreatments().stream().map((e) -> {
                DiseaseTreatmentTypeDTO diseaseTreatmentTypeDTO = e.getType();
                DiseaseTreatmentTypeDTO fullDiseaseTreatmentTypeDTO = new DiseaseTreatmentTypeDTO();
                if (diseaseTreatmentTypeDTO.getId() != null) {

                    fullDiseaseTreatmentTypeDTO = refFeignClient.getMetDiseaseTreatmentType(diseaseTreatmentTypeDTO.getId());

                    e.setType(fullDiseaseTreatmentTypeDTO);
                }
                return e;
            }).collect(Collectors.toSet());
            beneficiaryDTO.setDiseaseTreatments(diseaseTreatmentDTOS);
        }
        //Setting the full card type info
        if (beneficiaryDTO.getPerson().getBankCards() != null) {
            List<BankCardDTO> bankCardDTOS = beneficiaryDTO.getPerson().getBankCards().stream().map((e) -> {
                CardTypeDTO cardTypeDTO = e.getCardType();
                CardTypeDTO fullCardTypeDTO = new CardTypeDTO();
                if (cardTypeDTO.getId() != null) {

                    fullCardTypeDTO = refFeignClient.getConsCardType(cardTypeDTO.getId());

                    e.setCardType(fullCardTypeDTO);
                }
                return e;
            }).collect(Collectors.toList());
            beneficiaryDTO.getPerson().setBankCards(bankCardDTOS);
        }

        //Setting the full family member info
        if (beneficiaryDTO.getPerson().getFamilyMember() != null) {
            FamilyMemberDTO familyMemberDTO = beneficiaryDTO.getPerson().getFamilyMember();
            if (familyMemberDTO.getFamilyRelationship().getId() != null) {
                List<FamilyRelationshipDTO> familyRelationshipDTOS = refFeignClient.getAllMetFamilyRelationships();
                for (FamilyRelationshipDTO familyRelationshipDTO : familyRelationshipDTOS) {
                    if (familyMemberDTO.getFamilyRelationship().getId() == 1L && Objects.equals(familyRelationshipDTO.getName(), "Père")) {
                        familyMemberDTO.setFamilyRelationship(familyRelationshipDTO);
                    } else if (familyMemberDTO.getFamilyRelationship().getId() == 2L && Objects.equals(familyRelationshipDTO.getName(), "Mère")) {
                        familyMemberDTO.setFamilyRelationship(familyRelationshipDTO);
                    } else if (familyMemberDTO.getFamilyRelationship().getId().equals(familyRelationshipDTO.getId())) {
                        familyMemberDTO.setFamilyRelationship(familyRelationshipDTO);
                    }
                }
            }
            beneficiaryDTO.getPerson().setFamilyMember(familyMemberDTO);
        }
        //Get Beneficiary Picture
        if (beneficiary.get().getPerson().getPictureUrl() != null) {
            try {
                byte[] imageData = minioService.ReadFromMinIO(beneficiary.get().getPerson().getPictureUrl(), null);
                String base64Image = Base64.getEncoder().encodeToString(imageData);
                beneficiaryDTO.getPerson().setPictureBase64(base64Image);
            } catch (TechnicalException ex) {
                ex.printStackTrace();
            }
        }

        //Get Beneficiary Documents
//        List<DocumentBeneficiaryDTO> beneficiaryDocumentDTOS = beneficiaryDTO.getDocuments();
//        if (beneficiaryDocumentDTOS != null) {
//            for (DocumentBeneficiaryDTO beneficiaryDocumentDTO : beneficiaryDocumentDTOS) {
//                if (beneficiaryDocumentDTO.getDocument().getFileUrl() != null) {
//                    String base64 = Arrays.toString(minioService.ReadFromMinIO(beneficiaryDocumentDTO.getDocument().getFileUrl(),null));
//                    beneficiaryDocumentDTO.getDocument().setFile64(base64);
//                }
//            }
//        }
        //Setting Beneficiary taken in charge info
        if (!beneficiaryDTO.getTakenInChargeBeneficiaries().isEmpty()) {
            List<TakenInChargeBeneficiaryDTO> takenInChargeBeneficiaryDTOS = beneficiaryDTO.getTakenInChargeBeneficiaries().stream().map(e -> {
                if (e.getTakenInCharge().getServices() != null) {

                    Services services = servicesRepository.findById(e.getTakenInCharge().getServices().getId()).orElseThrow();
                    ServicesDTO servicesDTO = servicesMapper.toDto(services);
                    e.getTakenInCharge().setServices(servicesDTO);

                }

                if (!e.getTakenInCharge().getTakenInChargeDonors().isEmpty()) {
                    List<TakenInChargeDonorDTO> takenInChargeDonorDTOS = e.getTakenInCharge().getTakenInChargeDonors().stream().map(takenInChargeDonorDTO -> {

                        try {
                            DonorDTO donorDTO = donorService.getDonorById(takenInChargeDonorDTO.getDonor().getId());
                            takenInChargeDonorDTO.setDonor(donorDTO);
                        } catch (TechnicalException ex) {
                            log.warn("Donor with id '{}' not found !", takenInChargeDonorDTO.getDonor().getId());
                        }

                        return takenInChargeDonorDTO;
                    }).collect(Collectors.toList());
                    e.getTakenInCharge().setTakenInChargeDonors(takenInChargeDonorDTOS);
                }
                return e;
            }).collect(Collectors.toList());
            beneficiaryDTO.setTakenInChargeBeneficiaries(takenInChargeBeneficiaryDTOS);
        }

        List<Taggable> taggables=taggableRepository.findByTaggableIdAndTaggableType(beneficiaryDTO.getId(),"beneficiary");
        List<TagDTO> tags = new ArrayList<>();
        for (Taggable taggable : taggables) {
            TagDTO tagDTO = new TagDTO();
            tagDTO.setColor(taggable.getTag().getColor());
            tagDTO.setId(taggable.getTag().getId());
            tagDTO.setName(taggable.getTag().getName());
            tags.add(tagDTO);
        }
        beneficiaryDTO.setTags(tags);
        //Audit
        var newBeneficiaryDto = beneficiaryMapper.PersonDtoTomapBeneficiaryAddDTO(beneficiaryDTO.getPerson());
        newBeneficiaryDto.setId(beneficiaryDTO.getId());
        newBeneficiaryDto.setIndependent(beneficiaryDTO.getIndependent());
        newBeneficiaryDto.setEpsResident(beneficiaryDTO.getEpsResident());
        newBeneficiaryDto.setCode(beneficiaryDTO.getCode());
        newBeneficiaryDto.setAddedYear(beneficiaryDTO.getAddedYear() != null ? beneficiaryDTO.getAddedYear().substring(0, 4) : null);
        newBeneficiaryDto.setAccountingCode(beneficiaryDTO.getAccountingCode());
        newBeneficiaryDto.setCreatedAt(beneficiaryDTO.getCreatedAt());
        newBeneficiaryDto.setProfessionId(beneficiaryDTO.getPerson().getProfession().getId());
        newBeneficiaryDto.setAccommodationTypeId(beneficiaryDTO.getPerson().getAccommodationType().getId());
        newBeneficiaryDto.setCategoryBeneficiaryId(beneficiaryDTO.getPerson().getCategoryBeneficiary().getId());
        newBeneficiaryDto.setTypeIdentityId(beneficiaryDTO.getPerson().getTypeIdentity().getId());

        auditApplicationService.audit("Consultation du Bénéficiaire : " + beneficiary.get().getCode(), getUsernameFromJwt(), "Add Physical Donor",
                beneficiary.get().getAuditConsultation(null), null, BENEFICIAIRE, CONSULTATION);

        log.debug("End service Get Beneficiary by ID: {}, took {}", id, watch.toMS());
        return beneficiaryDTO;
    }


    @Transactional
    public void beneficiaryValidation(Long id) throws FunctionalException {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service beneficiaryValidation");

        Optional<Beneficiary> optionalBeneficiary = beneficiaryRepository.findById(id);

        if (optionalBeneficiary.isPresent()) {
            Beneficiary beneficiary = optionalBeneficiary.get();
            BeneficiaryStatut currentStatut = beneficiary.getBeneficiaryStatut();

//            if(cacheAdUser.getRole().getCode().equals(RoleCode.ASSISTANT.getCode())){
//                checkDocumentsForAssistantValidation(beneficiary);
//            }

            checkDocumentsForAssistantValidation(beneficiary);

            if (currentStatut != null && currentStatut.getId() != null) {
                BeneficiaryStatut newStatut = getBeneficiaryStatut(currentStatut);

                if (newStatut != null) {
                    if (newStatut.getId().equals(BeneficiaryStatus.BENEFICIAIRE_EN_ATTENTE.getId())) {
                        beneficiary.setLastUpdateDate(LocalDateTime.now());
                    }
                    BeneficiaryStatut beneficiaryStatut = beneficiaryStatutRepository.findById(beneficiary.getBeneficiaryStatut().getId()).get();
                    beneficiary.setBeneficiaryStatut(beneficiaryStatut);
                    newStatut.setNameStatut(getStatusNameById(newStatut.getId()));
                    beneficiary.setBeneficiaryStatut(newStatut);

                    auditApplicationService.audit("Validation de Bénéficiaire: " + beneficiary.getCode(), getUsernameFromJwt(), "Add Physical Donor",
                            beneficiary.getAuditConsultation(null), null, BENEFICIAIRE, VALIDATION);

                    beneficiaryRepository.save(beneficiary);

                    historyBeneficiaryService.saveHistoryBeneficiaryStatus(newStatut.getId(), beneficiary.getId(), getUsernameFromJwt());
                }

            }
        }

        log.debug("End service beneficiaryValidation, elapsed time: {}", watch.time());
    }


    private void checkDocumentsForAssistantValidation(Beneficiary beneficiary) throws FunctionalException {
        List<Long> requiredDocumentIds = new ArrayList<>(List.of(1L, 2L, 3L, 4L, 5L, 6L, 8L, 10L, 11L, 12L)); // IDs requis
        List<Long> missingDocumentIds = new ArrayList<>();

        if (beneficiary.getPerson() != null && !beneficiary.getPerson().isEducated()) {
            requiredDocumentIds.removeIf(id -> id.equals(5L) || id.equals(6L));
        }

        for (Long documentId : requiredDocumentIds) {
            if (!isDocumentPresent(beneficiary, documentId)) {
                missingDocumentIds.add(documentId);
            }
        }

        if (!missingDocumentIds.isEmpty()) {
            List<String> missingDocumentNames = new ArrayList<>();

            for (Long documentId : missingDocumentIds) {
                TypeDocumentDonorDTO typeDocumentDonorDTO = refFeignClient.getConsTypeDonorDocument(documentId);

                if (typeDocumentDonorDTO != null) {
                    missingDocumentNames.add(typeDocumentDonorDTO.getName());
                }
            }

            String missingDocumentsMessage = "Veuillez compléter les documents suivants avant de valider ce pré-candidat : "
                    + String.join(", ", missingDocumentNames);


            throw new FunctionalException(missingDocumentsMessage);
        }

    }

    private boolean isDocumentPresent(Beneficiary beneficiary, Long documentId) {
        List<DocumentBeneficiary> documentBeneficiaryList = documentBeneficiaryRepository.findByBeneficiaryId(beneficiary.getId());

        if (Boolean.FALSE.equals(beneficiary.getIndependent())) {
            Person person = personRepository.findById(beneficiary.getPerson().getId()).orElseThrow();
            FamilyMember familyMember = familyMemberRepository.findByPersonId(person.getId()).orElseThrow();
            List<DocumentFamily> documentFamilies = familyDocumentRepository.findByFamilyId(familyMember.getFamily().getId());

            boolean familyDocumentPresent = documentFamilies.stream()
                    .anyMatch(documentFamily ->
                            documentFamily.getDocument() != null
                                    && documentFamily.getDocument().getTypeDocumentId() != null &&
                                    documentFamily.getDocument().getTypeDocumentId().equals(documentId) &&
                                    (documentFamily.getDocument().getExpiryDate() == null ||
                                            documentFamily.getDocument().getExpiryDate().after(new Date()))
                    );

            if (familyDocumentPresent) {
                return true;
            }
        }

        return documentBeneficiaryList.stream()
                .anyMatch(documentBeneficiary ->
                        documentBeneficiary.getDocument() != null && documentBeneficiary.getDocument().getTypeDocumentId() != null &&
                                documentBeneficiary.getDocument().getTypeDocumentId().equals(documentId) &&
                                (documentBeneficiary.getDocument().getExpiryDate() == null ||
                                        documentBeneficiary.getDocument().getExpiryDate().after(new Date()))
                );
    }


    public Long candidateToComplete(Long idBeneficiary, Long idStatutTarget, String rqComplete) {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service candidateToComplete");

        Beneficiary beneficiary = beneficiaryRepository.findById(idBeneficiary)
                .orElseThrow(() -> new RuntimeException("Beneficiary not found"));

        final Optional<Assistant> assistant;
        final String assistantEmail;
        if (beneficiary.getZone() != null && beneficiary.getZone().getAssistant() != null) {
            assistant = assistantRepository.findById(beneficiary.getZone().getAssistant().getId());
            assistantEmail = assistant.map(Assistant::getEmail).orElse("<EMAIL>");
        } else {
            assistantEmail = "<EMAIL>";

        }
        final String username = getUsernameFromJwt();
        final String rqCompleteFinal = rqComplete; // Make sure this is effectively final

        if (idStatutTarget != null) {
            if (idStatutTarget.equals(BeneficiaryStatus.CANDIDAT_A_COMPLETER_PAR_ASSISTANCE.getId())) {
                BeneficiaryStatut beneficiaryStatut = new BeneficiaryStatut(idStatutTarget);
                beneficiary.setBeneficiaryStatut(beneficiaryStatut);
                executorService.submit(() -> {
                    try {
                        final String serviceKafalatEmail = "<EMAIL>";
                        log.debug("Sending email to assistant: {}", assistantEmail);
                        mailSenderService.sendCompletionEmail(null, beneficiary, rqCompleteFinal, "Candidat à compléter par l'assistant", assistantEmail, "Marketing", username);
                        mailSenderService.sendCompletionEmail(null, beneficiary, rqCompleteFinal, "Candidat à compléter par service Kafalat", serviceKafalatEmail, "Marketing", username);
                    } catch (Exception e) {
                        log.error("Error sending email to assistant: {}", assistantEmail, e);
                    }
                });
                //historyBeneficiaryService.saveHistoryBeneficiaryStatus(idStatutTarget, beneficiary.getId(), username);
            } else if (idStatutTarget.equals(BeneficiaryStatus.CANDIDAT_A_COMPLETER_PAR_KAFALAT.getId())) {
                BeneficiaryStatut beneficiaryStatut = new BeneficiaryStatut(idStatutTarget);
                beneficiary.setBeneficiaryStatut(beneficiaryStatut);
                final String serviceKafalatEmail = "<EMAIL>";
                executorService.submit(() -> {
                    try {
                        log.debug("Sending email to Kafalat: {}", serviceKafalatEmail);
                        mailSenderService.sendCompletionEmail(null, beneficiary, rqCompleteFinal, "Candidat à compléter par service Kafalat", serviceKafalatEmail, "Marketing", username);
                    } catch (Exception e) {
                        log.error("Error sending email to Kafalat: {}", serviceKafalatEmail, e);
                    }
                });
                //historyBeneficiaryService.saveHistoryBeneficiaryStatus(idStatutTarget, beneficiary.getId(), username);
            }
        } else {
            BeneficiaryStatut beneficiaryStatut = new BeneficiaryStatut(BeneficiaryStatus.CANDIDAT_A_COMPLETER_PAR_ASSISTANCE.getId());
            beneficiary.setBeneficiaryStatut(beneficiaryStatut);
            executorService.submit(() -> {
                try {
                    log.debug("Sending email to assistant: {}", assistantEmail);
                    mailSenderService.sendCompletionEmail(null, beneficiary, rqCompleteFinal, "Candidat à compléter par l'assistant", assistantEmail, "Kafalat", username);
                } catch (Exception e) {
                    log.error("Error sending email to assistant: {}", assistantEmail, e);
                }
            });
            //historyBeneficiaryService.saveHistoryBeneficiaryStatus(BeneficiaryStatus.CANDIDAT_A_COMPLETER_PAR_ASSISTANCE.getId(), beneficiary.getId(), getUsernameFromJwt());
        }

        if (rqComplete != null) {
            beneficiary.setRqComplete(rqComplete);
        }


        Beneficiary updatedBeneficiary = beneficiaryRepository.save(beneficiary);
        BeneficiaryStatut beneficiaryStatut = beneficiaryStatutRepository.findById(beneficiary.getBeneficiaryStatut().getId()).get();
        updatedBeneficiary.setBeneficiaryStatut(beneficiaryStatut);
        auditApplicationService.audit("A Complete de Bénéficiaire: " + updatedBeneficiary.getCode(), getUsernameFromJwt(), "Add Physical Donor",
                updatedBeneficiary.getAuditConsultation(rqComplete), null, BENEFICIAIRE, VALIDATION);
        if (idStatutTarget != null) {
            historyBeneficiaryService.saveHistoryBeneficiaryStatus(idStatutTarget, beneficiary.getId(), username);
        } else {
            historyBeneficiaryService.saveHistoryBeneficiaryStatus(BeneficiaryStatus.CANDIDAT_A_COMPLETER_PAR_ASSISTANCE.getId(), beneficiary.getId(), getUsernameFromJwt());
        }

        log.debug("End service candidateToComplete, elapsed time: {}", watch.time());
        return beneficiary.getBeneficiaryStatut() != null ? beneficiary.getBeneficiaryStatut().getId() : null;
    }

    @Transactional
    public Long validateUpdateCandidate(Long idBeneficiary) throws FunctionalException {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service validateUpdateCandidate");

        Beneficiary beneficiary = beneficiaryRepository.findById(idBeneficiary)
                .orElseThrow(() -> new RuntimeException("Beneficiary not found"));

        if (beneficiary.getBeneficiaryStatut().getId().equals(BeneficiaryStatus.CANDIDAT_A_UPDATER.getId())) {
            BeneficiaryStatut beneficiaryStatut = new BeneficiaryStatut(BeneficiaryStatus.BENEFICIAIRE_EN_ATTENTE.getId());
            beneficiary.setBeneficiaryStatut(beneficiaryStatut);
            beneficiary.setLastUpdateDate(LocalDateTime.now());
            beneficiaryRepository.save(beneficiary);
            historyBeneficiaryService.saveHistoryBeneficiaryStatus(BeneficiaryStatus.CANDIDAT_A_UPDATER.getId(), beneficiary.getId(), getUsernameFromJwt());
        } else {
            throw new FunctionalException("Candidat avec statut incorrect");
        }


        log.debug("End service validateUpdateCandidate, elapsed time: {}", watch.time());
        return idBeneficiary;
    }


    @Transactional
    public Long rejectBeneficiary(Long idBeneficiary, String rqReject) throws TechnicalException {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service rejectBeneficiary");

        Beneficiary beneficiary = beneficiaryRepository.findById(idBeneficiary)
                .orElseThrow(() -> new RuntimeException("Beneficiary not found"));

        Long currentStatusId = beneficiary.getBeneficiaryStatut().getId();
        Long newStatusId;

        if (currentStatusId.equals(BeneficiaryStatus.CANDIDAT_INITIAL.getId()) ||
                currentStatusId.equals(BeneficiaryStatus.CANDIDAT_A_COMPLETER_PAR_ASSISTANCE.getId()) ||
                currentStatusId.equals(BeneficiaryStatus.CANDIDAT_A_COMPLETER_PAR_KAFALAT.getId()) ||
                currentStatusId.equals(BeneficiaryStatus.CANDIDAT_VALIDER_ASSISTANCE.getId()) ||
                currentStatusId.equals(BeneficiaryStatus.CANDIDAT_VALIDER_KAFALAT.getId())) {

            newStatusId = BeneficiaryStatus.CANDIDAT_REJETE.getId();
        } else {
            if (!beneficiary.getTakenInChargeBeneficiaries().isEmpty()) {
                throw new TechnicalException("Impossible d'archiver ce bénéficiaire, il est en prise en charge");
            }
            newStatusId = BeneficiaryStatus.BENEFICIAIRE_REJETE.getId();
        }

        BeneficiaryStatut beneficiaryStatut = new BeneficiaryStatut(newStatusId);
        beneficiary.setBeneficiaryStatut(beneficiaryStatut);

        // Save the rejection reason if provided
        if (rqReject != null) {
            beneficiary.setRqReject(rqReject);
        }

        Beneficiary updatedBeneficiary = beneficiaryRepository.save(beneficiary);
        BeneficiaryStatut beneficiaryStatut1 = beneficiaryStatutRepository.findById(beneficiary.getBeneficiaryStatut().getId()).get();
        updatedBeneficiary.setBeneficiaryStatut(beneficiaryStatut1);
        auditApplicationService.audit("rejection du bénéficiare :" + updatedBeneficiary.getCode(), getUsernameFromJwt(), "Delete Beneficiary",
                updatedBeneficiary.getAuditConsultation(rqReject), null, BENEFICIAIRE, VALIDATION);
        historyBeneficiaryService.saveHistoryBeneficiaryStatus(newStatusId, beneficiary.getId(), getUsernameFromJwt());

        log.debug("End service rejectBeneficiary, elapsed time: {}", watch.time());
        return newStatusId;
    }

    @Nullable
    private static BeneficiaryStatut getBeneficiaryStatut(BeneficiaryStatut currentStatut) {
        BeneficiaryStatut newStatut = null;
        if (currentStatut.getId().equals(BeneficiaryStatus.CANDIDAT_INITIAL.getId()) || currentStatut.getId().equals(BeneficiaryStatus.CANDIDAT_A_COMPLETER_PAR_ASSISTANCE.getId())) {
            newStatut = new BeneficiaryStatut(BeneficiaryStatus.CANDIDAT_VALIDER_ASSISTANCE.getId());
        } else if (currentStatut.getId().equals(BeneficiaryStatus.CANDIDAT_VALIDER_ASSISTANCE.getId()) || currentStatut.getId().equals(BeneficiaryStatus.CANDIDAT_A_COMPLETER_PAR_KAFALAT.getId())) {
            newStatut = new BeneficiaryStatut(BeneficiaryStatus.CANDIDAT_VALIDER_KAFALAT.getId());
        } else if (currentStatut.getId().equals(BeneficiaryStatus.CANDIDAT_VALIDER_KAFALAT.getId())) {
            newStatut = new BeneficiaryStatut(BeneficiaryStatus.BENEFICIAIRE_EN_ATTENTE.getId());
        }
        return newStatut;
    }

    private String getStatusNameById(Long statusId) {
        return beneficiaryStatutRepository.findById(statusId)
                .map(BeneficiaryStatut::getNameStatut)
                .orElseThrow(() -> new IllegalArgumentException("Unknown status ID: " + statusId));
    }


    public void deleteBeneficiary(Long id, String rqReject) throws TechnicalException {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service delete Beneficiary");
        Optional<Beneficiary> beneficiary = beneficiaryRepository.findById(id);
        if (beneficiary.isPresent()) {
            // shoudl first check if the beneficiary is on a taken in charge if yes the holeoperation should not be done adn arere shouldbe send like impossible to delete this beneficiary

            // we should see his status if it is a candidate we can delete it
            //if not mean is a beneficiary we should archive it just the status
            Beneficiary deletedBeneficiary = beneficiary.get();
            if (!Objects.equals(beneficiary.get().getBeneficiaryStatut().getId(), BeneficiaryStatus.BENEFICIAIRE_EN_ATTENTE.getId()) &&
                    !Objects.equals(beneficiary.get().getBeneficiaryStatut().getId(), BeneficiaryStatus.BENEFICIAIRE_ACTIF.getId())) {
                deletedBeneficiary.setArchived(true);
            } else {
                if (!beneficiary.get().getTakenInChargeBeneficiaries().stream().filter(takenInChargeBeneficiary -> !Objects.equals(takenInChargeBeneficiary.getTakenInCharge().getStatus(), "Fermé")).toList().isEmpty()) {
                    throw new TechnicalException("Impossible d'archiver ce bénéficiaire, il est en prise en charge");
                }
                deletedBeneficiary.setBeneficiaryStatut(new BeneficiaryStatut(BeneficiaryStatus.BENEFICIAIRE_ANCIEN.getId()));
                deletedBeneficiary.setRqReject(rqReject);
                historyBeneficiaryService.saveHistoryBeneficiaryStatus(BeneficiaryStatus.BENEFICIAIRE_ANCIEN.getId(), deletedBeneficiary.getId(), getUsernameFromJwt());
            }
            if (!Objects.equals(rqReject, "null")) {
                BeneficiaryStatut beneficiaryStatut1 = beneficiaryStatutRepository.findById(deletedBeneficiary.getBeneficiaryStatut().getId()).get();
                deletedBeneficiary.setBeneficiaryStatut(beneficiaryStatut1);
                auditApplicationService.audit("Archivage du bénéficiaire : " + deletedBeneficiary.getCode(), getUsernameFromJwt(), "Delete Beneficiary",
                        deletedBeneficiary.getAuditConsultation(rqReject), null, BENEFICIAIRE, VALIDATION);
            } else {
                auditApplicationService.audit("Suppression du bénéficiaire : " + deletedBeneficiary.getCode(), getUsernameFromJwt(), "Delete Beneficiary",
                        deletedBeneficiary.getAuditConsultation(null), null, BENEFICIAIRE, DELETE);
            }


            log.debug("End service delete Beneficiary by ID: {}, took {}", id, watch.toMS());
            beneficiaryRepository.save(deletedBeneficiary);
        }
    }

    public void unArchiveBeneficiary(Long id) throws TechnicalException {
        TimeWatch watch = TimeWatch.start();
        log.debug("End service unArchive Beneficiary by ID: {}, took {}", id, watch.toMS());

        Beneficiary beneficiary = beneficiaryRepository.findById(id).orElseThrow();

        if (beneficiary.getBeneficiaryStatut().getId().equals(BeneficiaryStatus.BENEFICIAIRE_ANCIEN.getId()) || beneficiary.getBeneficiaryStatut().getId().equals(BeneficiaryStatus.CANDIDAT_REJETE.getId()) || beneficiary.getBeneficiaryStatut().getId().equals(BeneficiaryStatus.BENEFICIAIRE_REJETE.getId())) {
            beneficiary.setBeneficiaryStatut(new BeneficiaryStatut(BeneficiaryStatus.CANDIDAT_INITIAL.getId()));
            beneficiaryRepository.save(beneficiary);
            BeneficiaryStatut beneficiaryStatut1 = beneficiaryStatutRepository.findById(beneficiary.getBeneficiaryStatut().getId()).get();
            beneficiary.setBeneficiaryStatut(beneficiaryStatut1);
            auditApplicationService.audit("Restaurer du bénéficiare :" + beneficiary.getCode(), getUsernameFromJwt(), "Delete Beneficiary",
                    beneficiary.getAuditConsultation(null), null, BENEFICIAIRE, VALIDATION);
            historyBeneficiaryService.saveHistoryBeneficiary("Personne restaurée", null, beneficiary.getId(), getUsernameFromJwt());
        }


        log.debug("End service unArchive Beneficiary by ID: {}, took {}", id, watch.toMS());
    }

    public Page<GetBeneficiariesForTakeInchargeDTO> loadBeneficiariesForTakeInCharge(
            Optional<Integer> page,
            Boolean independent,
            String lastName,
            Long status,
            Long category
    ) {
        Pageable pageable = createPageable(page);
        List<Long> statusIds = new ArrayList<>();
        // Define the list of statuses you want to filter
        if (status == null) {
            statusIds = List.of(
                    BeneficiaryStatus.BENEFICIAIRE_ACTIF.getId(),
                    BeneficiaryStatus.BENEFICIAIRE_EN_ATTENTE.getId()
            );
        } else if (status.equals(BeneficiaryStatus.BENEFICIAIRE_ACTIF.getId())) {
            statusIds = List.of(
                    BeneficiaryStatus.BENEFICIAIRE_ACTIF.getId()
            );
        } else if (status.equals(BeneficiaryStatus.BENEFICIAIRE_EN_ATTENTE.getId())) {
            statusIds = List.of(
                    BeneficiaryStatus.BENEFICIAIRE_EN_ATTENTE.getId()
            );
        }
        // Fetch beneficiaries using dynamic filtering
        Page<Beneficiary> beneficiariesPage = fetchBeneficiariesWithFilters(
                statusIds, independent, lastName, category, pageable
        );

        // Map each Beneficiary to GetListDTO and apply the picture treatment
        return beneficiariesPage.map(beneficiary -> {
            GetBeneficiariesForTakeInchargeDTO dto = beneficiaryMapper.toGetBeneficiariesForTakeInchargeDTO(beneficiary);

            // Apply picture treatment if pictureUrl is present
            if (beneficiary.getPerson().getPictureUrl() != null) {
                try {
                    byte[] imageData = minioService.ReadFromMinIO(beneficiary.getPerson().getPictureUrl(), null);
                    String base64Image = Base64.getEncoder().encodeToString(imageData);
                    dto.setPictureBase64(base64Image);
                } catch (TechnicalException ex) {
                    ex.printStackTrace();
                    // Handle exception or log the error as needed
                }
            }

            // Set additional fields
            if (dto.getBeneficiaryStatutId() != null) {
                dto.setBeneficiaryStatut(
                        beneficiaryStatutRepository.findById(dto.getBeneficiaryStatutId()).get().getNameStatut()
                );
            }
            if (dto.getCategoryBeneficiaryId() != null) {
                dto.setCategoryBeneficiaryName(refFeignClient.getCategoryBeneficiary(dto.getCategoryBeneficiaryId()).getName());
            }
            if (dto.getId() != null) {
                dto.setNumberOfTakenInCharge(takenInChargeBeneficiaryRepository.countByBeneficiaryId(dto.getId()));
            }

            return dto;
        });
    }

    private Page<Beneficiary> fetchBeneficiariesWithFilters(
            List<Long> statusIds,
            Boolean independent,
            String lastName,
            Long category,
            Pageable pageable
    ) {
        List<Predicate> predicates = new ArrayList<>();
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Beneficiary> query = cb.createQuery(Beneficiary.class);
        Root<Beneficiary> root = query.from(Beneficiary.class);

        predicates.add(cb.isFalse(root.get("archived")));
        Join<Beneficiary, Person> personJoin = root.join("person");
        predicates.add(root.get("beneficiaryStatut").get("id").in(statusIds));
        if (category != null) {
            predicates.add(cb.equal(personJoin.get("categoryBeneficiaryId"), category));
        }
        if (lastName != null && !lastName.isEmpty()) {
            // Split the input if it contains both first name and last name
            String[] nameParts = lastName.split(" ", 2); // Split by space (assuming the user enters both names)

            Predicate namePredicate = cb.conjunction(); // This will combine all conditions

            // If the name contains both first and last name parts
            if (nameParts.length > 1) {
                // Try firstName lastName first
                Predicate firstLastNamePredicate = cb.and(
                        cb.like(cb.lower(personJoin.get("firstName")), "%" + nameParts[0].toLowerCase() + "%"),
                        cb.like(cb.lower(personJoin.get("lastName")), "%" + nameParts[1].toLowerCase() + "%")
                );

                // Then try lastName firstName
                Predicate lastFirstNamePredicate = cb.and(
                        cb.like(cb.lower(personJoin.get("firstName")), "%" + nameParts[1].toLowerCase() + "%"),
                        cb.like(cb.lower(personJoin.get("lastName")), "%" + nameParts[0].toLowerCase() + "%")
                );

                // Combine both cases (first-last and last-first)
                namePredicate = cb.and(namePredicate, cb.or(firstLastNamePredicate, lastFirstNamePredicate));
            } else if (nameParts.length == 1) {
                // If only one name part, check if it could be first name or last name
                namePredicate = cb.and(namePredicate, cb.or(
                        cb.like(cb.lower(personJoin.get("firstName")), "%" + nameParts[0].toLowerCase() + "%"),
                        cb.like(cb.lower(personJoin.get("lastName")), "%" + nameParts[0].toLowerCase() + "%")
                ));
            }

            // Add the namePredicate to the overall list of predicates
            predicates.add(namePredicate);
        }

        // Filter by independent
        if (independent != null) {
            predicates.add(cb.equal(root.get("independent"), independent));
        }

        // Filter by lastName (case-insensitive, partial match)

        query.where(cb.and(predicates.toArray(new Predicate[0])));


        // Create and execute the query with pagination
        TypedQuery<Beneficiary> typedQuery = entityManager.createQuery(query);
        long totalCount = typedQuery.getResultList().size();
        typedQuery.setFirstResult((int) pageable.getOffset());
        typedQuery.setMaxResults(pageable.getPageSize());
        List<Beneficiary> beneficiaries = typedQuery.getResultList();

        return new PageImpl<>(beneficiaries, pageable, totalCount);
    }


    private TypedQuery<Beneficiary> createBeneficiaryCriteriaQuery(
            List<Long> zoneIds,
            String searchByNom,
            String lastNameAr,
            Boolean searchByTypeBeneficiaire,
            String searchByService,
            String searchByStatut,
            String searchByNumTel,
            Date minDate,
            Date maxDate,
            Boolean isCandidate, Boolean isCandidateInitial, Boolean isValidateAssistant, Boolean isValidateKafalat,
            Boolean isOldBeneficiary, Boolean isBeneficiaryRejete, Boolean isBeneficiaryArchived, Boolean isCandidateRejete, Boolean isBenefeiciaryWaiting, Boolean isBeneficiaryActif,Long searchByTagId) {

        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Beneficiary> criteriaQuery = criteriaBuilder.createQuery(Beneficiary.class);
        Root<Beneficiary> root = criteriaQuery.from(Beneficiary.class);

        Predicate predicate = buildPredicate(zoneIds, criteriaBuilder, root, searchByNom, lastNameAr, searchByTypeBeneficiaire,
                searchByService, searchByStatut, searchByNumTel, minDate, maxDate, isCandidate, isCandidateInitial, isValidateAssistant, isValidateKafalat, isOldBeneficiary, isBeneficiaryRejete, isBeneficiaryArchived, isCandidateRejete, isBenefeiciaryWaiting, isBeneficiaryActif,searchByTagId);

        criteriaQuery.where(predicate);
        criteriaQuery.orderBy(criteriaBuilder.desc(root.get("createdAt")));

        return entityManager.createQuery(criteriaQuery);
    }

    public Predicate buildPredicate(List<Long> zoneIds, CriteriaBuilder criteriaBuilder, Root<Beneficiary> root,
                                    String searchByNom, String lastNameAr, Boolean searchByTypeBeneficiaire,
                                    String searchByService, String searchByStatut, String searchByNumTel,
                                    Date minDate, Date maxDate, Boolean isCandidate, Boolean isCandidateInitial, Boolean isValidateAssistant,
                                    Boolean isValidateKafalat, Boolean isOldBeneficiary, Boolean isBeneficiaryRejete, Boolean isBeneficiaryArchived,
                                    Boolean isCandidateRejete, Boolean isBenefeiciaryWaiting, Boolean isBeneficiaryActif,Long searchByTagId) {

        List<Predicate> predicates = new ArrayList<>();
        predicates.add(criteriaBuilder.isFalse(root.get("archived")));


        Join<Beneficiary, Person> personJoin = root.join("person");
        //Join<Beneficiary, BeneficiaryService> serviceJoin = root.join("beneficiaryServices");

        if (zoneIds != null && !zoneIds.isEmpty()) {
            predicates.add(root.get("zone").get("id").in(zoneIds));
        }


        if (searchByTagId != null) {
            // Create a subquery to find donors with the specified tag
            Subquery<Long> subquery = criteriaBuilder.createQuery().subquery(Long.class);
            Root<Taggable> taggableRoot = subquery.from(Taggable.class);
            subquery.select(taggableRoot.get("taggableId"))
                    .where(
                            criteriaBuilder.and(
                                    criteriaBuilder.equal(taggableRoot.get("taggableType"), "beneficiary"),
                                    criteriaBuilder.equal(taggableRoot.get("tag").get("id"), searchByTagId)
                            )
                    );

            // Add the subquery condition to the main predicate
            predicates.add(criteriaBuilder.in(root.get("id")).value(subquery));
        }

        if (searchByNom != null && !searchByNom.isEmpty()) {
            String[] nameParts = searchByNom.split(" ", 2);
            Predicate namePredicate = criteriaBuilder.conjunction();

            // Search in beneficiary's name
            if (nameParts.length > 1) {
                Predicate firstLastNamePredicate = criteriaBuilder.and(
                        criteriaBuilder.like(criteriaBuilder.lower(personJoin.get("firstName")), "%" + nameParts[0].toLowerCase() + "%"),
                        criteriaBuilder.like(criteriaBuilder.lower(personJoin.get("lastName")), "%" + nameParts[1].toLowerCase() + "%")
                );

                Predicate lastFirstNamePredicate = criteriaBuilder.and(
                        criteriaBuilder.like(criteriaBuilder.lower(personJoin.get("firstName")), "%" + nameParts[1].toLowerCase() + "%"),
                        criteriaBuilder.like(criteriaBuilder.lower(personJoin.get("lastName")), "%" + nameParts[0].toLowerCase() + "%")
                );

                namePredicate = criteriaBuilder.or(firstLastNamePredicate, lastFirstNamePredicate);
            } else if (nameParts.length == 1) {
                namePredicate = criteriaBuilder.or(
                        criteriaBuilder.like(criteriaBuilder.lower(personJoin.get("firstName")), "%" + nameParts[0].toLowerCase() + "%"),
                        criteriaBuilder.like(criteriaBuilder.lower(personJoin.get("lastName")), "%" + nameParts[0].toLowerCase() + "%")
                );
            }

            // Search in tutor's name for non-independent beneficiaries
            Join<Beneficiary, FamilyMember> familyMemberJoin = root.join("person", JoinType.LEFT).join("familyMember", JoinType.LEFT);
            Join<FamilyMember, Family> familyJoin = familyMemberJoin.join("family", JoinType.LEFT);
            Join<Family, FamilyMember> tutorJoin = familyJoin.join("familyMembers", JoinType.LEFT);
            Join<FamilyMember, Person> tutorPersonJoin = tutorJoin.join("person", JoinType.LEFT);

            Predicate tutorNamePredicate = criteriaBuilder.and(
                    criteriaBuilder.isFalse(root.get("independent")),
                    criteriaBuilder.isTrue(tutorJoin.get("tutor"))
            );

            if (nameParts.length > 1) {
                Predicate tutorFirstLastNamePredicate = criteriaBuilder.and(
                        criteriaBuilder.like(criteriaBuilder.lower(tutorPersonJoin.get("firstName")), "%" + nameParts[0].toLowerCase() + "%"),
                        criteriaBuilder.like(criteriaBuilder.lower(tutorPersonJoin.get("lastName")), "%" + nameParts[1].toLowerCase() + "%")
                );

                Predicate tutorLastFirstNamePredicate = criteriaBuilder.and(
                        criteriaBuilder.like(criteriaBuilder.lower(tutorPersonJoin.get("firstName")), "%" + nameParts[1].toLowerCase() + "%"),
                        criteriaBuilder.like(criteriaBuilder.lower(tutorPersonJoin.get("lastName")), "%" + nameParts[0].toLowerCase() + "%")
                );

                tutorNamePredicate = criteriaBuilder.and(tutorNamePredicate, criteriaBuilder.or(tutorFirstLastNamePredicate, tutorLastFirstNamePredicate));
            } else if (nameParts.length == 1) {
                Predicate tutorSingleNamePredicate = criteriaBuilder.or(
                        criteriaBuilder.like(criteriaBuilder.lower(tutorPersonJoin.get("firstName")), "%" + nameParts[0].toLowerCase() + "%"),
                        criteriaBuilder.like(criteriaBuilder.lower(tutorPersonJoin.get("lastName")), "%" + nameParts[0].toLowerCase() + "%")
                );
                tutorNamePredicate = criteriaBuilder.and(tutorNamePredicate, tutorSingleNamePredicate);
            }

            predicates.add(criteriaBuilder.or(namePredicate, tutorNamePredicate));
        }

        if (searchByNumTel != null && !searchByNumTel.isEmpty()) {
            // Search in beneficiary's phone number
            Predicate beneficiaryPhonePredicate = criteriaBuilder.like(personJoin.get("phoneNumber"), "%" + searchByNumTel + "%");

            // Search in family's phone number for non-independent beneficiaries
            Join<Beneficiary, FamilyMember> familyMemberJoin = root.join("person", JoinType.LEFT).join("familyMember", JoinType.LEFT);
            Join<FamilyMember, Family> familyJoin = familyMemberJoin.join("family", JoinType.LEFT);
            Predicate familyPhonePredicate = criteriaBuilder.and(
                    criteriaBuilder.isFalse(root.get("independent")),
                    criteriaBuilder.like(familyJoin.get("phoneNumberFamily"), "%" + searchByNumTel + "%")
            );

            predicates.add(criteriaBuilder.or(beneficiaryPhonePredicate, familyPhonePredicate));
        }

        if (searchByTypeBeneficiaire != null) {
            predicates.add(criteriaBuilder.equal(root.get("independent"), searchByTypeBeneficiaire));
        }

//        if (searchByService != null) {
//            predicates.add(criteriaBuilder.equal(serviceJoin.get("serviceId"), searchByService));
//        }
//
//        if (searchByStatut != null) {
//            predicates.add(criteriaBuilder.equal(serviceJoin.get("statusId"), searchByStatut));
//        }

        if (minDate != null) {
            predicates.add(criteriaBuilder.greaterThanOrEqualTo(personJoin.get("birthDate"), minDate));
        }

        if (maxDate != null) {
            predicates.add(criteriaBuilder.lessThanOrEqualTo(personJoin.get("birthDate"), maxDate));
        }


        // Conditions liées au statut du candidat
        if (Boolean.TRUE.equals(isCandidate)) {
            if (Boolean.TRUE.equals(isCandidateInitial)) {
                predicates.add(root.get("beneficiaryStatut").get("id").in(1, 4));
            } else if (Boolean.TRUE.equals(isValidateAssistant)) {
                predicates.add(root.get("beneficiaryStatut").get("id").in(2, 8));
            } else if (Boolean.TRUE.equals(isValidateKafalat)) {
                predicates.add(criteriaBuilder.equal(root.get("beneficiaryStatut").get("id"), 3));
            } else {
                predicates.add(root.get("beneficiaryStatut").get("id").in(1, 2, 3, 4, 8));
            }
        } else if (Boolean.TRUE.equals(isOldBeneficiary)) {
            if (Boolean.TRUE.equals(isBeneficiaryRejete)) {
                predicates.add(criteriaBuilder.equal(root.get("beneficiaryStatut").get("id"), 7));
            } else if (Boolean.TRUE.equals(isBeneficiaryArchived)) {
                predicates.add(criteriaBuilder.equal(root.get("beneficiaryStatut").get("id"), 9));
            } else if (Boolean.TRUE.equals(isCandidateRejete)) {
                predicates.add(criteriaBuilder.equal(root.get("beneficiaryStatut").get("id"), 5));
            } else {
                predicates.add(root.get("beneficiaryStatut").get("id").in(7, 9, 5));
            }
        }
        //we still need to add another else if for the case of Archived Beneficiary
        else {
            if (Boolean.TRUE.equals(isBenefeiciaryWaiting)) {
                predicates.add(root.get("beneficiaryStatut").get("id").in(10, 13));
            } else if (Boolean.TRUE.equals(isBeneficiaryActif)) {
                predicates.add(criteriaBuilder.equal(root.get("beneficiaryStatut").get("id"), 6));
            } else {
                predicates.add(root.get("beneficiaryStatut").get("id").in(6, 10, 13));
            }
        }


        return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
    }

    public List<BeneficiaryExportDTO> getListBeneficiariesByFilterToExport(List<Long> zoneId, String searchByNom, String lastNameAr, Boolean searchByTypeBeneficiaire,
                                                                           String searchByService, String searchByStatut, String searchByNumTel,
                                                                           Date minDate, Date maxDate, Boolean isCandidate, Boolean isCandidateInitial, Boolean isValidateAssistant, Boolean isValidateKafalat, Boolean isOldBeneficiary,
                                                                           Boolean isBeneficiaryRejete, Boolean isBeneficiaryArchived, Boolean isCandidateRejete, Boolean isBenefeiciaryWaiting, Boolean isBeneficiaryActif) {
        // Create the criteria builder and query
        TypedQuery<Beneficiary> typedQuery = createBeneficiaryCriteriaQuery(zoneId, searchByNom, lastNameAr, searchByTypeBeneficiaire,
                searchByService, searchByStatut, searchByNumTel, minDate, maxDate, isCandidate, isCandidateInitial, isValidateAssistant, isValidateKafalat, isOldBeneficiary, isBeneficiaryRejete, isBeneficiaryArchived, isCandidateRejete, isBenefeiciaryWaiting, isBeneficiaryActif,null);

        List<Beneficiary> listBeneficiaries = typedQuery.getResultList();

        // Map Beneficiary entities to BeneficiaryDTOs with initial mapping
        Iterable<BeneficiaryDTO> beneficiaryDTOIterable = beneficiaryMapper.beneficiaryToBeneficiaryDTO(listBeneficiaries);
        List<BeneficiaryDTO> beneficiaries = StreamSupport.stream(beneficiaryDTOIterable.spliterator(), false)
                .collect(Collectors.toList());

        // Fetch additional data for each BeneficiaryDTO
        fetchAdditionalDataAsynchronously(beneficiaries);

        // Map to BeneficiaryExportDTO

        return mapBeneficiaryDTOToExportDTO(beneficiaries);
    }

    // Fetch additional data for each BeneficiaryDTO in parallel to speed up the process
    private void fetchAdditionalDataAsynchronously(List<BeneficiaryDTO> beneficiaries) {
        // Wait for all futures to complete
        // Getting  the city and related data for each beneficiary in parallel using CompletableFuture
        CompletableFuture.allOf(beneficiaries.stream()
                .map(beneficiary -> CompletableFuture.runAsync(() -> {
                    // Fetch city and related data
                    CityDTO cityDTO = beneficiary.getPerson().getCity();
                    if (cityDTO != null && cityDTO.getId() != null) {
                        CompletableFuture<CityDTO> cityFuture = CompletableFuture.supplyAsync(() -> refFeignClient.getParCity(cityDTO.getId()));
                        CompletableFuture<CityWithRegionAndCountryDTO> countryFuture = CompletableFuture.supplyAsync(() -> refController.getCityWithRegionAndCountry(cityDTO.getId()).getBody());

                        CityDTO fullCityDTO = cityFuture.join();
                        beneficiary.getPerson().setCity(fullCityDTO);

                        CityWithRegionAndCountryDTO fullCountryDto = countryFuture.join();
                        if (fullCountryDto != null) {
                            beneficiary.getPerson().setInfo(fullCountryDto);
                        }
                    }

                    // Fetch service and status details
                    if (beneficiary.getBeneficiaryServices() != null) {
                        beneficiary.getBeneficiaryServices().forEach(serviceDTO -> {
                            CompletableFuture<ServiceDTO> serviceFuture = CompletableFuture.supplyAsync(() -> refFeignClient.getMetService(serviceDTO.getService().getId()));
                            CompletableFuture<StatusDTO> statusFuture = CompletableFuture.supplyAsync(() -> refFeignClient.getParStatus(serviceDTO.getStatus().getId()));

                            ServiceDTO fullServiceDTO = serviceFuture.join();
                            serviceDTO.setService(fullServiceDTO);

                            StatusDTO fullStatusDTO = statusFuture.join();
                            serviceDTO.setStatus(fullStatusDTO);
                        });
                    }
                    // Fetch type identity details
                    TypeIdentityDTO typeIdentityDTO = beneficiary.getPerson().getTypeIdentity();
                    if (typeIdentityDTO != null && typeIdentityDTO.getId() != null) {
                        CompletableFuture<TypeIdentityDTO> typeIdentityFuture = CompletableFuture.supplyAsync(() -> refFeignClient.getParTypeIdentity(typeIdentityDTO.getId()));

                        TypeIdentityDTO fullTypeIdentityDTO = typeIdentityFuture.join();
                        beneficiary.getPerson().setTypeIdentity(fullTypeIdentityDTO);
                    }
                })).toArray(CompletableFuture[]::new)).join();
    }

    private List<BeneficiaryExportDTO> mapBeneficiaryDTOToExportDTO(List<BeneficiaryDTO> beneficiaries) {
        if (beneficiaries == null) {
            return Collections.emptyList();
        }
        // Pré-allouer la liste pour éviter la réallocation fréquente
        List<BeneficiaryExportDTO> exportDTOs = new ArrayList<>(beneficiaries.size());

        // Utiliser parallelStream pour un traitement plus rapide avec plusieurs cœurs
        beneficiaries.parallelStream()
                .map(beneficiaryMapper::beneficiaryDTOToBeneficiaryExportDTO)
                .forEachOrdered(exportDTOs::add);

        return exportDTOs;
    }

    public ExportFileDTO exportFileWithName(String searchByNom, String lastNameAr, Boolean searchByTypeBeneficiaire,
                                            String searchByService, String searchByStatut, String searchByNumTel,
                                            Date minDate, Date maxDate, Boolean isCandidateInitial, Boolean isValidateAssistant, Boolean isValidateKafalat, Boolean isCandidate, Boolean isOldBeneficiary, Boolean isBeneficiaryRejete, Boolean isBeneficiaryArchived, Boolean isCandidateRejete, Boolean isBenefeiciaryWaiting, Boolean isBeneficiaryActif) {

        TimeWatch watch = TimeWatch.start();
        log.debug("Start service exportBeneficiaryFile searchByNom {}, lastNameAr {}, searchByTypeBeneficiaire {}, searchByService {}, searchByStatut {}, searchByNumTel {}, minDate {}, maxDate {}",
                searchByNom, lastNameAr, searchByTypeBeneficiaire, searchByService, searchByStatut, searchByNumTel, minDate, maxDate);


        // Define export parameters
        String sheetName = "Rapport des Bénéficiaires";
        if (isCandidate != null && isCandidate) {
            sheetName = "Rapport des Candidats";
        }


        String[] headers = Arrays.stream(BeneficiaryExportHeaders.values())
                .map(BeneficiaryExportHeaders::getHeaderName)
                .toArray(String[]::new);


        List<AssistantZone> assistantZones = List.of();
        CacheAdUser cacheAdUser = getRoleFromJwt();
        // Get impersonation token from request header
        String impersonationToken = request.getHeader("Impersonation-Token");
        CacheAdUserDTO cacheAdUser1 = null;
        if (impersonationToken != null && !impersonationToken.isEmpty()) {
            cacheAdUser1 = tokenImpersonationService.getImpersonatedUser(impersonationToken);
        }
        if (cacheAdUser1 != null && cacheAdUser1.getRole().getCode().equals(RoleCode.ASSISTANT.getCode())) {
            Assistant assistant = assistantRepository.findByCacheAdUserId(cacheAdUser1.getId());
            List<BeneficiaryExportDTO> listExportDTOVide = new ArrayList<>();
            if (assistant != null) {
                if (!assistant.getAssistantZones().isEmpty()) {
                    assistantZones = assistant.getAssistantZones();
                } else {
                    return exportService.exportEntities(sheetName, headers, listExportDTOVide, this::mapToExportRow);
                }
            } else {
                return exportService.exportEntities(sheetName, headers, listExportDTOVide, this::mapToExportRow);
            }
        }
        else
        if (cacheAdUser!=null && cacheAdUser.getRole().getCode().equals(RoleCode.ASSISTANT.getCode())) {
            Assistant assistant = assistantRepository.findByCacheAdUserId(cacheAdUser.getId());
            List<BeneficiaryExportDTO> listExportDTOVide = new ArrayList<>();
            if (assistant != null) {
                if (!assistant.getAssistantZones().isEmpty()) {
                    assistantZones = assistant.getAssistantZones();
                }  else {
                    return exportService.exportEntities(sheetName, headers, listExportDTOVide, this::mapToExportRow);
                }
            } else {
                return exportService.exportEntities(sheetName, headers, listExportDTOVide, this::mapToExportRow);
            }
        }

        // Filter Beneficiary entities based on criteria
        List<BeneficiaryExportDTO> listExportDTO = getListBeneficiariesByFilterToExport(assistantZones.stream()
                        .map(assistantZone -> assistantZone.getZone().getId())
                        .collect(Collectors.toList()), searchByNom, lastNameAr, searchByTypeBeneficiaire,
                searchByService, searchByStatut, searchByNumTel, minDate, maxDate, isCandidate, isCandidateInitial, isValidateAssistant, isValidateKafalat, isOldBeneficiary, isBeneficiaryRejete, isBeneficiaryArchived, isCandidateRejete, isBenefeiciaryWaiting, isBeneficiaryActif);


        log.debug("End service exportBeneficiaryFile, took {}", watch.toMS());
        // Perform export
        return exportService.exportEntities(sheetName, headers, listExportDTO, this::mapToExportRow);
    }

    private Object[] mapToExportRow(BeneficiaryExportDTO dto) {
        return new Object[]{
                dto.getCode(),
                dto.getFormattedCreatedAt(),
                dto.getFirstName(),
                dto.getLastName(),
                dto.getFormattedBirthDate(),
                dto.getTypeIdentity(),
                dto.getIdentityCode(),
                dto.getPhoneNumber(),
                dto.getEmail(),
                dto.getSex(),
                dto.getAddress(),
                dto.getCity(),
                dto.getRegion(),
                dto.getCountry(),
                dto.getType(),
        };
    }


//    public Beneficiary ajouterBeneficiaryAdHocPerson(BeneficiaryAdHocPersonneDto dto) {
//        // Récupérer ou créer une nouvelle personne associée au bénéficiaire
//        Person person = personRepository.findByIdentityCode(dto.getIdentityCode())
//                .orElse(new Person());  // si la personne n'existe pas, crée une nouvelle
//        String beneficiaryCode = generateBeneficiaryCode(beneficiary);
//        beneficiary.setCode(beneficiaryCode);
//
//
//        // Mise à jour des informations de la personne
//        person.setFirstName(dto.getFirstName());
//        person.setLastName(dto.getLastName());
//        person.setFirstNameAr(dto.getFirstNameAr());
//        person.setLastNameAr(dto.getLastNameAr());
//        person.setTypeIdentityId(dto.getTypeIdentityId());
//        person.setIdentityCode(dto.getIdentityCode());
//
//        // Sauvegarder la personne
//        personRepository.save(person);
//
//        // Créer un nouveau bénéficiaire
//        Beneficiary beneficiary = Beneficiary.builder()
//                .person(person)
//                .code(dto.getCode())
//                .statusBeneficiaryAdHoc(dto.getStatusBeneficiaryAdHoc())
//                .comment(dto.getComment())
//                .aideComplementaires(dto.getAideComplementaires()) // Associer les aides complémentaires
//                .beneficiaryStatut(beneficiaryStatutRepository.findById(BeneficiaryStatus.BENEFICIARY_AD_HOC_INDIVIDUAL.getId()).orElse(null)) // Statut ad hoc
//                .build();
//
//        // Sauvegarder le nouveau bénéficiaire
//        return beneficiaryRepository.save(beneficiary);
//    }


    @Transactional
    public AddedBeneficiaryResponse ajouterBeneficiaryAdHocPerson(BeneficiaryAdHocPersonneDto beneficiaryAdHocPersonneDto) throws TechnicalException {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service Ajouter Beneficiary Ad Hoc {}", beneficiaryAdHocPersonneDto);

        Person person;
        Beneficiary existingBeneficiaryfordto = new Beneficiary();

        if (beneficiaryAdHocPersonneDto.getId() != null) {
            Optional<Beneficiary> beneficiaryOptional = beneficiaryRepository.findById(beneficiaryAdHocPersonneDto.getId());
            Beneficiary existingBeneficiary = beneficiaryOptional.orElseThrow(() -> new TechnicalException("Beneficiary not found"));

            Person existingPerson = new Person();
            BeanUtils.copyProperties(existingBeneficiary.getPerson(), existingPerson);
            existingBeneficiary.setPerson(existingPerson);

            BeanUtils.copyProperties(existingBeneficiary, existingBeneficiaryfordto);

            beneficiaryAdHocPersonneDto.setPersonId(existingBeneficiaryfordto.getPerson().getId());
            beneficiaryAdHocPersonneDto.setCode(existingBeneficiary.getCode());

        }

        if (beneficiaryAdHocPersonneDto.getId() != null && beneficiaryAdHocPersonneDto.getPersonId() != null) {
            personRepository.findById(beneficiaryAdHocPersonneDto.getPersonId()).orElseThrow(() -> new TechnicalException(PERSON_NOT_FOUND));
        }

        person = beneficiaryMapper.mapBeneficiaryAdHocPersonDtoToPerson(beneficiaryAdHocPersonneDto);

        //add TypePriseEnCharges to person
        if (beneficiaryAdHocPersonneDto.getTypePriseEnChargeIds() != null && !beneficiaryAdHocPersonneDto.getTypePriseEnChargeIds().isEmpty()) {
            StringBuilder priseEnChargesString = new StringBuilder();
            int listSize = beneficiaryAdHocPersonneDto.getTypePriseEnChargeIds().size();
            int count = 0;

            for (Long pr : beneficiaryAdHocPersonneDto.getTypePriseEnChargeIds()) {
                TypePriseEnChargeDTO typePriseEnChargeDTO = refFeignClient.getParTypePriseEnCharge(pr);
                priseEnChargesString.append(typePriseEnChargeDTO.getId());
                count++;
                if (count < listSize) {
                    priseEnChargesString.append(",");
                }
            }

            person.setTypePriseEnChargeIdsList(priseEnChargesString.toString());
        }

        var beneficiary = beneficiaryMapper.mapBeneficiaryAdHocPersonDtoToBeneficiary(beneficiaryAdHocPersonneDto);
        if (beneficiary.getCreatedAt() == null) {
            beneficiary.setCreatedAt(Instant.now());
        }

        if (beneficiaryAdHocPersonneDto.getId() == null) {

            String code = generateBeneficiaryAdHocCode(beneficiary);
            beneficiary.setCode(code);
            // Ajouter le statut SBENEFICIARY_AD_HOC_INDIVIDUAL
            BeneficiaryStatut beneficiaryStatut = new BeneficiaryStatut(BeneficiaryStatus.BENEFICIARY_AD_HOC_INDIVIDUAL.getId());
            beneficiary.setBeneficiaryStatut(beneficiaryStatut);
        }

        if (beneficiaryAdHocPersonneDto.getPersonId() != null) {
            person.setId(beneficiaryAdHocPersonneDto.getPersonId());
        }
        //beneficiaryAdHocPersonneDto.setUpdateDate(LocalDateTime.now());

        if (beneficiaryAdHocPersonneDto.getId() != null) {
            Optional<Beneficiary> beneficiaryOptional = beneficiaryRepository.findById(beneficiaryAdHocPersonneDto.getId());
            beneficiary.setId(beneficiaryOptional.get().getId());
            person.setId(beneficiaryAdHocPersonneDto.getPersonId());
            beneficiary.setBeneficiaryStatut(existingBeneficiaryfordto.getBeneficiaryStatut());
        }

        beneficiary.setArchived(Boolean.FALSE);
        beneficiary.setEpsResidents(null);
        beneficiary.setIndependent(Boolean.TRUE);
        person = personRepository.save(person);
        beneficiary.setPerson(person);
        beneficiaryRepository.save(beneficiary);

        AddedBeneficiaryResponse response = new AddedBeneficiaryResponse();
        response.setId(beneficiary.getId());
        response.setPersonId(beneficiary.getPerson().getId());
        response.setCode(beneficiary.getCode());

        log.debug("End service Ajouter Beneficiary Ad Hoc, took {}", watch.toMS());
        return response;
    }

//    public String generateBeneficiaryAdHocCode(Beneficiary beneficiary) {
//        String code = "8";
//
//        String year = beneficiary.getCreatedAt()
//                .atZone(ZoneId.systemDefault())
//                .toLocalDate()
//                .getYear() + "";
//
//        if (beneficiary.getCode() == null || !beneficiary.getCode().substring(1, 5).equals(year)) {
//            Optional<BeneficiaryYearCount> beneficiaryYearCounts = beneficiaryYearCountRepository.findByYear(year);
//
//            if (beneficiaryYearCounts.isPresent()) {
//                BeneficiaryYearCount beneficiaryYearCount = beneficiaryYearCounts.get();
//                Long count = beneficiaryYearCount.getCount() + 1L;
//
//                String orderNumber = String.format("%05d", count);
//                code += beneficiaryYearCount.getYear() + orderNumber;
//
//                beneficiaryYearCount.setCount(count);
//                beneficiaryYearCountRepository.save(beneficiaryYearCount);
//            } else {
//                code += year + "00001";
//
//                BeneficiaryYearCount newBeneficiaryYearCount = new BeneficiaryYearCount();
//                newBeneficiaryYearCount.setYear(year);
//                newBeneficiaryYearCount.setCount(1L);
//                beneficiaryYearCountRepository.save(newBeneficiaryYearCount);
//            }
//        } else if (beneficiary.getCode().substring(1, 5).equals(year)) {
//            code = beneficiary.getCode().substring(0, 10);
//        }
//
//        return code;
//    }

    public String generateBeneficiaryAdHocCode(Beneficiary beneficiary) throws TechnicalException {
        String code = "Ad-Hoc";

        // Vérifier que la date de création n'est pas nulle
        if (beneficiary.getCreatedAt() == null) {
            throw new TechnicalException("La date de création du bénéficiaire est nulle. Impossible de générer le code.");
        }

        String year = beneficiary.getCreatedAt()
                .atZone(ZoneId.systemDefault())
                .toLocalDate()
                .getYear() + "";

        if (beneficiary.getCode() == null || !beneficiary.getCode().substring(1, 5).equals(year)) {
            Optional<BeneficiaryYearCount> beneficiaryYearCounts = beneficiaryYearCountRepository.findByYear(year);

            if (beneficiaryYearCounts.isPresent()) {
                BeneficiaryYearCount beneficiaryYearCount = beneficiaryYearCounts.get();
                Long count = beneficiaryYearCount.getCount() + 1L;

                String orderNumber = String.format("%05d", count);
                code += beneficiaryYearCount.getYear() + orderNumber;

                beneficiaryYearCount.setCount(count);
                beneficiaryYearCountRepository.save(beneficiaryYearCount);
            } else {
                code += year + "00001";

                BeneficiaryYearCount newBeneficiaryYearCount = new BeneficiaryYearCount();
                newBeneficiaryYearCount.setYear(year);
                newBeneficiaryYearCount.setCount(1L);
                beneficiaryYearCountRepository.save(newBeneficiaryYearCount);
            }
        } else if (beneficiary.getCode().substring(1, 5).equals(year)) {
            code = beneficiary.getCode().substring(0, 10);
        }

        return code;
    }


    @Transactional
    public AddedBeneficiaryResponse updateBeneficiaryAdHocPerson(BeneficiaryAdHocPersonneDto beneficiaryAdHocPersonneDto) throws TechnicalException {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service Update Beneficiary Ad Hoc {}", beneficiaryAdHocPersonneDto);

        Beneficiary existingBeneficiary = beneficiaryRepository.findById(beneficiaryAdHocPersonneDto.getId())
                .orElseThrow(() -> new TechnicalException("Beneficiary not found"));

        Person existingPerson = personRepository.findById(beneficiaryAdHocPersonneDto.getPersonId())
                .orElseThrow(() -> new TechnicalException(PERSON_NOT_FOUND));

        Person updatedPerson = beneficiaryMapper.mapBeneficiaryAdHocPersonDtoToPerson(beneficiaryAdHocPersonneDto);
        updatedPerson.setId(existingPerson.getId());

        if (beneficiaryAdHocPersonneDto.getTypePriseEnChargeIds() != null && !beneficiaryAdHocPersonneDto.getTypePriseEnChargeIds().isEmpty()) {
            StringBuilder priseEnChargesString = new StringBuilder();
            int listSize = beneficiaryAdHocPersonneDto.getTypePriseEnChargeIds().size();
            int count = 0;

            for (Long pr : beneficiaryAdHocPersonneDto.getTypePriseEnChargeIds()) {
                TypePriseEnChargeDTO typePriseEnChargeDTO = refFeignClient.getParTypePriseEnCharge(pr);
                priseEnChargesString.append(typePriseEnChargeDTO.getId());
                count++;
                if (count < listSize) {
                    priseEnChargesString.append(",");
                }
            }

            updatedPerson.setTypePriseEnChargeIdsList(priseEnChargesString.toString());
        }

        personRepository.save(updatedPerson);

        Beneficiary updatedBeneficiary = beneficiaryMapper.mapBeneficiaryAdHocPersonDtoToBeneficiary(beneficiaryAdHocPersonneDto);
        updatedBeneficiary.setId(existingBeneficiary.getId());
        updatedBeneficiary.setPerson(updatedPerson);

        updatedBeneficiary.setCode(existingBeneficiary.getCode());
        updatedBeneficiary.setBeneficiaryStatut(existingBeneficiary.getBeneficiaryStatut());

        beneficiaryRepository.save(updatedBeneficiary);

        AddedBeneficiaryResponse response = new AddedBeneficiaryResponse();
        response.setId(updatedBeneficiary.getId());
        response.setPersonId(updatedBeneficiary.getPerson().getId());
        response.setCode(updatedBeneficiary.getCode());

        log.debug("End service Update Beneficiary Ad Hoc, took {}", watch.toMS());
        return response;
    }


    public Page<GetListDTO> getAllBeneficiariesAdHocPersonne(Optional<String> criteria, Optional<String> value1, Optional<String> value2,
                                                             String searchByNom, String lastNameAr, Boolean searchByTypeBeneficiaire,
                                                             String searchByStatut, String searchByNumTel,
                                                             Date minDate, Date maxDate, Optional<Integer> page) {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service get all bénéficiaries ad-hoc personne");

        Pageable pageable = createPageable(page);

        Map<String, String> searchParams = collectSearchParamsBeneficiaryAdHocPersonne(searchByNom, lastNameAr, searchByTypeBeneficiaire, searchByStatut, searchByNumTel, minDate, maxDate);
        String jsonSearchParams = convertMapToJsonString(searchParams);

        Page<Beneficiary> listBeneficiaries = findBeneficiariesAdHocPersonne(criteria, value1, value2, searchByNom, lastNameAr, searchByTypeBeneficiaire, searchByStatut, searchByNumTel, minDate, maxDate, pageable);

        List<GetListDTO> beneficiaries = mapToDTOs(listBeneficiaries);

        log.debug("End service get all bénéficiaries ad-hoc personne, took {}", watch.toMS());
        return new PageImpl<>(beneficiaries, pageable, listBeneficiaries.getTotalElements());
    }

    private Map<String, String> collectSearchParamsBeneficiaryAdHocPersonne(String searchByNom, String lastNameAr, Boolean searchByTypeBeneficiaire,
                                                                            String searchByStatut, String searchByNumTel,
                                                                            Date minDate, Date maxDate) {
        Map<String, String> searchParams = new LinkedHashMap<>();
        if (searchByNom != null) searchParams.put("Nom du bénéficiaire", searchByNom);
        if (lastNameAr != null) searchParams.put("Nom arabe du bénéficiaire", lastNameAr);
        if (searchByTypeBeneficiaire != null)
            searchParams.put("Type du bénéficiaire", searchByTypeBeneficiaire ? "Indépendant" : "Membre de Famille");
        if (searchByStatut != null)
            searchParams.put("Statut", refFeignClient.getParStatus(Long.valueOf(searchByStatut)).getName());
        if (searchByNumTel != null) searchParams.put("Numéro Téléphone", searchByNumTel);
        if (minDate != null) searchParams.put("Date de naissance minimale", minDate.toString());
        if (maxDate != null) searchParams.put("Date de naissance maximale", maxDate.toString());
        return searchParams;
    }


    private Page<Beneficiary> findBeneficiariesAdHocPersonne(Optional<String> criteria, Optional<String> value1, Optional<String> value2,
                                                             String searchByNom, String lastNameAr, Boolean searchByTypeBeneficiaire,
                                                             String searchByStatut, String searchByNumTel,
                                                             Date minDate, Date maxDate, Pageable pageable) {
        if (criteria.isPresent() && value1.isPresent()) {
            return searchBeneficiariesAdHocPersonneByCriteria(criteria.get(), value1, value2, pageable);
        } else if (searchByNom != null || lastNameAr != null || searchByTypeBeneficiaire != null ||
                searchByStatut != null || searchByNumTel != null || minDate != null || maxDate != null) {
            return filterBeneficiariesAdHocPersonne(searchByNom, lastNameAr, searchByTypeBeneficiaire, searchByStatut, searchByNumTel, minDate, maxDate, pageable);
        } else {
            return beneficiaryRepository.findBeneficiaryByBeneficiaryStatut(pageable);
        }
    }

    private Page<Beneficiary> searchBeneficiariesAdHocPersonneByCriteria(String criteria, Optional<String> value1, Optional<String> value2, Pageable pageable) {
        if (criteria.equals("name")) {
            String firstName = value1.orElse("");
            String lastName = value2.orElse("");
            return beneficiaryRepository.searchBeneficiaryByName(firstName, lastName, pageable);
        }
        return Page.empty(pageable);
    }

    public Page<Beneficiary> filterBeneficiariesAdHocPersonne(String searchByNom, String lastNameAr, Boolean searchByTypeBeneficiaire,
                                                              String searchByStatut, String searchByNumTel,
                                                              Date minDate, Date maxDate, Pageable pageable) {
        TypedQuery<Beneficiary> typedQuery = createBeneficiaryAdHocPersonneCriteriaQuery(searchByNom, lastNameAr, searchByTypeBeneficiaire,
                searchByStatut, searchByNumTel, minDate, maxDate);
        long totalCount = typedQuery.getResultList().size();
        typedQuery.setFirstResult((int) pageable.getOffset());
        typedQuery.setMaxResults(pageable.getPageSize());
        List<Beneficiary> resultList = typedQuery.getResultList();
        return new PageImpl<>(resultList, pageable, totalCount);
    }

    private TypedQuery<Beneficiary> createBeneficiaryAdHocPersonneCriteriaQuery(
            String searchByNom,
            String lastNameAr,
            Boolean searchByTypeBeneficiaire,
            String searchByStatut,
            String searchByNumTel,
            Date minDate,
            Date maxDate) {

        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Beneficiary> criteriaQuery = criteriaBuilder.createQuery(Beneficiary.class);
        Root<Beneficiary> root = criteriaQuery.from(Beneficiary.class);

        Predicate predicate = buildPredicateBeneficiaryAdHocPersonne(criteriaBuilder, root, searchByNom, lastNameAr, searchByTypeBeneficiaire, searchByStatut, searchByNumTel, minDate, maxDate);

        criteriaQuery.where(predicate);
        criteriaQuery.orderBy(criteriaBuilder.desc(root.get("createdAt")));

        return entityManager.createQuery(criteriaQuery);
    }

    public Predicate buildPredicateBeneficiaryAdHocPersonne(CriteriaBuilder criteriaBuilder, Root<Beneficiary> root,
                                                            String searchByNom, String lastNameAr, Boolean searchByTypeBeneficiaire, String searchByStatut, String searchByNumTel,
                                                            Date minDate, Date maxDateal) {

        List<Predicate> predicates = new ArrayList<>();
        predicates.add(criteriaBuilder.isFalse(root.get("archived")));


        Join<Beneficiary, Person> personJoin = root.join("person");

        if (searchByNom != null && !searchByNom.isEmpty()) {
            predicates.add(criteriaBuilder.like(
                    criteriaBuilder.lower(personJoin.get("lastName")),
                    "%" + searchByNom.toLowerCase() + "%"));
        }

        if (lastNameAr != null && !lastNameAr.isEmpty()) {
            predicates.add(criteriaBuilder.like(
                    criteriaBuilder.lower(personJoin.get("lastNameAr")),
                    "%" + lastNameAr.toLowerCase() + "%"));
        }

        if (searchByNumTel != null) {
            predicates.add(criteriaBuilder.like(personJoin.get("phoneNumber"), "%" + searchByNumTel + "%"));
        }

        if (searchByTypeBeneficiaire != null) {
            predicates.add(criteriaBuilder.equal(root.get("independent"), searchByTypeBeneficiaire));
        }

        if (minDate != null) {
            predicates.add(criteriaBuilder.greaterThanOrEqualTo(personJoin.get("birthDate"), minDate));
        }

        predicates.add(root.get("beneficiaryStatut").get("id").in(11, 12));

        return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
    }


//    public File generateReport(Long beneficiaryId, String reportType, String language) throws JRException, IOException {
//        // Charger le modèle Jasper
//        InputStream jasperStream = getClass().getClassLoader().getResourceAsStream("Template/orphelin_fr.jrxml");
//
//        if (jasperStream == null) {
//            throw new FileNotFoundException("Template not found");
//        }
//
//        JasperReport jasperReport = JasperCompileManager.compileReport(jasperStream);
//
//        // Préparer les paramètres pour JasperReports
//        Map<String, Object> parameters = prepareReportParameters(null);  // Utilisez les paramètres avec des valeurs valides
//
//        // Utilisez une source de données pour remplir le rapport
//        JRBeanCollectionDataSource dataSource = (JRBeanCollectionDataSource) parameters.get("REPORT_DATA_SOURCE");
//        JasperPrint jasperPrint = JasperFillManager.fillReport(jasperReport, parameters, dataSource);
//
//        // Sauvegarder le rapport en tant que fichier PDF
//        File pdfFile = File.createTempFile("report_", ".pdf");
//        try (FileOutputStream fos = new FileOutputStream(pdfFile)) {
//            JasperExportManager.exportReportToPdfStream(jasperPrint, fos);
//        }
//
//        return pdfFile;
//    }

    public File generateReport(Long beneficiaryId, String reportType, String language) throws JRException, IOException {
        // Charger le modèle Jasper
        InputStream jasperStream = getClass().getClassLoader().getResourceAsStream("Template/orphelin_fr.jrxml");

        if (jasperStream == null) {
            throw new FileNotFoundException("Template not found");
        }

        JasperReport jasperReport = JasperCompileManager.compileReport(jasperStream);

        // Récupérer les informations du bénéficiaire
        Beneficiary beneficiary = beneficiaryRepository.findById(beneficiaryId)
                .orElseThrow(() -> new RuntimeException("Beneficiary not found"));

        BeneficiaryDTO beneficiaryDTO = beneficiaryMapper.beneficiaryToBeneficiaryDTO(beneficiary);

        // Préparer les paramètres pour le rapport
        Map<String, Object> parameters = prepareReportParameters(beneficiaryDTO);

        // Utiliser la source de données pour générer le rapport
        JRBeanCollectionDataSource dataSource = (JRBeanCollectionDataSource) parameters.get("REPORT_DATA_SOURCE");
        JasperPrint jasperPrint = JasperFillManager.fillReport(jasperReport, parameters, dataSource);

        // Sauvegarder le rapport en tant que fichier PDF
        File pdfFile = File.createTempFile("report_", ".pdf");
        try (FileOutputStream fos = new FileOutputStream(pdfFile)) {
            JasperExportManager.exportReportToPdfStream(jasperPrint, fos);
        }

        return pdfFile;
    }

    private Map<String, Object> prepareReportParameters(BeneficiaryDTO beneficiary) throws IOException {
        Map<String, Object> parameters = new LinkedHashMap<>();

        // Préparer les données dynamiques
        List<Map<String, Object>> data = new ArrayList<>();
        Map<String, Object> row = new LinkedHashMap<>();

        // Ajouter les informations sur le bénéficiaire
        row.put("fullName", beneficiary.getPerson().getFirstName() + " " + beneficiary.getPerson().getLastName());
        row.put("address", beneficiary.getPerson().getAddress());
        row.put("healthCondition", "Bon état de santé");

        if (beneficiary.getPerson().getPictureUrl() != null) {
            try {
                byte[] imageData = minioService.ReadFromMinIO(beneficiary.getPerson().getPictureUrl(), false);
                String base64Image = Base64.getEncoder().encodeToString(imageData);
                log.debug("Generated Base64 Image: " + base64Image); // Log de la chaîne Base64
                row.put("pictureBase64", imageData);  // Utiliser base64 pour l'image
            } catch (TechnicalException e) {
                log.error("Erreur lors de la récupération de l'image depuis MinIO", e);
                row.put("pictureBase64", null);  // Si l'image ne peut pas être récupérée
            }
        }

        // Ajoutez la ligne de données à la liste des données
        data.add(row);

        // Utiliser la source de données pour JasperReports
        JRBeanCollectionDataSource dataSource = new JRBeanCollectionDataSource(data);
        parameters.put("REPORT_DATA_SOURCE", dataSource);

        return parameters;
    }

    private String getTemplatePath(String reportType, String language) {
        switch (reportType.toLowerCase()) {
            case "handicap":
                return "Template/handicap_" + language + ".jrxml";
            case "student":
                return "Template/student_" + language + ".jrxml";
            case "orphan":
                return "Template/orphan_" + language + ".jrxml";
            default:
                throw new RuntimeException("Unknown report type: " + reportType);
        }
    }

    /**
     * Save coordinates for a beneficiary
     * 
     * @param id The ID of the beneficiary
     * @param coordinatesDTO The coordinates DTO containing the coordinates string
     * @return The updated beneficiary
     * @throws TechnicalException if the beneficiary is not found
     */
    @Transactional
    public Beneficiary saveCoordinates(Long id, BeneficiaryCoordinatesDTO coordinatesDTO) throws TechnicalException {
        Beneficiary beneficiary = beneficiaryRepository.findById(id)
                .orElseThrow(() -> new TechnicalException("Beneficiary not found with id: " + id));

        beneficiary.setCoordinates(coordinatesDTO.getCoordinates());
        beneficiary.setLastUpdateDate(LocalDateTime.now());

        Beneficiary updatedBeneficiary = beneficiaryRepository.save(beneficiary);

        return updatedBeneficiary;
    }
}
