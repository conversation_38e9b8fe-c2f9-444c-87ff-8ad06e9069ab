-- Création de la table AideComplementaire
CREATE TABLE aide_complementaire (
                                     id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
                                     name VARCHAR(255) NOT NULL,
                                     code VARCHAR(255) UNIQUE NOT NULL,
                                     type_prise_en_charge_id BIGINT,
                                     statut VARCHAR(50),
                                     montant_prevu BIGINT,
                                     CONSTRAINT aide_complementaire_code_unique UNIQUE (code)
);

-- Ajout de la colonne aide_complementaire_id dans la table Beneficiary pour la relation Many-to-One
ALTER TABLE beneficiary
    ADD COLUMN aide_complementaire_id BIGINT;

-- Ajout de la contrainte de clé étrangère pour relier Beneficiary à AideComplementaire
ALTER TABLE beneficiary
    ADD CONSTRAINT fk_aide_complementaire
        FOREIGN KEY (aide_complementaire_id) REFERENCES aide_complementaire(id)
            ON DELETE SET NULL;
