CREATE TABLE aide_complementaire_beneficiary_ad_hoc_groupe (
       id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
       aide_complementaire_id BIGINT NOT NULL,
       beneficiary_ad_hoc_group_id BIGINT NOT NULL,
       montant_affecter DOUBLE PRECISION,
       statut_validation BOOLEAN,

       CONSTRAINT fk_aide_complementaire FOREI<PERSON><PERSON> KEY (aide_complementaire_id)
           REFERENCES aide_complementaire(id) ON DELETE CASCADE,
       CONSTRAINT fk_beneficiary_ad_hoc_group FOREIGN KEY (beneficiary_ad_hoc_group_id)
           REFERENCES beneficiary_ad_hoc_group(id) ON DELETE CASCADE
);
