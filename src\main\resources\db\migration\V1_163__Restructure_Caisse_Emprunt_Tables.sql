-- Drop the existing foreign key constraint for service
ALTER TABLE caisse_emprunt DROP CONSTRAINT IF EXISTS fk_service;

-- Rename existing columns and add new ones to caisse_emprunt
ALTER TABLE caisse_emprunt 
    DROP COLUMN IF EXISTS service_id,
    DROP COLUMN IF EXISTS date_emprunt,
    DROP COLUMN IF EXISTS date_remboursement,
    ADD COLUMN IF NOT EXISTS global_amount FLOAT,
    ADD COLUMN IF NOT EXISTS last_date_emprunt TIMESTAMP,
    ADD COLUMN IF NOT EXISTS last_date_remboursement TIMESTAMP;

-- Update the amount column to global_amount if it exists
UPDATE caisse_emprunt SET global_amount = amount WHERE global_amount IS NULL AND amount IS NOT NULL;

-- Drop the old amount column
ALTER TABLE caisse_emprunt DROP COLUMN IF EXISTS amount;

-- Create the new caisse_emprunt_history table
CREATE TABLE IF NOT EXISTS caisse_emprunt_history (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    caisse_emprunt_id BIGINT NOT NULL,
    amount FLOAT NOT NULL,
    date_emprunt TIMESTAMP,
    date_remboursement TIMESTAMP,
    type VARCHAR(50) NOT NULL, -- 'EMPRUNT' or 'REMBOURSEMENT'
    creation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_caisse_emprunt_history_caisse_emprunt 
        FOREIGN KEY (caisse_emprunt_id) REFERENCES caisse_emprunt(id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_caisse_emprunt_history_caisse_emprunt_id ON caisse_emprunt_history(caisse_emprunt_id);
CREATE INDEX IF NOT EXISTS idx_caisse_emprunt_history_type ON caisse_emprunt_history(type);
CREATE INDEX IF NOT EXISTS idx_caisse_emprunt_history_date_emprunt ON caisse_emprunt_history(date_emprunt);
CREATE INDEX IF NOT EXISTS idx_caisse_emprunt_history_date_remboursement ON caisse_emprunt_history(date_remboursement);
