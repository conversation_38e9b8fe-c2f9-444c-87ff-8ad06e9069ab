package ma.almobadara.backend.service.migration;

import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.takenInCharge.TakenInChargeOperationDTO;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.model.beneficiary.Beneficiary;
import ma.almobadara.backend.model.donor.Donor;
import ma.almobadara.backend.model.service.Services;
import ma.almobadara.backend.model.takenInCharge.TakenInChargeDonor;
import ma.almobadara.backend.repository.donor.TakenInChargeDonorRepository;
import ma.almobadara.backend.service.donor.DonorService;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.CellValue;
import org.apache.poi.ss.usermodel.FormulaEvaluator;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;

import ma.almobadara.backend.repository.donor.DonorRepository;
import ma.almobadara.backend.repository.beneficiary.BeneficiaryRepository;
import ma.almobadara.backend.repository.services.ServicesRepository;
import ma.almobadara.backend.repository.administration.TagRepository;
import ma.almobadara.backend.repository.administration.TaggableRepository;
import ma.almobadara.backend.dto.takenInCharge.TakenInChargeDTO;
import ma.almobadara.backend.dto.takenInCharge.TakenInChargeDonorDTO;
import ma.almobadara.backend.dto.takenInCharge.TakenInChargeBeneficiaryDTO;
import ma.almobadara.backend.dto.donor.DonorDTO;
import ma.almobadara.backend.dto.beneficiary.BeneficiaryDTO;
import ma.almobadara.backend.dto.service.ServicesDTO;
import ma.almobadara.backend.dto.administration.TagDTO;
import ma.almobadara.backend.service.takenInCharge.TakenInChargeService;
import org.springframework.transaction.annotation.Transactional;
import ma.almobadara.backend.mapper.DonorMapper;
import ma.almobadara.backend.mapper.BeneficiaryMapper;

import static ma.almobadara.backend.service.migration.util.UtilityClass.getCellValue;

@Service
@Slf4j
@RequiredArgsConstructor
public class KafalatMigrationService {

    private final DonorRepository donorRepository;
    private final BeneficiaryRepository beneficiaryRepository;
    private final ServicesRepository servicesRepository;
    private final DonorService donorService;
    private final TagRepository tagRepository;
    private final TaggableRepository taggableRepository;
    private final TakenInChargeDonorRepository takenInChargeDonorRepository;
    private final TakenInChargeService takenInChargeService;
    private final DonorMapper donorMapper;
    private final BeneficiaryMapper beneficiaryMapper;
    private final EntityManager entityManager;
    @Transactional
    public void migrateKafalat(MultipartFile file) throws TechnicalException, IOException {
        log.info("Received kafalat migration file: {}", file.getOriginalFilename());
        try (Workbook workbook = new XSSFWorkbook(file.getInputStream())) {
            FormulaEvaluator evaluator = workbook.getCreationHelper().createFormulaEvaluator();
            Sheet sheet = workbook.getSheetAt(1);
            for (Row row : sheet) {
                if (row.getRowNum() == 0) continue; // Skip header
                if (row.getCell(0) == null || row.getCell(0).getCellType() == CellType.BLANK) break;
                String codeBeneficiary = getEvaluatedCellValue(row, 0, evaluator);
                String donorCode = getEvaluatedCellValue(row, 4, evaluator);
                String serviceName = getEvaluatedCellValue(row, 2, evaluator);
                if (donorCode == null || donorCode.isEmpty() || codeBeneficiary == null || codeBeneficiary.isEmpty()) {
                    log.warn("Row {} skipped: missing donor or beneficiary code", row.getRowNum());
                    continue;
                }

                Map<Long, Long> serviceId = mapServiceNameToId(serviceName);
                Optional<Services> service = servicesRepository.findByServiceCategoryIdAndServiceCategoryTypeId(serviceId.get(0), serviceId.get(1));
                if (serviceId == null) {
                    log.warn("Row {} skipped: unknown service name {}", row.getRowNum(), serviceName);
                    continue;
                }
                if(!service.isPresent()){
                    log.warn("Row {} skipped: service not found for id {}", row.getRowNum(), serviceId);
                    continue;
                }
                DonorDTO donorDTO = findDonorDTO(donorCode);
                BeneficiaryDTO beneficiaryDTO = findBeneficiaryDTO(codeBeneficiary);
                if (donorDTO == null) {
                    log.warn("Row {} skipped: donor not found for code {}", row.getRowNum(), donorCode);
                    continue;
                }
                if (beneficiaryDTO == null) {
                    log.warn("Row {} skipped: beneficiary not found for code {}", row.getRowNum(), codeBeneficiary);
                    continue;
                }
                if(!takenInChargeDonorRepository.findByDonorIdAndBeneficiaryId(donorDTO.getId(),beneficiaryDTO.getId()).isPresent()) continue;
                TakenInChargeDonorDTO donorLink = TakenInChargeDonorDTO.builder().donor(DonorDTO.builder().id(donorDTO.getId()).build()).build();
                TakenInChargeBeneficiaryDTO beneficiaryLink = TakenInChargeBeneficiaryDTO.builder().id(beneficiaryDTO.getId()).build();
                List<TagDTO> tags = buildTags();
                Date startDate = buildStartDate();
                TakenInChargeOperationDTO operation = buildOperation(row, evaluator, donorLink, startDate);
                TakenInChargeDTO dto = TakenInChargeDTO.builder()
                        .startDate(startDate)

                        .serviceId(service.get().getId())
                        .services(ServicesDTO.builder().id(service.get().getId()).build())
                        .takenInChargeDonors(List.of(donorLink))
                        .takenInChargeBeneficiaries(List.of(beneficiaryLink))
                        .tags(tags)
                        .status("Inactif")
                        .build();
                try {
                    TakenInChargeDTO takenInChargeDTO = takenInChargeService.addTakenInCharge(dto);
                    log.info("Row {} migrated: donor {} -> beneficiary {} for service {}", row.getRowNum(), donorCode, codeBeneficiary, serviceName);
                } catch (Exception e) {
                    log.error("Row {} migration failed: {}", row.getRowNum(), e.getMessage());
                }
            }
        }
    }

    private Map<Long, Long> mapServiceNameToId(String serviceName) {
        Map<String, Map<Long, Long>> serviceMap = Map.of(
                "ORPHELIN", Map.of(1L, 1L),
                "ETUDIANT", Map.of(1L, 2L),
                "VEUVE", Map.of(1L, 3L),
                "HANDICAPE", Map.of(1L, 4L),
                "FAMILLE", Map.of(1L, 5L),
                "ENFANT DIFFICULTE", Map.of(1L, 6L)
        );

        return serviceMap.getOrDefault(serviceName != null ? serviceName.toUpperCase() : "", null);
    }

    private DonorDTO findDonorDTO(String donorCode) throws TechnicalException {
        var donorOpt = donorRepository.findByCodeComptabilite(donorCode);
        if (donorOpt.isEmpty()) return null;
        Donor donorEntity = donorOpt.get();
        return donorService.getDonorById(donorEntity.getId());
    }

    private BeneficiaryDTO findBeneficiaryDTO(String codeBeneficiary) {
        var beneficiaryOpt = beneficiaryRepository.findByCodeBeneficiary(codeBeneficiary);
        if (beneficiaryOpt.isEmpty()) return null;
        Beneficiary beneficiaryEntity = beneficiaryOpt.get();
        return beneficiaryMapper.beneficiaryToBeneficiaryDTO(beneficiaryEntity);
    }

    private List<TagDTO> buildTags() {
        List<TagDTO> tags = new ArrayList<>();
        tagRepository.findByNameAndType("migration", "metier").ifPresent(tag -> tags.add(TagDTO.builder().id(tag.getId()).name(tag.getName()).build()));
        tagRepository.findByNameAndType("à_compléter", "metier").ifPresent(tag -> tags.add(TagDTO.builder().id(tag.getId()).name(tag.getName()).build()));
        return tags;
    }

    private Date buildStartDate() {
        LocalDate jan1LastYear = LocalDate.now().minusYears(1).withDayOfYear(1);
        return Date.from(jan1LastYear.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }

    private TakenInChargeOperationDTO buildOperation(Row row, FormulaEvaluator evaluator, TakenInChargeDonorDTO donorLink, Date startDate) {
        String amountStr = getEvaluatedCellValue(row, 9, evaluator);
        double amount = 0;
        try {
            amount = amountStr != null && !amountStr.isEmpty() ? Double.parseDouble(amountStr) : 0;
        } catch (NumberFormatException e) {
            log.warn("Row {}: invalid amount '{}', defaulting to 0", row.getRowNum(), amountStr);
        }
        return TakenInChargeOperationDTO.builder()
                .amount(amount)
                .managementFees(10)
                .planningDate(startDate)

                .takenInChargeDonor(donorLink)
                .build();
    }

    private String getEvaluatedCellValue(Row row, int cellIndex, FormulaEvaluator evaluator) {
        Cell cell = row.getCell(cellIndex);
        if (cell != null) {
            if (cell.getCellType() == CellType.FORMULA) {
                CellValue evaluatedValue = evaluator.evaluate(cell);
                switch (evaluatedValue.getCellType()) {
                    case STRING: return evaluatedValue.getStringValue();
                    case NUMERIC: return java.math.BigDecimal.valueOf(evaluatedValue.getNumberValue()).stripTrailingZeros().toPlainString();
                    case BOOLEAN: return String.valueOf(evaluatedValue.getBooleanValue());
                    default: return "";
                }
            } else {
                return getCellValue(row, cellIndex);
            }
        }
        return "";
    }

    public void planOperation(String donorCode, String beneficiaryCode, double amount) throws TechnicalException {
        var donorOpt = donorRepository.findByCodeComptabilite(donorCode);
        if (donorOpt.isEmpty()) throw new IllegalArgumentException("Donor not found for code: " + donorCode);
        var beneficiaryOpt = beneficiaryRepository.findByCodeBeneficiary(beneficiaryCode);
        if (beneficiaryOpt.isEmpty()) throw new IllegalArgumentException("Beneficiary not found for code: " + beneficiaryCode);
        Long donorId = donorOpt.get().getId();
        Long beneficiaryId = beneficiaryOpt.get().getId();
        // Find the TakenInCharge for this donor and beneficiary
        Optional<TakenInChargeDonor> takenInChargeOpt =takenInChargeDonorRepository.findByDonorIdAndBeneficiaryId(donorId,beneficiaryId);
        if (takenInChargeOpt.isEmpty()) throw new IllegalArgumentException("TakenInCharge not found for donor and beneficiary");
        // Find the donor link
        amount=(amount*10)/100;
        Date planningDate = new Date();
        TakenInChargeOperationDTO operation = TakenInChargeOperationDTO.builder()
                .amount(amount)
                .managementFees(10)
                .planningDate(planningDate)
                .takenInChargeDonor(TakenInChargeDonorDTO.builder().id(takenInChargeOpt.get().getId()).build())
                .build();
        takenInChargeService.planTakenInCharge(List.of(operation));
    }

    public void planOperationsFromFile(MultipartFile file) throws IOException {
        try (Workbook workbook = new XSSFWorkbook(file.getInputStream())) {
            Sheet sheet = workbook.getSheetAt(1); // or 0, depending on your file
            FormulaEvaluator evaluator = workbook.getCreationHelper().createFormulaEvaluator();
            for (Row row : sheet) {
                if (row.getRowNum() == 0) continue; // skip header
                if (row.getCell(0) == null || row.getCell(0).getCellType() == CellType.BLANK) break;

                String donorCode = getEvaluatedCellValue(row, 4, evaluator);
                String beneficiaryCode = getEvaluatedCellValue(row, 0, evaluator);
                String amountStr = getEvaluatedCellValue(row, 9, evaluator);
                double amount = 0;
                try {
                    amount = amountStr != null && !amountStr.isEmpty() ? Double.parseDouble(amountStr) : 0;
                } catch (NumberFormatException e) {
                    log.warn("Row {}: invalid amount '{}', defaulting to 0", row.getRowNum(), amountStr);
                }
                try {
                    planOperation(donorCode, beneficiaryCode, amount);
                    log.info("Row {}: Operation planned for donor {} and beneficiary {}", row.getRowNum(), donorCode, beneficiaryCode);
                } catch (Exception e) {
                    log.error("Row {}: Failed to plan operation: {}", row.getRowNum(), e.getMessage());
                }
            }
        }
    }
} 