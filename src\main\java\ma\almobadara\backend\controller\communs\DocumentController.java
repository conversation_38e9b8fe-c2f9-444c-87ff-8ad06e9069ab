package ma.almobadara.backend.controller.communs;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.Audit.AuditApplicationService;
import ma.almobadara.backend.dto.communs.DocumentAndEntityDto;
import ma.almobadara.backend.dto.communs.DocumentDTO;
import ma.almobadara.backend.dto.communs.DocumentRenewDTO;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.service.communs.DocumentService;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static ma.almobadara.backend.service.administration.CacheAdUserService.getUsernameFromJwt;
import static ma.almobadara.backend.util.constants.GlobalConstants.*;


@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/documents")
public class DocumentController {

    private final DocumentService documentService;
    private final AuditApplicationService auditApplicationService;

    @PostMapping(consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    @Operation(summary = "Create a Document and Assign to Entity", description = "add a new document and assign it to an entity", tags = {"SIG"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = DocumentDTO.class))))})
    public ResponseEntity<DocumentDTO> createDocumentAndAssignToEntity(@ModelAttribute DocumentAndEntityDto documentAndEntityDto) throws IOException, TechnicalException {
        log.info("Start resource createDocumentAndAssignToEntity {}", documentAndEntityDto);

        DocumentDTO createdDocument = documentService.createDocumentAndAssignToEntity(documentAndEntityDto);

        log.info("End resource createDocumentAndAssignToEntity  {}", documentAndEntityDto);
        return new ResponseEntity<>(createdDocument, new HttpHeaders(), HttpStatus.OK);
    }

    @GetMapping("")
    public List<DocumentDTO> getAllDocuments() {
        log.info("Start resource getAllDocuments ");

        List<DocumentDTO> documentDTOList = documentService.getAllDocuments();

        log.info("End resource getAllDocuments size : {} ", documentDTOList.size());
        return documentDTOList;
    }

    @DeleteMapping("/deleteDocument/{target}/{idDocument}")
    public ResponseEntity<String> deleteDocumentByTargetAndId(@PathVariable String target, @PathVariable Long idDocument) throws TechnicalException {
        log.info("Start resource deleteDocumentByTargetAndId. Target: {}, Document ID: {}", target, idDocument);

        documentService.deleteDocumentByDocumentId(target, idDocument);

        log.info("End resource deleteDocumentByTargetAndId. Target: {}, Document ID: {}", target, idDocument);
        return new ResponseEntity<>("Document deleted successfully", HttpStatus.OK);
    }

    @GetMapping("/{entityType}/{entityId}")
    @Operation(summary = "Get All Documents By Entity", description = "get all documents associated with a specific entity", tags = {"SIG"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = DocumentDTO.class))))})
    public ResponseEntity<List<DocumentDTO>> getAllDocumentsByEntity(
            @PathVariable String entityType,
            @PathVariable Long entityId) {
        log.info("Start  resource getAllDocumentsByEntity. Entity Type: {}, Entity ID: {}", entityType, entityId);

        List<DocumentDTO> documentDTOList = documentService.getAllDocumentsByEntity(entityType, entityId);

        log.info("End resource getAllDocumentsByEntity. Entity Type: {}, Entity ID: {}, Document Count: {}", entityType, entityId, documentDTOList.size());
        return new ResponseEntity<>(documentDTOList, new HttpHeaders(), HttpStatus.OK);
    }

    @GetMapping("/download/{idDocument}")
    public ResponseEntity<byte[]> downloadDocumentById(@PathVariable Long idDocument) throws TechnicalException {
        log.info("Start resource downloadDocumentById  {}", idDocument);
        byte[] documentBytes = documentService.downloadDocumentById(idDocument);
        String fileName = documentService.getDocumentFileNameOrUrl(idDocument);
        log.info("End resource downloadDocumentById  {} with fileName {}", idDocument, fileName);
        return ResponseEntity.ok()
                .header("Access-Control-Expose-Headers", HttpHeaders.CONTENT_DISPOSITION)
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + fileName)
                .contentType(MediaType.valueOf(MediaType.APPLICATION_OCTET_STREAM_VALUE))
                .body(documentBytes);
    }

    @GetMapping("/view/{idDocument}")
    public ResponseEntity<byte[]> viewDocumentById(@PathVariable Long idDocument) throws TechnicalException {
        log.info("Start resource viewDocumentById  {}", idDocument);

        // Retrieve the document bytes as you would for download
        byte[] documentBytes = documentService.downloadDocumentById(idDocument);

        // Get the filename or URL to extract its type
        String fileName = documentService.getDocumentFileNameOrUrl(idDocument);

        // Check the file type (e.g., PNG, PDF, etc.) and set the correct content type for viewing
        String contentType = documentService.getContentType(fileName);  // You might need a method to determine the file type

        log.info("End resource viewDocumentById  {} with fileName {}", idDocument, fileName);

        return ResponseEntity.ok()
                .header("Access-Control-Expose-Headers", HttpHeaders.CONTENT_DISPOSITION)
                .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=" + fileName)  // Use inline for viewing in browser
                .contentType(MediaType.valueOf(contentType))
                .body(documentBytes);
    }

    @GetMapping("/renew")
    @Operation(summary = "Get All Documents By Entity", description = "get all documents associated with a specific entity", tags = {"SIG"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = DocumentDTO.class))))})
    public ResponseEntity<Page<DocumentRenewDTO>> getAllDocumentsToRenew(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam() List<Long> zoneId,
            @RequestParam(required = false) final String searchByName,
            @RequestParam(required = false) final String searchByModule,
            @RequestParam(required = false) final String searchByType,
            @RequestParam(required = false) final String searchByExpiryDate

    ) {

        Page<DocumentRenewDTO> documentDTOList = documentService.getToRenewDocuments(page,size,zoneId,searchByName,searchByModule,searchByType,searchByExpiryDate);

        return new ResponseEntity<>(documentDTOList, new HttpHeaders(), HttpStatus.OK);
    }




}
