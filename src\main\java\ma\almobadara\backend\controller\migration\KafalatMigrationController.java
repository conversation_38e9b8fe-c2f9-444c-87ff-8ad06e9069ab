package ma.almobadara.backend.controller.migration;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.exceptions.FunctionalException;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.service.migration.KafalatMigrationService;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.usermodel.FormulaEvaluator;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

@RestController
@RequestMapping("/kafalat-migration")
@Slf4j
@RequiredArgsConstructor
public class KafalatMigrationController {

    private final KafalatMigrationService kafalatMigrationService;

    @PostMapping("/upload")
    public ResponseEntity<String> migrateKafalatData(@RequestParam("file") MultipartFile file) throws FunctionalException {
        log.info("Received request to migrate kafalat data");
        try {
            kafalatMigrationService.migrateKafalat(file);
            return ResponseEntity.ok("Kafalat data migration completed successfully.");
        } catch (IOException e) {
            log.error("Error during kafalat migration: {}", e.getMessage());
            return ResponseEntity.internalServerError().body("Error during kafalat migration: " + e.getMessage());
        } catch (TechnicalException e) {
            throw new RuntimeException(e);
        }
    }

    @PostMapping("/operation")
    public ResponseEntity<String> planOperationsFromFile(@RequestParam("file") MultipartFile file) {
        try {
            kafalatMigrationService.planOperationsFromFile(file);
            return ResponseEntity.ok("Operations planned successfully from file.");
        } catch (Exception e) {
            log.error("Error planning operations from file: {}", e.getMessage());
            return ResponseEntity.status(500).body("Error planning operations from file: " + e.getMessage());
        }
    }
} 