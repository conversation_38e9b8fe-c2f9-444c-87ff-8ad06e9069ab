package ma.almobadara.backend.dto.exportentities;

import lombok.*;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class DonationExportDTO {
    private String code;
    private LocalDateTime createdAt;
    private Double value;
    private String transactionNumber;
    private String typeDonation;
    private String identifiedDonor;
    private String donorName;
    private String typeDonor;
    private String canalDonation;
    private Long canalDonationId;
    private Date receptionDate;
    private String comment;

    public String getFormattedReceptionDate() {
        if (receptionDate != null) {
            DateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
            return dateFormat.format(receptionDate);
        }
        return null;
    }


}
