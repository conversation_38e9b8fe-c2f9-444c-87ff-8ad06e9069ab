package ma.almobadara.backend.Cache;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import org.springframework.http.ResponseEntity;
import org.springframework.http.HttpStatus;



@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/cache")
public class CachingController {

    @Autowired
    private CachingService cachingService;

    @Autowired
    public CachingController(CachingService cachingService) {
        this.cachingService = cachingService;
    }


    @GetMapping("/clearAllCaches")
    @Operation(summary = "Clear all caches", description = "Clear all caching service caches", tags = {"SIG"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Caches cleared successfully"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<Boolean> clearAllCaches() {
        log.info("Start resource Add clearAllCaches");
        try {
            cachingService.evictAllCaches();
            log.info("End resource Add clearAllCaches");
            return ResponseEntity.ok(Boolean.TRUE);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Boolean.FALSE);
        }
    }


}
