package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.family.DocumentFamilyMemberDTO;
import ma.almobadara.backend.model.family.DocumentFamilyMember;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface FamilyMemberDocumentMapper {

	DocumentFamilyMemberDTO familyMemberDocumentToFamilyMemberDocumentDTO(DocumentFamilyMember familyMemberDocument);

	Iterable<DocumentFamilyMemberDTO> familyMemberDocumentToFamilyMemberDocumentDTO(Iterable<DocumentFamilyMember> familyMemberDocuments);

	DocumentFamilyMember familyMemberDocumentDTOToFamilyMemberDocument(DocumentFamilyMemberDTO familyMemberDocumentDTO);

	Iterable<DocumentFamilyMember> familyMemberDocumentDTOToFamilyMemberDocument(Iterable<DocumentFamilyMemberDTO> familyMemberDocumentDTOS);

}
