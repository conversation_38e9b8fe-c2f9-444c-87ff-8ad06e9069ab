package ma.almobadara.backend.service.family;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.Audit.AuditApplicationService;
import ma.almobadara.backend.clients.RefFeignClient;
import ma.almobadara.backend.dto.ExternalIncomeDTO;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.mapper.ExternalIncomeMapper;
import ma.almobadara.backend.model.family.ExternalIncome;
import ma.almobadara.backend.model.family.FamilyMember;
import ma.almobadara.backend.repository.family.ExternalIncomeRepository;
import ma.almobadara.backend.repository.family.FamilyMemberRepository;
import ma.almobadara.backend.util.times.TimeWatch;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Optional;

import static ma.almobadara.backend.Audit.ObjectConverter.convertObjectToJson;
import static ma.almobadara.backend.service.administration.CacheAdUserService.getUsernameFromJwt;
import static ma.almobadara.backend.util.constants.GlobalConstants.*;

@Service
@AllArgsConstructor
@Slf4j
public class ExternalIncomeService {

	private final ExternalIncomeRepository externalIncomeRepository;
	private final FamilyMemberRepository familyMemberRepository;
	private final ExternalIncomeMapper externalIncomeMapper;
	private final RefFeignClient refFeignClient;
	private final AuditApplicationService auditApplicationService;

	public ExternalIncomeDTO addExternalIncome(ExternalIncomeDTO externalIncomeDTO) throws TechnicalException{
		TimeWatch watch = TimeWatch.start();
		log.debug("Start service Add ExternalIncome ");
		if (externalIncomeDTO == null){
			throw new TechnicalException("External Income should not be null");
		}
		ExternalIncome newExternalIncome = null;
		ExternalIncome externalIncome = externalIncomeMapper.externalIncomeDTOToExternalIncome(externalIncomeDTO);
		Long familyMemberId = externalIncomeDTO.getFamilyMember().getId();
		FamilyMember familyMemberEntity = familyMemberRepository.findById(familyMemberId).orElse(null);

		if (familyMemberEntity == null) {
			throw new TechnicalException("Family member not found");
		}

		ExternalIncome existingExternalIncomeOptionalforAudit = null;

		if (externalIncomeDTO.getId() != null){
			Optional<ExternalIncome> existingExternalIncomeOptional = externalIncomeRepository.findById(externalIncomeDTO.getId());
			existingExternalIncomeOptional.ifPresent(income -> externalIncome.setId(income.getId()));
			existingExternalIncomeOptionalforAudit = existingExternalIncomeOptional.get();
		}

		externalIncome.setFamilyMember(familyMemberEntity);
		newExternalIncome = externalIncomeRepository.save(externalIncome);
		ExternalIncomeDTO newExternalIncomeDTO = externalIncomeMapper.externalIncomeToExternalIncomeDTO(newExternalIncome);
		newExternalIncomeDTO.setIncomeSource(externalIncomeDTO.getIncomeSource());

		Optional<ExternalIncome> existingExternalIncomeOptional = null;
		if (externalIncomeDTO.getId() != null) {
			existingExternalIncomeOptional = Optional.ofNullable(existingExternalIncomeOptionalforAudit);
			if (existingExternalIncomeOptional.isPresent()) {
				ExternalIncome existingExternalIncome = existingExternalIncomeOptional.get();
				ExternalIncomeDTO existingExternalIncomeDto = externalIncomeMapper.externalIncomeToExternalIncomeDTO(existingExternalIncome);

				var externalIncomeDtoConvert = externalIncomeMapper.externalIncomeAuditDTOToExternalIncomeDto(existingExternalIncomeDto);
				externalIncomeDtoConvert.setIncomeSourceId(existingExternalIncomeOptional.get().getIncomeSourceId());
				externalIncomeDtoConvert.setFamilyMemberId(existingExternalIncomeOptional.get().getFamilyMember().getId());

				var newExternalIncomeAudit = externalIncomeMapper.externalIncomeAuditDTOToExternalIncomeDto(newExternalIncomeDTO);
				newExternalIncomeAudit.setIncomeSourceId(externalIncomeDTO.getIncomeSource().getId());
				newExternalIncomeAudit.setFamilyMemberId(externalIncomeDTO.getFamilyMember().getId());
				auditApplicationService.audit("Update externalIncome", getUsernameFromJwt(), "Update externalIncome",
						convertObjectToJson(externalIncomeDtoConvert),convertObjectToJson(newExternalIncomeAudit), FAMILLE, UPDATE);
			}
		}else{
			var newExternalIncomeAudit = externalIncomeMapper.externalIncomeAuditDTOToExternalIncomeDto(newExternalIncomeDTO);
			newExternalIncomeAudit.setIncomeSourceId(externalIncomeDTO.getIncomeSource().getId());
			newExternalIncomeAudit.setFamilyMemberId(externalIncomeDTO.getFamilyMember().getId());
			auditApplicationService.audit("Add externalIncome", getUsernameFromJwt(), "Add externalIncome",
					null, convertObjectToJson(newExternalIncomeAudit), FAMILLE, CREATE);
		}

		log.debug("End service Add ExternalIncome , took {}", watch.toMS());
		return newExternalIncomeDTO;
	}

	public void deleteExternalIncome(Long id) {
		TimeWatch watch = TimeWatch.start();
		log.debug("Start service Delete ExternalIncome by ID : {}", id);
		externalIncomeRepository.deleteById(id);
		log.debug("End service Delete ExternalIncome by ID : {}, took {}", id, watch.toMS());
	}


}
