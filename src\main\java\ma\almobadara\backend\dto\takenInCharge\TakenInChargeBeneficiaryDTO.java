package ma.almobadara.backend.dto.takenInCharge;

import lombok.*;
import ma.almobadara.backend.dto.beneficiary.BankCardDTO;
import ma.almobadara.backend.dto.beneficiary.BeneficiaryDTO;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class TakenInChargeBeneficiaryDTO {

	private Long id;

	private BeneficiaryDTO beneficiary;

	private TakenInChargeDTO takenInCharge;

	private List<BankCardDTO> bankCards;

}
