package ma.almobadara.backend.service.authRefrentiel;

import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Service
@Slf4j
public class AuthenticationService {

    @Value("${ref.uri}")
    String uri;
    @Value("${ref.username}")
    String username;
    @Value("${ref.password}")
    String password;

    public String getJwtToken() {
        RestTemplate restTemplate = new RestTemplate();
        JSONObject body = new JSONObject();
        body.put("username", username);
        body.put("password", password);
        HttpHeaders headers = new HttpHeaders();
        headers.add("Accept", MediaType.APPLICATION_JSON.toString());
        headers.add("Content-Type", MediaType.APPLICATION_JSON.toString());
        String url = uri + "/authenticate";
        HttpEntity<Object> entity = new HttpEntity<>(body.toString(), headers);
        ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, entity, String.class);
        String responseBody = response.getBody();
        JSONObject result = new JSONObject(responseBody);
        return result.getString("id_token");
    }

}

