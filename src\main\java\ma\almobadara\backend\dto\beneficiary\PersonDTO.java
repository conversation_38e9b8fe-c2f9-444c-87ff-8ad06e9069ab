package ma.almobadara.backend.dto.beneficiary;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import ma.almobadara.backend.dto.CityWithRegionAndCountryDTO;
import ma.almobadara.backend.dto.family.FamilyMemberDTO;
import ma.almobadara.backend.dto.referentiel.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class PersonDTO {

	private Long id;

	private String pictureUrl;

	private String pictureBase64;

	@JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
	private MultipartFile picture;

	private String firstName;

	private String lastName;

	private String firstNameAr;

	private String lastNameAr;

	private String sex;

	private String email;

	private String phoneNumber;

	private String address;

	private String addressAr;

	private Date birthDate;

	private CityDTO city;

	private CityWithRegionAndCountryDTO info;

	private boolean deceased;
    private boolean educated;
	private String schoolLevelType;
	private String schoolName;
	private String schoolNameAr;
	private Date deathDate;

	private String deathReason;

	private String identityCode;

	private TypeIdentityDTO typeIdentity;
	private SchoolLevelDTO schoolLevel;

	private ProfessionDTO profession;
	private DeathReasonDTO deathReasonSelected;
	private AccommodationTypeDTO accommodationType;
	private AccommodationNatureDTO accommodationNature;

	private TypeKafalatDTO typeKafalat;
	private List<ServiceDTO> typePriseEnCharges;

	private CategoryBeneficiaryDTO categoryBeneficiary;
	private ServiceDTO serviceDTO;

	private List<BankCardDTO> bankCards;

	private BeneficiaryDTO beneficiary;

	private FamilyMemberDTO familyMember;

	private List<Long> typePriseEnChargeIds;

	private SourceBeneficiaryDTO sourceBeneficiary;
	private String sourceBeneficiaryComment;

}
