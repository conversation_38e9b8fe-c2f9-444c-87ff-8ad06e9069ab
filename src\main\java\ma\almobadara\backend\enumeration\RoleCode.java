package ma.almobadara.backend.enumeration;

import lombok.Getter;
import lombok.Setter;


public enum RoleCode {
    ADMIN("ADMIN"),
    ASSISTANT("ASSISTANT"),
    GESTIONNAIRE_KAFALAT("GESTIONNAIRE_KAFALAT"),
    GESTIONNAIRE_MARKETING("GESTIONNAIRE_MARKETING");

    private final String code;

    RoleCode(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public static RoleCode fromCode(String code) {
        for (RoleCode roleCode : RoleCode.values()) {
            if (roleCode.getCode().equalsIgnoreCase(code)) {
                return roleCode;
            }
        }
        throw new IllegalArgumentException("Unknown role code: " + code);
    }
}

