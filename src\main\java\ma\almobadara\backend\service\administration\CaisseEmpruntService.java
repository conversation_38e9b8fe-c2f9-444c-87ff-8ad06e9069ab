package ma.almobadara.backend.service.administration;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.administration.CaisseEmpruntDto;
import ma.almobadara.backend.dto.donation.BudgetLineDTO;
import ma.almobadara.backend.dto.donation.DonationDTO;
import ma.almobadara.backend.dto.donor.DonorDTO;
import ma.almobadara.backend.dto.referentiel.CanalDonationDTO;
import ma.almobadara.backend.dto.service.ServicesDTO;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.mapper.CaisseEmpruntMapper;
import ma.almobadara.backend.mapper.CaisseMapper;
import ma.almobadara.backend.model.administration.CaisseEmprunt;
import ma.almobadara.backend.model.donation.Donation;
import ma.almobadara.backend.model.donor.Donor;
import ma.almobadara.backend.model.service.Services;
import ma.almobadara.backend.repository.administration.CaisseEmpruntRepository;
import ma.almobadara.backend.repository.caisse.CaisseRepository;
import ma.almobadara.backend.repository.donor.DonorRepository;
import ma.almobadara.backend.repository.services.ServicesRepository;
import ma.almobadara.backend.service.Donation.DonationService;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Date;
import java.util.List;

@Service
@AllArgsConstructor
@Slf4j
public class CaisseEmpruntService {
    private final CaisseEmpruntMapper caisseEmpruntMapper;
    private final CaisseEmpruntRepository caisseEmpruntRepository;
    private final DonationService donationService;
    private final DonorRepository donorRepository;
    private final ServicesRepository servicesRepository;


    public CaisseEmpruntDto createCaisseEmprunt(CaisseEmpruntDto caisseEmpruntDto) throws TechnicalException, IOException {
        log.info("Creating Caisse Emprunt: {}", caisseEmpruntDto);
        Donor donor= donorRepository.findById(caisseEmpruntDto.getDonorId())
                .orElseThrow(() -> new RuntimeException("Donor not found with id: " + caisseEmpruntDto.getDonorId()));
        Services service = servicesRepository.findById(caisseEmpruntDto.getServiceId())
                .orElseThrow(() -> new RuntimeException("Service not found with id: " + caisseEmpruntDto.getServiceId()));
        // Map DTO to Entity
        var caisseEmprunt = caisseEmpruntMapper.toEntity(caisseEmpruntDto);
        // Save the entity
        caisseEmprunt.setDonor(donor);
        caisseEmprunt.setServices(service);
        caisseEmprunt.setStatus("entré");
        caisseEmprunt = caisseEmpruntRepository.save(caisseEmprunt);
        DonationDTO donationDTO = buildDonationFromCaisseEmprunt(caisseEmprunt);
        donationService.addDonation(donationDTO);
        log.info("Caisse Emprunt created successfully: {}", caisseEmprunt);
        return caisseEmpruntMapper.toDto(caisseEmprunt);
    }
    public DonationDTO buildDonationFromCaisseEmprunt(CaisseEmprunt caisseEmprunt) {
        BudgetLineDTO budgetLine = BudgetLineDTO.builder()
                .amount(caisseEmprunt.getAmount())
                .type("Kafalat")
                .makeItAvailable(false)
                .natureBudgetLine(false)
                .service(ma.almobadara.backend.dto.service.ServicesDTO.builder().id(caisseEmprunt.getServices().getId()).build())
                .build();
        DonationDTO donation = DonationDTO.builder()
                .donor(DonorDTO.builder().id(caisseEmprunt.getDonor().getId()).build())
                .value(caisseEmprunt.getAmount())
                .enableCurrency(false)
                .transactionNumber("Inconnu")
                .receptionDate(new Date())
                .identifiedDonor(true)
                .canalDonation(ma.almobadara.backend.dto.referentiel.CanalDonationDTO.builder().id(6L).build())
                .type("Financière")
                .budgetLines(List.of(budgetLine))
                .build();
        donation.setNonIdentifiedValue(0D);
        donation.setNonIdentifiedStatus("DISPONIBLE");
        return donation;
    }

}
