package ma.almobadara.backend.service.administration;

import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.administration.CaisseEmpruntDto;
import ma.almobadara.backend.dto.administration.CaisseEmpruntListDto;
import ma.almobadara.backend.dto.donation.BudgetLineDTO;
import ma.almobadara.backend.dto.donation.DonationDTO;
import ma.almobadara.backend.dto.donor.DonorDTO;
import ma.almobadara.backend.dto.referentiel.CanalDonationDTO;
import ma.almobadara.backend.dto.service.ServicesDTO;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.mapper.CaisseEmpruntMapper;
import ma.almobadara.backend.mapper.CaisseMapper;
import ma.almobadara.backend.model.administration.CaisseEmprunt;
import ma.almobadara.backend.model.donation.Donation;
import ma.almobadara.backend.model.donor.Donor;
import ma.almobadara.backend.model.donor.DonorPhysical;
import ma.almobadara.backend.model.donor.DonorMoral;
import ma.almobadara.backend.model.donor.DonorAnonyme;
import ma.almobadara.backend.model.service.Services;
import ma.almobadara.backend.repository.administration.CaisseEmpruntRepository;
import ma.almobadara.backend.repository.caisse.CaisseRepository;
import ma.almobadara.backend.repository.donor.DonorRepository;
import ma.almobadara.backend.repository.services.ServicesRepository;
import ma.almobadara.backend.service.Donation.DonationService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
@Slf4j
public class CaisseEmpruntService {
    private final CaisseEmpruntMapper caisseEmpruntMapper;
    private final CaisseEmpruntRepository caisseEmpruntRepository;
    private final DonationService donationService;
    private final DonorRepository donorRepository;
    private final ServicesRepository servicesRepository;
    private final EntityManager entityManager;


    public CaisseEmpruntDto createCaisseEmprunt(CaisseEmpruntDto caisseEmpruntDto) throws TechnicalException, IOException {
        log.info("Creating Caisse Emprunt: {}", caisseEmpruntDto);
        Donor donor= donorRepository.findById(caisseEmpruntDto.getDonorId())
                .orElseThrow(() -> new RuntimeException("Donor not found with id: " + caisseEmpruntDto.getDonorId()));
        Services service = servicesRepository.findById(caisseEmpruntDto.getServiceId())
                .orElseThrow(() -> new RuntimeException("Service not found with id: " + caisseEmpruntDto.getServiceId()));
        // Map DTO to Entity
        var caisseEmprunt = caisseEmpruntMapper.toEntity(caisseEmpruntDto);
        // Save the entity
        caisseEmprunt.setDonor(donor);
        caisseEmprunt.setServices(service);
        caisseEmprunt.setStatus("entré");
        caisseEmprunt = caisseEmpruntRepository.save(caisseEmprunt);
        DonationDTO donationDTO = buildDonationFromCaisseEmprunt(caisseEmprunt);
        donationService.addDonation(donationDTO);
        log.info("Caisse Emprunt created successfully: {}", caisseEmprunt);
        return caisseEmpruntMapper.toDto(caisseEmprunt);
    }
    public DonationDTO buildDonationFromCaisseEmprunt(CaisseEmprunt caisseEmprunt) {
        BudgetLineDTO budgetLine = BudgetLineDTO.builder()
                .amount(caisseEmprunt.getAmount())
                .type("Kafalat")
                .makeItAvailable(false)
                .natureBudgetLine(false)
                .service(ma.almobadara.backend.dto.service.ServicesDTO.builder().id(caisseEmprunt.getServices().getId()).build())
                .build();
        DonationDTO donation = DonationDTO.builder()
                .donor(DonorDTO.builder().id(caisseEmprunt.getDonor().getId()).build())
                .value(caisseEmprunt.getAmount())
                .enableCurrency(false)
                .transactionNumber("Inconnu")
                .receptionDate(new Date())
                .identifiedDonor(true)
                .canalDonation(ma.almobadara.backend.dto.referentiel.CanalDonationDTO.builder().id(6L).build())
                .type("Financière")
                .budgetLines(List.of(budgetLine))
                .build();
        donation.setNonIdentifiedValue(0D);
        donation.setNonIdentifiedStatus("DISPONIBLE");
        return donation;
    }

    public Page<CaisseEmpruntListDto> getAllCaisseEmprunt(int page, int size, String searchByDonorName,
                                                          String searchByStatus, LocalDateTime searchByDateEmprunt,
                                                          LocalDateTime searchByDateRemboursement) {
        log.info("Getting all CaisseEmprunt with page: {}, size: {}, searchByDonorName: {}, searchByStatus: {}, searchByDateEmprunt: {}, searchByDateRemboursement: {}",
                page, size, searchByDonorName, searchByStatus, searchByDateEmprunt, searchByDateRemboursement);

        Pageable pageable = org.springframework.data.domain.PageRequest.of(page, size);
        Page<CaisseEmprunt> caisseEmpruntPage;

        if (searchByDonorName != null || searchByStatus != null || searchByDateEmprunt != null || searchByDateRemboursement != null) {
            caisseEmpruntPage = filterCaisseEmprunt(searchByDonorName, searchByStatus, searchByDateEmprunt, searchByDateRemboursement, pageable);
        } else {
            caisseEmpruntPage = caisseEmpruntRepository.findAllOrderByUpdateDateDesc(pageable);
        }

        List<CaisseEmpruntListDto> caisseEmpruntListDtos = caisseEmpruntPage.getContent().stream()
                .map(caisseEmprunt -> {
                    CaisseEmpruntListDto dto = caisseEmpruntMapper.toListDto(caisseEmprunt);
                    // Set donor name based on donor type and service name
                    if (caisseEmprunt.getDonor() != null) {
                        dto.setDonorName(getDonorDisplayName(caisseEmprunt.getDonor()));
                        dto.setDonorId(caisseEmprunt.getDonor().getId());
                    }
                    if (caisseEmprunt.getServices() != null) {
                        dto.setServiceName(caisseEmprunt.getServices().getName());
                        dto.setServiceId(caisseEmprunt.getServices().getId());
                    }
                    return dto;
                })
                .collect(Collectors.toList());

        return new PageImpl<>(caisseEmpruntListDtos, pageable, caisseEmpruntPage.getTotalElements());
    }

    private Page<CaisseEmprunt> filterCaisseEmprunt(String searchByDonorName, String searchByStatus,
                                                   LocalDateTime searchByDateEmprunt, LocalDateTime searchByDateRemboursement,
                                                   Pageable pageable) {
        log.debug("Start service filterCaisseEmprunt with searchByDonorName: {}, searchByStatus: {}, searchByDateEmprunt: {}, searchByDateRemboursement: {}",
                searchByDonorName, searchByStatus, searchByDateEmprunt, searchByDateRemboursement);

        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<CaisseEmprunt> criteriaQuery = criteriaBuilder.createQuery(CaisseEmprunt.class);
        Root<CaisseEmprunt> root = criteriaQuery.from(CaisseEmprunt.class);

        Predicate predicate = buildPredicate(criteriaBuilder, root, searchByDonorName, searchByStatus, searchByDateEmprunt, searchByDateRemboursement);
        criteriaQuery.where(predicate);
        criteriaQuery.orderBy(criteriaBuilder.desc(root.get("updateDate")));

        TypedQuery<CaisseEmprunt> typedQuery = entityManager.createQuery(criteriaQuery);
        long totalCount = typedQuery.getResultList().size();
        typedQuery.setFirstResult((int) pageable.getOffset());
        typedQuery.setMaxResults(pageable.getPageSize());
        List<CaisseEmprunt> resultList = typedQuery.getResultList();

        log.debug("End service filterCaisseEmprunt with {} CaisseEmprunt found", totalCount);
        return new PageImpl<>(resultList, pageable, totalCount);
    }

    private Predicate buildPredicate(CriteriaBuilder criteriaBuilder, Root<CaisseEmprunt> root,
                                   String searchByDonorName, String searchByStatus,
                                   LocalDateTime searchByDateEmprunt, LocalDateTime searchByDateRemboursement) {
        Predicate predicate = criteriaBuilder.conjunction();

        // Filter by donor name - handle different donor types (following DonationService pattern)
        if (searchByDonorName != null && !searchByDonorName.trim().isEmpty()) {
            Join<CaisseEmprunt, Donor> donorJoin = root.join("donor", JoinType.LEFT);

            // Search across all donor types using OR condition (similar to DonationService approach)
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.or(
                    // Search in physical donor fields (firstName, lastName)
                    criteriaBuilder.like(criteriaBuilder.lower(donorJoin.get("firstName")), "%" + searchByDonorName.toLowerCase() + "%"),
                    criteriaBuilder.like(criteriaBuilder.lower(donorJoin.get("lastName")), "%" + searchByDonorName.toLowerCase() + "%"),
                    // Search in moral donor field (company)
                    criteriaBuilder.like(criteriaBuilder.lower(donorJoin.get("company")), "%" + searchByDonorName.toLowerCase() + "%"),
                    // Search in anonymous donor field (name)
                    criteriaBuilder.like(criteriaBuilder.lower(donorJoin.get("name")), "%" + searchByDonorName.toLowerCase() + "%")
            ));
        }

        // Filter by status
        if (searchByStatus != null && !searchByStatus.trim().isEmpty()) {
            predicate = criteriaBuilder.and(predicate,
                criteriaBuilder.like(criteriaBuilder.lower(root.get("status")),
                    "%" + searchByStatus.toLowerCase() + "%"));
        }

        // Filter by date emprunt
        if (searchByDateEmprunt != null) {
            predicate = criteriaBuilder.and(predicate,
                criteriaBuilder.equal(
                    criteriaBuilder.function("DATE", LocalDateTime.class, root.get("dateEmprunt")),
                    criteriaBuilder.function("DATE", LocalDateTime.class, criteriaBuilder.literal(searchByDateEmprunt))
                ));
        }

        // Filter by date remboursement
        if (searchByDateRemboursement != null) {
            predicate = criteriaBuilder.and(predicate,
                criteriaBuilder.equal(
                    criteriaBuilder.function("DATE", LocalDateTime.class, root.get("dateRemboursement")),
                    criteriaBuilder.function("DATE", LocalDateTime.class, criteriaBuilder.literal(searchByDateRemboursement))
                ));
        }

        return predicate;
    }

    /**
     * Get the appropriate display name for a donor based on its type (following DonationService pattern)
     */
    private String getDonorDisplayName(Donor donor) {
        if (donor instanceof DonorPhysical donorPhysical) {
            return donorPhysical.getFirstName() + " " + donorPhysical.getLastName();
        } else if (donor instanceof DonorMoral donorMoral) {
            return donorMoral.getCompany();
        } else if (donor instanceof DonorAnonyme donorAnonyme) {
            return donorAnonyme.getName();
        }
        // Fallback to label if type is not recognized
        return donor.getLabel() != null ? donor.getLabel() : "";
    }

}
