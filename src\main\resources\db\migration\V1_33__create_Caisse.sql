-- Création de la table Caisse
CREATE TABLE caisse (
                        id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
                        code VA<PERSON>HA<PERSON>(255) UNIQUE NOT NULL,
                        name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
                        type_prise_en_charge_id BIGINT NOT NULL,
                        comment TEXT,
                        created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                        is_deleted BOOLEAN DEFAULT FALSE
);

ALTER TABLE aide_complementaire
    ADD COLUMN caisse_id BIGINT;


ALTER TABLE aide_complementaire
    ADD CONSTRAINT fk_aide_complementaire_caisse
        FOREIGN KEY (caisse_id) REFERENCES caisse(id)
            ON DELETE CASCADE;


CREATE INDEX idx_caisse_code ON caisse(code);
