-- 1. Ins<PERSON><PERSON> le type_tag 'metie' s'il n'existe pas déjà
INSERT INTO type_tag (name, read_only, description)
SELECT 'metier', TRUE, 'Tags for metier type (read only)'
WHERE NOT EXISTS (
    SELECT 1 FROM type_tag WHERE name = 'metier'
);

-- 2. Ins<PERSON>rer le tag 'migration' s'il n'existe pas déjà
INSERT INTO tag (name, color, type_tag_id)
SELECT 'migration', '000000', id
FROM type_tag
WHERE name = 'metier' AND read_only = TRUE
  AND NOT EXISTS (
    SELECT 1 FROM tag WHERE name = 'migration' AND type_tag_id = type_tag.id
);

-- 3. Insérer le tag 'à_compléter' s'il n'existe pas déjà
INSERT INTO tag (name, color, type_tag_id)
SELECT 'à_compléter', '000000', id
FROM type_tag
WHERE name = 'metier' AND read_only = TRUE
  AND NOT EXISTS (
    SELECT 1 FROM tag WHERE name = 'à_compléter' AND type_tag_id = type_tag.id
);
