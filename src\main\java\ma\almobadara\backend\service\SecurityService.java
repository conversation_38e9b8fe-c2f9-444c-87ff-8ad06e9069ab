package ma.almobadara.backend.service;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.model.administration.CacheAdUser;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.stereotype.Component;

@AllArgsConstructor
@Slf4j
@Component("securityCustomService")
public class SecurityService {

    private static String getUserId(Authentication authentication) {
        Object oid = null;

        // If the authentication token is a JwtAuthenticationToken
        if (authentication instanceof JwtAuthenticationToken jwtAuthentication) {
            // Extract the 'oid' from the JWT claims
            oid = jwtAuthentication.getToken().getClaims().get("oid");
        }
        // If the authentication token is a UsernamePasswordAuthenticationToken
        else if (authentication instanceof UsernamePasswordAuthenticationToken usernamePasswordAuthenticationToken) {
            // Access the principal, which could be a UserDetails object or a custom user object
            Object user = usernamePasswordAuthenticationToken.getPrincipal();

            // Ensure user is an instance of the expected class (e.g., CacheAdUser)
            if (user instanceof CacheAdUser) {
                // Access the custom attribute (in this case, azureDirectoryId) from CacheAdUser
                oid = ((CacheAdUser) user).getAzureDirectoryId();
            }
        }

        // Return the 'oid' as a string, or null if it's not available
        return oid != null ? oid.toString() : null;
    }



    public String getAuthenticatedAzureId() {
                Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
                return getUserId(authentication);
            }

            public String getAuthenticatedUserEmail() {
                Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
                if (authentication instanceof JwtAuthenticationToken jwtAuthentication) {
                    Object email = jwtAuthentication.getToken().getClaims().get("upn");
                    if (email != null) {
                        return email.toString();
            }
        }
        return null;
    }

}
