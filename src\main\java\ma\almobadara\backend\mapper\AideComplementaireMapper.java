package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.aideComplemenatire.AddAideComplementaireDTO;
import ma.almobadara.backend.dto.aideComplemenatire.AideComplementaireDTO;
import ma.almobadara.backend.dto.beneficiary.AideComplementaireListDto;
import ma.almobadara.backend.model.aideComplemenatire.AideComplementaire;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public interface AideComplementaireMapper {
    @Mapping(target ="createdAt", ignore = true)
    @Mapping(target ="modifiedAt", ignore = true)
    @Mapping(target ="services", ignore = true)
    @Mapping(target ="serviceId", source = "service.id")
    AideComplementaireDTO toDto(AideComplementaire aideComplementaire);


    AideComplementaireListDto toListDto(AideComplementaire aideComplementaire);

    List<AideComplementaireDTO> toDtoList(List<AideComplementaire> aideComplementaires);

    @Mapping(target ="createdAt", ignore = true)
    @Mapping(target ="modifiedAt", ignore = true)
    @Mapping(target ="service.id", source = "serviceId")
    AideComplementaire toEntity(AideComplementaireDTO aideComplementaireDTO);

    @Mapping(target ="createdAt", ignore = true)
    @Mapping(target ="modifiedAt", ignore = true)
    @Mapping(target ="service.id", source = "serviceId")
    AideComplementaire AddDtotoEntity(AddAideComplementaireDTO aideComplementaireDTO);
}
