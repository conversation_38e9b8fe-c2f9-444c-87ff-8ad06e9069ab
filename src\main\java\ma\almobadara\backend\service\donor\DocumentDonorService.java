package ma.almobadara.backend.service.donor;

import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.donor.DocumentDonorDto;
import ma.almobadara.backend.dto.donor.DonorDocumentDetailsDTO;
import ma.almobadara.backend.mapper.DocumentDonorDetailsMapper;
import ma.almobadara.backend.mapper.DonorMapper;
import ma.almobadara.backend.model.donor.DocumentDonor;
import ma.almobadara.backend.model.donor.Donor;
import ma.almobadara.backend.repository.communs.DocumentDonorRepository;
import ma.almobadara.backend.repository.donor.DonorRepository;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class DocumentDonorService {

    private final DocumentDonorRepository documentDonorRepository;
    private final DonorRepository donorRepository;
    private final DonorMapper donorMapper;
    private final DocumentDonorDetailsMapper documentDonorDetailsMapper;


    public DocumentDonorDto getDocumentDonorById(Long documentDonorId) {
        log.debug("Start service Get DocumentDonor by ID: {}", documentDonorId);

        DocumentDonor documentDonorEntity = documentDonorRepository.findById(documentDonorId)
                .orElseThrow(() -> new EntityNotFoundException("DocumentDonor not found with ID: " + documentDonorId));

        documentDonorEntity.getDocument().getTypeDocumentId();

        DocumentDonorDto documentDonorDto = donorMapper.documentDonorEntityToDto(documentDonorEntity);

        log.debug("End service Get DocumentDonor");
        return documentDonorDto;
    }

   public List<DonorDocumentDetailsDTO> getDocumentsByDonorId(Long donorId) {
       log.debug("Start service Get Documents by Donor ID: {}", donorId);
       Optional<Donor> optionalDonor = donorRepository.findById(donorId);
       if (optionalDonor.isPresent()) {
           Donor donor = optionalDonor.get();
           List<DocumentDonor> documentDonors = donor.getDocumentsDonors();
           List<DonorDocumentDetailsDTO> donorDocumentDetailsDTOList =
                   documentDonorDetailsMapper.documentDonorListToDtoList(documentDonors);

           log.debug("End service Get Documents by Donor ID");
           return donorDocumentDetailsDTOList;
       } else {
           // Gérez le cas où le Donor n'est pas trouvé, vous pouvez renvoyer null ou une erreur
           return null;
       }
   }

}
