package ma.almobadara.backend.controller.beneficiary;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.beneficiary.ScholarshipBeneficiaryDTO;
import ma.almobadara.backend.mapper.ScholarshipBeneficiaryMapper;
import ma.almobadara.backend.model.beneficiary.Beneficiary;
import ma.almobadara.backend.model.beneficiary.ScholarshipBeneficiary;
import ma.almobadara.backend.repository.beneficiary.BeneficiaryRepository;
import ma.almobadara.backend.service.beneficiary.ScholarshipBeneficiaryService;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.rest.webmvc.ResourceNotFoundException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static ma.almobadara.backend.service.administration.CacheAdUserService.logUserInfo;

@RefreshScope
@RestController
@AllArgsConstructor
@Slf4j
@RequestMapping("/scholarshipsBeneficiaries")
public class ScholarshipBeneficiaryController {

    private final ScholarshipBeneficiaryService scholarshipService;
    private final BeneficiaryRepository beneficiaryRepository;
    private final ScholarshipBeneficiaryMapper scholarshipBeneficiaryMapper;

    @PostMapping("/{beneficiaryId}")
    public ResponseEntity<ScholarshipBeneficiaryDTO> addScholarshipToBeneficiary(@PathVariable Long beneficiaryId, @RequestBody ScholarshipBeneficiaryDTO scholarshipBeneficiaryDTO) {

        logUserInfo("addScholarshipToBeneficiary", String.valueOf(beneficiaryId));

        ScholarshipBeneficiaryDTO createdScholarshipBeneficiaryDTO;
        HttpStatus status;
        try {
            Beneficiary beneficiary = beneficiaryRepository.findById(beneficiaryId)
                    .orElseThrow(() -> new ResourceNotFoundException("Beneficiary not found with id: " + beneficiaryId));

            ScholarshipBeneficiary scholarshipBeneficiary = scholarshipService.addScholarshipToBeneficiary(beneficiary, scholarshipBeneficiaryDTO);
            createdScholarshipBeneficiaryDTO = scholarshipBeneficiaryMapper.scholarshipBeneficiaryToScholarshipBeneficiaryDTO(scholarshipBeneficiary);
            status = HttpStatus.OK;
            log.info("End service created scholarship by ID : {}, OK", beneficiaryId);
        } catch (ResourceNotFoundException e) {
            createdScholarshipBeneficiaryDTO = null;
            status = HttpStatus.NOT_FOUND;
            log.error("End service created scholarship by ID : {}, KO: {}", beneficiaryId, e.getMessage());
        }

        return new ResponseEntity<>(createdScholarshipBeneficiaryDTO, new HttpHeaders(), status);
    }

    @GetMapping("/{beneficiaryId}/list-scholarship")
    public ResponseEntity<List<ScholarshipBeneficiaryDTO>> getScholarshipByBeneficiaryId(@PathVariable Long beneficiaryId) {

        logUserInfo("getScholarshipByBeneficiaryId", String.valueOf(beneficiaryId));

        List<ScholarshipBeneficiaryDTO> scholarshipBeneficiaryDTOS;
        HttpStatus status;
        try {
            scholarshipBeneficiaryDTOS = scholarshipService.getScholarshipByBeneficiaryId(beneficiaryId);
            status = HttpStatus.OK;
            log.info("End service get scholarship by ID : {}, OK", beneficiaryId);
        } catch (ResourceNotFoundException e) {
            scholarshipBeneficiaryDTOS = null;
            status = HttpStatus.NOT_FOUND;
            log.error("End service get scholarship by ID : {}, KO: {}", beneficiaryId, e.getMessage());
        }

        return new ResponseEntity<>(scholarshipBeneficiaryDTOS, status);
    }

    @DeleteMapping(value = "{idScholarship}", headers = "Accept=application/json")
    @Operation(summary = "Delete scholarship", description = "delete education", tags = {"SIG"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "successful operation", content = @Content(schema = @Schema()))})
    public ResponseEntity<Void> deleteScholarship(@PathVariable Long idScholarship) {

        logUserInfo("deleteScholarship", String.valueOf(idScholarship));

        HttpStatus status;
        try {
            scholarshipService.deleteScholarship(idScholarship);
            status = HttpStatus.NO_CONTENT;
            log.info("End resource Delete scholarship with ID: {}, OK", idScholarship);
        } catch (ResourceNotFoundException e) {
            status = HttpStatus.NOT_FOUND;
            log.error("End resource Delete scholarship with ID: {}, KO: {}", idScholarship, e.getMessage());
        }

        return new ResponseEntity<>(status);
    }

}
