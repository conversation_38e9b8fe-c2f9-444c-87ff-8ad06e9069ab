package ma.almobadara.backend.service.graph;


import com.azure.identity.ClientSecretCredential;
import com.azure.identity.ClientSecretCredentialBuilder;
import com.microsoft.graph.authentication.TokenCredentialAuthProvider;
import com.microsoft.graph.requests.GraphServiceClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.properties.AzureEnterpriseApp;
import okhttp3.Request;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class GraphClientService {

    private final AzureEnterpriseApp azureProperties;

    @Bean
    public GraphServiceClient<Request> getGraphClient() {
        ClientSecretCredential clientSecretCredential = new ClientSecretCredentialBuilder()
                .clientId(azureProperties.getClientId())
                .clientSecret(azureProperties.getClientSecret())
                .tenantId(azureProperties.getTenantId())
                .build();

        TokenCredentialAuthProvider tokenCredentialAuthProvider =
                new TokenCredentialAuthProvider(clientSecretCredential);

        return GraphServiceClient
                .builder()
                .authenticationProvider(tokenCredentialAuthProvider)
                .buildClient();
    }

}
