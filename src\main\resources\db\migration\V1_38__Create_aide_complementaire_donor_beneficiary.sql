CREATE TABLE aide_complementaire_donor_beneficiary (
                                                       id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
                                                       aide_complementaire_id BIGINT NOT NULL,
                                                       donor_id BIGINT NOT NULL,
                                                       beneficiary_id BIGINT NOT NULL,
                                                       <PERSON><PERSON><PERSON><PERSON><PERSON> (aide_complementaire_id) REFERENCES aide_complementaire(id),
                                                       <PERSON>OR<PERSON><PERSON><PERSON>EY (donor_id) REFERENCES donor(id),
                                                       <PERSON>OR<PERSON><PERSON><PERSON> KEY (beneficiary_id) REFERENCES beneficiary(id)
);
