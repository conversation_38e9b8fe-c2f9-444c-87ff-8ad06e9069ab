package ma.almobadara.backend.controller.family;

import com.fasterxml.jackson.databind.ObjectMapper;
import ma.almobadara.backend.config.Messages;
import ma.almobadara.backend.config.TestSecurityConfig;
import ma.almobadara.backend.dto.family.FamilyDTO;
import ma.almobadara.backend.service.family.FamilyService;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(FamilyController.class)
@ExtendWith(MockitoExtension.class)
@Import({TestSecurityConfig.class, Messages.class})
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class FamilyControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private FamilyService familyService;

    private final ObjectMapper objectMapper = new ObjectMapper();

        @Test
        void createFamily_ShouldReturnOk_WhenFamilyIsCreated() throws Exception {
            // Given
            FamilyDTO familyRequest = new FamilyDTO(); // Remplissez avec des données appropriées
            FamilyDTO createdFamilyResponse = new FamilyDTO(); // Remplissez avec des données appropriées

            when(familyService.addFamily(any(FamilyDTO.class))).thenReturn(createdFamilyResponse);

            // When & Then
            mockMvc.perform(post("/families")
                            .flashAttr("familyRequest", familyRequest)
                            .contentType(MediaType.MULTIPART_FORM_DATA))
                    .andExpect(status().isOk())
                    .andExpect(content().json(objectMapper.writeValueAsString(createdFamilyResponse)));
        }

        @Test
        void createFamily_ShouldReturnInternalServerError_WhenExceptionOccurs() throws Exception {
            // Given
            FamilyDTO familyRequest = new FamilyDTO();

            when(familyService.addFamily(any(FamilyDTO.class))).thenThrow(new RuntimeException("Internal Server Error"));

            // When & Then
            mockMvc.perform(post("/families")
                            .flashAttr("familyRequest", familyRequest)
                            .contentType(MediaType.MULTIPART_FORM_DATA))
                    .andExpect(status().isInternalServerError());
        }

    @Test
    void getAllFamilies_ShouldReturnInternalServerError_WhenExceptionOccurs() throws Exception {
        // Given
        when(familyService.getAllFamilies(any(), any(), any(), any(), any(), any(), any(), any(),any(),any()))
                .thenThrow(new RuntimeException("Internal Server Error"));

        // When & Then
        mockMvc.perform(get("/families")
                        .param("page", "1")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isInternalServerError());
    }

    @Test
    void getFamilyById_ShouldReturnOk_WhenFamilyIsFound() throws Exception {
        // Given
        Long familyId = 1L;
        FamilyDTO familyDTO = new FamilyDTO(); // Remplissez avec des données appropriées

        when(familyService.getFamilyById(familyId)).thenReturn(familyDTO);

        // When & Then
        mockMvc.perform(get("/families/{idFamily}", familyId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(familyDTO)));
    }

    @Test
    void getFamilyById_ShouldReturn404_WhenFamilyIsFound() throws Exception {
        // Given
        Long familyId = 1L;
        FamilyDTO familyDTO = new FamilyDTO(); // Remplissez avec des données appropriées

        // When & Then
        mockMvc.perform(get("/families/{idFamily}", familyId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(familyDTO)));
    }

    @Test
    void getFamilyForBeneficiary_ShouldReturnOk_WhenFamilyIsFound() throws Exception {
        // Given
        Long beneficiaryId = 1L;
        FamilyDTO familyDTO = new FamilyDTO(); // Remplissez avec des données appropriées
        when(familyService.getFamilyForOneBeneficiary(beneficiaryId)).thenReturn(familyDTO);

        // When & Then
        mockMvc.perform(get("/families/beneficiary/{beneficiaryId}", beneficiaryId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(familyDTO)));
    }

    @Test
    void getFamilyForBeneficiary_ShouldReturnOk_WhenFamilyIsNotFound() throws Exception {
        // Given
        Long beneficiaryId = 2L;
        when(familyService.getFamilyForOneBeneficiary(beneficiaryId)).thenReturn(null);

        // When & Then
        mockMvc.perform(get("/families/beneficiary/{beneficiaryId}", beneficiaryId)
                        .contentType(MediaType.APPLICATION_JSON))
                        .andExpect(status().isOk());
    }




}
