package ma.almobadara.backend.controller.administration;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.administration.CaisseEmpruntDto;
import ma.almobadara.backend.dto.administration.CaisseEmpruntDetailDto;
import ma.almobadara.backend.dto.administration.CaisseEmpruntListDto;
import ma.almobadara.backend.dto.administration.EpsDto;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.service.administration.CaisseEmpruntService;
import org.springframework.data.domain.Page;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.time.LocalDateTime;

@RestController
@RequestMapping("/caisses-emprunt")
@Slf4j
@AllArgsConstructor
public class CaisseEmpruntController {
    private final CaisseEmpruntService caisseEmpruntService;

    @PostMapping
    public ResponseEntity<?> createCaisseEmprunt(@RequestBody CaisseEmpruntDto caisseEmpruntDto) {
        log.info("Start resource createCaisseEmprunt with id: {}", caisseEmpruntDto.getId());

        try {
            CaisseEmpruntDto result = caisseEmpruntService.createCaisseEmprunt(caisseEmpruntDto);
            log.info("End resource createCaisseEmprunt with id: {}", result.getId());
            return ResponseEntity.status(HttpStatus.CREATED).body(result);

        } catch (TechnicalException | IOException e) {
            log.error("TechnicalException in createCaisseEmprunt: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Une erreur technique s'est produite. Veuillez réessayer plus tard.");

        } catch (IllegalArgumentException e) {
            log.warn("Bad request in createCaisseEmprunt: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body("Données invalides : " + e.getMessage());

        } catch (RuntimeException e) {
            log.error("Unexpected RuntimeException in createCaisseEmprunt: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Une erreur inattendue s'est produite.");
        }
    }

    @GetMapping
    public ResponseEntity<Page<CaisseEmpruntListDto>> getAllCaisseEmprunt(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "5") int size,
            @RequestParam(required = false) final String searchByDonorName,
            @RequestParam(required = false) final String searchByStatus,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) final LocalDateTime searchByDateEmprunt,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) final LocalDateTime searchByDateRemboursement
    ) {
        log.info("Start resource getAllCaisseEmprunt with filters - page: {}, size: {}, donorName: {}, status: {}, dateEmprunt: {}, dateRemboursement: {}",
                page, size, searchByDonorName, searchByStatus, searchByDateEmprunt, searchByDateRemboursement);

        Page<CaisseEmpruntListDto> caisseEmpruntList = caisseEmpruntService.getAllCaisseEmprunt(
                page, size, searchByDonorName, searchByStatus, searchByDateEmprunt, searchByDateRemboursement);

        log.info("End resource getAllCaisseEmprunt with {} results", caisseEmpruntList.getTotalElements());
        return ResponseEntity.ok(caisseEmpruntList);
    }

    @GetMapping("/{id}")
    public ResponseEntity<?> getCaisseEmpruntById(@PathVariable Long id) {
        log.info("Start resource getCaisseEmpruntById with ID: {}", id);

        try {
            CaisseEmpruntDetailDto result = caisseEmpruntService.getCaisseEmpruntDetailById(id);
            log.info("End resource getCaisseEmpruntById with ID: {}", id);
            return ResponseEntity.ok(result);

        } catch (TechnicalException e) {
            log.error("TechnicalException in getCaisseEmpruntById: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body("CaisseEmprunt not found with ID: " + id);

        } catch (RuntimeException e) {
            log.error("Unexpected RuntimeException in getCaisseEmpruntById: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Une erreur inattendue s'est produite.");
        }
    }

}
