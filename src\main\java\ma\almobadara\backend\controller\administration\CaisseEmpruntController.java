package ma.almobadara.backend.controller.administration;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.administration.CaisseEmpruntDto;
import ma.almobadara.backend.dto.administration.EpsDto;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.service.administration.CaisseEmpruntService;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

@RestController
@RequestMapping("/caisses-emprunt")
@Slf4j
@AllArgsConstructor
public class CaisseEmpruntController {
    private final CaisseEmpruntService caisseEmpruntService;

    @PostMapping
    public ResponseEntity<?> createCaisseEmprunt(@RequestBody CaisseEmpruntDto caisseEmpruntDto) {
        log.info("Start resource createCaisseEmprunt with id: {}", caisseEmpruntDto.getId());

        try {
            CaisseEmpruntDto result = caisseEmpruntService.createCaisseEmprunt(caisseEmpruntDto);
            log.info("End resource createCaisseEmprunt with id: {}", result.getId());
            return ResponseEntity.status(HttpStatus.CREATED).body(result);

        } catch (TechnicalException | IOException e) {
            log.error("TechnicalException in createCaisseEmprunt: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Une erreur technique s'est produite. Veuillez réessayer plus tard.");

        } catch (IllegalArgumentException e) {
            log.warn("Bad request in createCaisseEmprunt: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body("Données invalides : " + e.getMessage());

        } catch (RuntimeException e) {
            log.error("Unexpected RuntimeException in createCaisseEmprunt: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Une erreur inattendue s'est produite.");
        }
    }


}
