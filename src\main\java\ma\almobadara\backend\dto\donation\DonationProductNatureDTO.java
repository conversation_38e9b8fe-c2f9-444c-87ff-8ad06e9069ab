package ma.almobadara.backend.dto.donation;

import lombok.*;
import ma.almobadara.backend.dto.referentiel.ProductNatureDTO;
import ma.almobadara.backend.dto.referentiel.ProductUnitDTO;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class DonationProductNatureDTO  {

	protected Long id;

	private DonationDTO donation;

	private ProductNatureDTO productNature;

	private double unitPrice;

	private int quantity;

	private ProductUnitDTO productUnit;

}
