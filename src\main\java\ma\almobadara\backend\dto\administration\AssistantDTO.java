package ma.almobadara.backend.dto.administration;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import ma.almobadara.backend.dto.beneficiary.SmallZoneDTO;
import ma.almobadara.backend.dto.referentiel.LanguageCommunicationDTO;
import ma.almobadara.backend.dto.referentiel.SchoolLevelDTO;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
public class AssistantDTO {
    private Long id;
    private String code;
    private Long cityId;
    private String address;
    private LocalDateTime creationDate;
    private LocalDateTime updateDate;
    private Long zoneId;
    private List<Long> zoneIds;
    private CacheAdUserDTO cacheAdUser;
    private Long cacheAdUserId;
    private SmallZoneDTO zone;
    private List<SmallZoneDTO> zones;
    private boolean hasBeneficiaries;
    private LocalDate dateAffectationToZone;
    private LocalDate dateEndAffectationToZone;
    private String firstName;
    private String lastName;
    private String email;
    private Date birthDate;
    //private LanguageCommunicationDTO languageCommunication;
    private List<Long> languageCommunicationIds;
    private List<LanguageCommunicationDTO> languageCommunicationDetails;
    private SchoolLevelDTO schoolLevel;
    private boolean status;
    private String pictureUrl;

    private String picture64;

    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private MultipartFile picture;

    private String cinNumber;
    private String phone;
    private Boolean oldAssistant;
    private String password;
    private String device_token;

    // Additional information that can be added dynamically
    private Map<String, Object> additionalInfo;

    private List<String> zoneNames;
}
