package ma.almobadara.backend.dto.referentiel;

import lombok.*;
import org.springframework.hateoas.RepresentationModel;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class TypeProductNatureDTO extends RepresentationModel<TypeProductNatureDTO> implements Serializable {

	private Long id;

	private String code;
	private String name;
	private String nameAr;
	private String nameEn;

	private List<ProductNatureDTO> productNatures;

}
