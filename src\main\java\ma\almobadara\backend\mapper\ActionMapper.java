package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.communs.ActionAuditDTO;
import ma.almobadara.backend.dto.communs.ActionDTO;
import ma.almobadara.backend.dto.communs.NoteAuditDTO;
import ma.almobadara.backend.model.communs.Action;
import ma.almobadara.backend.model.communs.Note;
import ma.almobadara.backend.model.donor.ActionDonor;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface ActionMapper {


    @Mapping(source = "status", target = "actionStatus.id")
    ActionDTO actionModelToDto(Action donor);

    @Mapping(source = "status", target = "actionStatus.id")
    Iterable<ActionDTO> actionListModelToDto(Iterable<Action> donors);

    @Mapping(source = "actionStatus.id", target = "status")
    Action actionDtoModelToModel(ActionDTO actionDTO);


    @Mapping(source = "dateEntry", target = "DateDeCreation")
    @Mapping(source = "deadline", target = "DateLimite")
    @Mapping(source = "dateRealize", target = "DateDeRealisation")
    @Mapping(source = "subject", target = "Objet")
    @Mapping(target = "InitierPar", ignore = true)
    @Mapping(target = "AffecterA", ignore = true)
    @Mapping(target = "Statut", ignore = true)
    ActionAuditDTO actionToActionAuditDto(Action action);

}
