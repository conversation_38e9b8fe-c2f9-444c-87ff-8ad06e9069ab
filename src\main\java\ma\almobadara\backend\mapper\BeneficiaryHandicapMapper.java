package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.beneficiary.BeneficiaryHandicapDto;
import ma.almobadara.backend.dto.beneficiary.HandicapBeneficiaryAddDto;
import ma.almobadara.backend.model.beneficiary.BeneficiaryHandicap;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public interface BeneficiaryHandicapMapper {
    @Mapping(source = "handicapTypeId", target = "handicapType.id")
    BeneficiaryHandicapDto beneficiaryHandicapToBeneficiaryHandicapDto(BeneficiaryHandicap beneficiaryHandicap);

    Iterable<BeneficiaryHandicapDto> beneficiaryHandicapToBeneficiaryHandicapDto(Iterable<BeneficiaryHandicap> beneficiaryHandicaps);

    @Mapping(source = "handicapType.id", target = "handicapTypeId")
    BeneficiaryHandicap beneficiaryHandicapDtoToBeneficiaryHandicap(BeneficiaryHandicapDto beneficiaryHandicapDto);

    Iterable<BeneficiaryHandicap> beneficiaryHandicapDtoToBeneficiaryHandicap(Iterable<BeneficiaryHandicapDto> beneficiaryHandicapDtos);

    Iterable<BeneficiaryHandicap> beneficiaryHandicapDtoToBeneficiaryHandicap(List<HandicapBeneficiaryAddDto> handicapBeneficiaryAddDtoList);

    BeneficiaryHandicap HandicapBeneficiaryAddDtoToBeneficiaryHandicap(HandicapBeneficiaryAddDto handicapBeneficiaryAddDto);

}
