package ma.almobadara.backend.repository.donation;


import ma.almobadara.backend.model.donation.DocumentDonation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface DocumentDonationRepository extends JpaRepository<DocumentDonation, Long> {
    List<DocumentDonation> findByDonationId(Long donationId  );
    Optional<DocumentDonation> findByDonationIdAndDocumentId(Long donationId, Long documentId);

    List<DocumentDonation> findByDocumentId(Long documentId);

}