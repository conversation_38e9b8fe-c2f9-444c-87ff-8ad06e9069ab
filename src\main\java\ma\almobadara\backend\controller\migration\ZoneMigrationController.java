package ma.almobadara.backend.controller.migration;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.mapper.ZoneMapperImpl;
import ma.almobadara.backend.service.migration.AssistantZoneMigrationService;
import ma.almobadara.backend.service.migration.ZoneMigrationService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/zone-migration")
@Slf4j
@AllArgsConstructor
public class ZoneMigrationController {


    private final ZoneMigrationService zoneMigrationService;

    @PostMapping("/upload")
    public ResponseEntity<String> migrateZoneAssistant(@RequestParam("file") MultipartFile file) throws TechnicalException {
        log.info("Request to migrateZoneAssistant : {}", file);
        zoneMigrationService.migrateZone(file);
        return ResponseEntity.ok().body("Migration Done");
    }
}