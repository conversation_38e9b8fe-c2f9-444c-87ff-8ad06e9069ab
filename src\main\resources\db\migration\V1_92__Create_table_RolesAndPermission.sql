-- V1__Create_role_and_permission_tables.sql
-- This script creates the 'role' and 'permission' tables

CREATE TABLE role (
                      id           BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
                      name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL UNIQUE
);

CREATE TABLE permission (
                            id           BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
                            name VA<PERSON>HA<PERSON>(255) NOT NULL UNIQUE,
                            description VARCHAR(255) NOT NULL
);

-- This table links roles and permissions, allowing many-to-many relationships
CREATE TABLE role_permissions (
                                  role_id BIGINT NOT NULL,
                                  permission_id BIGINT NOT NULL,
                                  PRIMARY KEY (role_id, permission_id),
                                  <PERSON>OR<PERSON><PERSON><PERSON> KEY (role_id) REFERENCES role(id) ON DELETE CASCADE,
                                  FOREIG<PERSON> KEY (permission_id) REFERENCES permission(id) ON DELETE CASCADE
);

-- Inserts roles and permissions data, then establishes their relationships

-- Insert roles into the 'role' table
INSERT INTO role (name) VALUES ('assistant');
INSERT INTO role (name) VALUES ('admin');

-- Insert permissions into the 'permission' table
INSERT INTO permission (name, description) VALUES ('valider_assistant_initial', 'Valider la demande initiale de l’assistant');
INSERT INTO permission (name, description) VALUES ('rendre_candidat_a_completer', 'Marquer le candidat comme complété');

-- Establish role-permission relationships in the 'role_permissions' table
-- Link 'assistant' role to 'valider_assistant_initial' permission
INSERT INTO role_permissions (role_id, permission_id)
VALUES (
           (SELECT id FROM role WHERE name = 'assistant'),
           (SELECT id FROM permission WHERE name = 'valider_assistant_initial')
       );

-- Link 'admin' role to both permissions
INSERT INTO role_permissions (role_id, permission_id)
VALUES (
           (SELECT id FROM role WHERE name = 'admin'),
           (SELECT id FROM permission WHERE name = 'valider_assistant_initial')
       ),
       (
           (SELECT id FROM role WHERE name = 'admin'),
           (SELECT id FROM permission WHERE name = 'rendre_candidat_a_completer')
       );

