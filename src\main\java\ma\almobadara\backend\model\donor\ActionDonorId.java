package ma.almobadara.backend.model.donor;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Objects;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ActionDonorId implements Serializable {

    private Long donor;
    private Long action;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ActionDonorId that = (ActionDonorId) o;
        return Objects.equals(donor, that.donor) && Objects.equals(action, that.action);
    }

    @Override
    public int hashCode() {
        return Objects.hash(donor, action);
    }

}
