package ma.almobadara.backend.repository.administration;

import ma.almobadara.backend.model.administration.SousZone;
import ma.almobadara.backend.model.administration.Zone;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SousZoneRepository extends JpaRepository<SousZone, Long> {

    //findByZone
    List<SousZone> findByZone(Zone zone);

   //countByCodeStartingWith
    int countByCodeStartingWith(String code);

  //existsByCode
    boolean existsByCode(String code);



}
