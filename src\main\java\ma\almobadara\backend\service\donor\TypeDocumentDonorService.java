package ma.almobadara.backend.service.donor;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.referentiel.TypeDocumentDonorDTO;
import ma.almobadara.backend.service.authRefrentiel.AuthenticationService;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

@Service
@AllArgsConstructor
@Slf4j
public class TypeDocumentDonorService {

    private final AuthenticationService authenticationService;

    @Cacheable(value = "TypeDocumentDonorList")
    public List<TypeDocumentDonorDTO> getAllTypeDocumentDonor() {
        log.debug("Start service Get All TypeDocumentDonor {}", "");

        String token = authenticationService.getJwtToken();
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.add("Authorization", "Bearer " + token);
        String url = "http://localhost:8080/api/type_donor_documents";
        HttpEntity<Object> entity = new HttpEntity<>(headers);
        TypeDocumentDonorDTO[] result = restTemplate.exchange(url, HttpMethod.GET, entity, TypeDocumentDonorDTO[].class).getBody();

        assert result != null;
        Iterable<TypeDocumentDonorDTO> typeDocumentDonorDTOS = Arrays.asList(result);

        List<TypeDocumentDonorDTO> list = StreamSupport.stream(typeDocumentDonorDTOS.spliterator(), false).collect(Collectors.toList());

        log.debug("End service Get All TypeDocumentDonor {}", "");
        return list;
    }

    @Cacheable(value = "TypeDocumentDonor", key = "#id")
    public TypeDocumentDonorDTO getTypeDocumentDonorById(Long id)  {
        log.debug("Start service Get TypeDocumentDonor by ID: {}", id);

        //Consuming master data
        String token = authenticationService.getJwtToken();

        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.add("Authorization", "Bearer " + token);
        String url ="http://localhost:8080/api/type_donor_documents/" + id.toString();
        HttpEntity<Object> entity = new HttpEntity<>(headers);
        //API call
        TypeDocumentDonorDTO result = restTemplate.exchange(url, HttpMethod.GET, entity, TypeDocumentDonorDTO.class).getBody();

        assert result != null;
        Optional<TypeDocumentDonorDTO> typeDocumentDonor = Optional.of(result);
        TypeDocumentDonorDTO typeDocumentDonorDTO = typeDocumentDonor.get();

        log.debug("End service Get TypeDocumentDonor by ID: {}", id);
        return typeDocumentDonorDTO;
    }

}
