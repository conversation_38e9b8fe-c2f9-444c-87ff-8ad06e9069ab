package ma.almobadara.backend.repository.administration;

import ma.almobadara.backend.model.administration.CaisseEmpruntHistory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface CaisseEmpruntHistoryRepository extends JpaRepository<CaisseEmpruntHistory, Long> {
    
    List<CaisseEmpruntHistory> findByCaisseEmpruntIdOrderByCreationDateDesc(Long caisseEmpruntId);
    

}
