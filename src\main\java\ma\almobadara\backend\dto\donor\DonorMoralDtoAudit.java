package ma.almobadara.backend.dto.donor;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import ma.almobadara.backend.dto.referentiel.DonorStatusDTO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;


@Getter
@Setter
@SuperBuilder
@AllArgsConstructor
public class DonorMoralDtoAudit {
    private String raisonSociale;

    private String abreviationNomEntreprise;

    private String identifiantEntreprise;

    private String type = "Moral";
    private String statut;

    private String adresse;
    private String adresseArabe;

    private String AnneePremiereDonation;



//    private String logoUrl;
//
//    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
//    private MultipartFile logo;
//
//    private String logo64;

    //private List<Long> donorContactsIds;

    private String SecteursActivite;

    private String typeMoral;
}
