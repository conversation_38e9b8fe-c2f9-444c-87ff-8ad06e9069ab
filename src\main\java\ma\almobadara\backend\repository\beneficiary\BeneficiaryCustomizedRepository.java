package ma.almobadara.backend.repository.beneficiary;

import ma.almobadara.backend.model.beneficiary.Beneficiary;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.util.Optional;
@Repository
public interface BeneficiaryCustomizedRepository  {
    Page<Beneficiary> filterBeneficiary(Pageable pageable, Optional<String> criteria, Optional<String> value1, Optional<String> value2, Optional<String> column, Optional<Boolean> click, Optional<String> code, Optional<String> birthDay, Optional<String> beneficiaryName, Optional<String> type, Optional<String> numberPhone, Optional<Long> city, Optional<Long> status,Optional<Long> service);
}