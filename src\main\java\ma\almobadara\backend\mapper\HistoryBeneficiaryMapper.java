package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.beneficiary.HistoryBeneficiaryDTO;
import ma.almobadara.backend.model.beneficiary.HistoryBeneficiary;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

@Mapper
public interface HistoryBeneficiaryMapper {

    HistoryBeneficiaryMapper INSTANCE = Mappers.getMapper(HistoryBeneficiaryMapper.class);
    @Mapping(target = "beneficiaryId", source = "beneficiary.id")
    HistoryBeneficiaryDTO toDTO(HistoryBeneficiary historyBeneficiary);

}
