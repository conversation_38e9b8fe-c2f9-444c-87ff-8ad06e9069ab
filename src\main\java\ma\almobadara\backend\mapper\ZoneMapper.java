package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.administration.*;
import ma.almobadara.backend.model.administration.Assistant;
import ma.almobadara.backend.model.administration.HistoryZone;
import ma.almobadara.backend.model.administration.Zone;
import ma.almobadara.backend.model.beneficiary.Beneficiary;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Mapper(componentModel = "spring")
public interface ZoneMapper {

    ZoneMapper INSTANCE = Mappers.getMapper(ZoneMapper.class);

    @Mapping(target = "cityIds", source = "cityIds", qualifiedByName = "stringToLongList")
    @Mapping(target = "cityDetails", ignore = true) // Will be set separately in service
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    // we should ceck first if the assistant is null or not
   // @Mapping(target = "assistantName", expression = "java(zone.getAssistant() != null ? zone.getAssistant().getUser().getFirstName() + \" \" + zone.getAssistant().getUser().getLastName() : null)")
    @Mapping(target = "assistantName", expression = "java(zone.getAssistant() != null ? zone.getAssistant().getCacheAdUser().getFirstName() + \" \" + zone.getAssistant().getCacheAdUser().getLastName() : null)")
    @Mapping(target = "assistantId", source = "assistant.id")
    @Mapping(target = "hasAssistant", expression = "java(zone.getAssistant() != null)")
    @Mapping(target = "hasBeneficiaries", expression = "java(zone.getBeneficiaries() != null && !zone.getBeneficiaries().isEmpty())")
    @Mapping(target = "beneficiariesCount", expression = "java(zone.getBeneficiaries() != null ? zone.getBeneficiaries().size() : 0)")
    @Mapping(target = "eps", ignore = true)

    ZoneDTO zoneToZoneDTO(Zone zone);



    @Mapping(target = "cityIds", ignore = true)
    @Mapping(target = "cityDetails", ignore = true) // Will be set separately in service
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "beneficiaries", ignore = true)
    @Mapping(target = "assistantName",  ignore = true)
    @Mapping(target = "assistantId", source = "assistant.id")
    @Mapping(target = "hasAssistant",  ignore = true)
    @Mapping(target = "hasBeneficiaries", ignore = true)
    ZoneDTO zoneToLightZoneDTO(Zone zone);

    @Mapping(target = "cityIds", source = "cityIds", qualifiedByName = "longListToString")
    @Mapping(target = "nameAr", source = "nameAr")
    // we should ignore the assistant because is not added in the add of the zone
    @Mapping(target = "assistant", ignore = true)
    @Mapping(target = "eps", ignore = true)
    Zone zoneDTOToZone(ZoneDTO zoneDTO);

    //toHistoryZoneDto
    HistoryZoneDTO historyZoneToHistoryZoneDTO(HistoryZone historyZone);

    @Named("stringToLongList")
    default List<Long> stringToLongList(String cityIds) {
        if (cityIds == null || cityIds.isEmpty()) {
            return Collections.emptyList();
        }
        return Arrays.stream(cityIds.split(",\\s*"))
                .map(Long::parseLong)
                .collect(Collectors.toList());
    }

    @Named("longListToString")
    default String longListToString(List<Long> cityIds) {
        if (cityIds == null || cityIds.isEmpty()) {
            return "";
        }
        return cityIds.stream()
                .map(String::valueOf)
                .collect(Collectors.joining(", "));
    }

    default AssistantDTO toAssistantDto(Assistant assistant) {
        if (assistant == null) {
            return null;
        }
        AssistantDTO assistantDTO = new AssistantDTO();
        assistantDTO.setId(assistant.getId());
        assistantDTO.setFirstName(assistant.getFirstName());
        assistantDTO.setLastName(assistant.getLastName());
        assistantDTO.setCode(assistant.getCode());
        assistantDTO.setEmail(assistant.getEmail());
        assistantDTO.setDateAffectationToZone(assistant.getDateAffectationToZone());
        assistantDTO.setDateEndAffectationToZone(assistant.getDateEndAffectationToZone());
        // Vous pouvez mapper d'autres champs ici si nécessaire
        return assistantDTO;
    }


    default ZoneDetailsDTO toBeneficiaryDto(Beneficiary beneficiary) {
        if (beneficiary == null) {
            return null;
        }
        ZoneDetailsDTO beneficiaryDTO = new ZoneDetailsDTO();
        beneficiaryDTO.setId(beneficiary.getId());
        beneficiaryDTO.setBeneficiaryName(beneficiary.getPerson().getFirstName());
        beneficiaryDTO.setBeneficiaryLastName(beneficiary.getPerson().getLastName());
        beneficiaryDTO.setBeneficiarySex(beneficiary.getPerson().getSex());
        beneficiaryDTO.setBeneficiaryPhoneNumber(beneficiary.getPerson().getPhoneNumber());
        beneficiaryDTO.setBeneficiaryAddress(beneficiary.getPerson().getAddress());
        beneficiaryDTO.setBeneficiaryAddressAr(beneficiary.getPerson().getAddressAr());
        beneficiaryDTO.setBeneficiaryBirthDate(beneficiary.getPerson().getBirthDate());
        beneficiaryDTO.setBeneficiaryCode(beneficiary.getCodeBeneficiary() != null ? beneficiary.getCodeBeneficiary() : beneficiary.getCode());
        beneficiaryDTO.setBeneficiaryCreatedAt(beneficiary.getCreatedAt());
        // Vous pouvez mapper d'autres champs ici si nécessaire
        return beneficiaryDTO;
    }


    @Mapping(target = "assistantName", expression = "java(getAssistantName(zone))")
    @Mapping(target = "assistantId", expression = "java(zone.getAssistant()!=null?zone.getAssistant().getId():null)")
    @Mapping(target = "totalBenecifiaries", expression = "java(zone.getBeneficiaries() != null ? zone.getBeneficiaries().size() : 0)")
    ZoneDTOLight zoneToZoneDTOLight(Zone zone);

    default String getAssistantName(Zone zone) {
        if (zone.getAssistant() != null && zone.getAssistant().getCacheAdUser() != null) {
            String firstName = zone.getAssistant().getCacheAdUser().getFirstName();
            String lastName = zone.getAssistant().getCacheAdUser().getLastName();
            return firstName != null && lastName != null ? firstName + " " + lastName : null;
        }
        return null;
    }

}
