package ma.almobadara.backend.model.family;

import jakarta.persistence.*;
import lombok.*;
import ma.almobadara.backend.model.communs.Action;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@IdClass(ActionFamilyId.class)
public class ActionFamily {

    @Id
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "family_id")
    private Family family;

    @Id
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "action_id")
    private Action action;

}
