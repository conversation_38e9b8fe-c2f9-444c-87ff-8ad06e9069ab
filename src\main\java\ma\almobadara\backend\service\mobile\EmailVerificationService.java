package ma.almobadara.backend.service.mobile;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.service.communs.MailSenderService;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.security.SecureRandom;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
@RequiredArgsConstructor
public class EmailVerificationService {

    private final RedisTemplate<String, Object> redisTemplate;
    private final MailSenderService mailSenderService;
    
    private static final String VERIFICATION_CODE_PREFIX = "email_verification:";
    private static final int CODE_EXPIRATION_MINUTES = 10;
    private static final int CODE_LENGTH = 6;
    
    /**
     * Generate a 6-digit verification code and send it to the specified email
     * @param email The email address to send the verification code to
     * @return The generated verification code (for testing purposes)
     */
    public String generateAndSendVerificationCode(String email) {
        log.info("Generating verification code for email: {}", email);
        
        // Generate 6-digit code
        String verificationCode = generateSixDigitCode();
        
        // Store code in Redis with 10-minute expiration
        String redisKey = VERIFICATION_CODE_PREFIX + email;
        redisTemplate.opsForValue().set(redisKey, verificationCode, CODE_EXPIRATION_MINUTES, TimeUnit.MINUTES);
        
        // Send email with verification code
        sendVerificationEmail(email, verificationCode);
        
        log.info("Verification code generated and sent successfully for email: {}", email);
        return verificationCode;
    }
    
    /**
     * Validate the verification code for the given email
     * @param email The email address
     * @param code The verification code to validate
     * @return true if the code is valid and not expired, false otherwise
     */
    public boolean validateVerificationCode(String email, String code) {
        log.info("Validating verification code for email: {}", email);
        
        String redisKey = VERIFICATION_CODE_PREFIX + email;
        String storedCode = (String) redisTemplate.opsForValue().get(redisKey);
        
        if (storedCode == null) {
            log.warn("No verification code found for email: {} (expired or never generated)", email);
            return false;
        }
        
        boolean isValid = storedCode.equals(code);
        
        if (isValid) {
            // Remove the code from Redis after successful validation
            redisTemplate.delete(redisKey);
            log.info("Verification code validated successfully for email: {}", email);
        } else {
            log.warn("Invalid verification code provided for email: {}", email);
        }
        
        return isValid;
    }
    
    /**
     * Generate a secure 6-digit numeric code
     * @return A 6-digit string
     */
    private String generateSixDigitCode() {
        SecureRandom random = new SecureRandom();
        int code = 100000 + random.nextInt(900000); // Ensures 6 digits
        return String.valueOf(code);
    }
    
    /**
     * Send verification email with styled HTML content
     * @param email The recipient email address
     * @param verificationCode The 6-digit verification code
     */
    private void sendVerificationEmail(String email, String verificationCode) {
        String subject = "Code de vérification - Almobadara";
        String emailBody = createEmailTemplate(verificationCode);
        
        try {
            mailSenderService.sendNewMail(email, subject, emailBody);
            log.info("Verification email sent successfully to: {}", email);
        } catch (Exception e) {
            log.error("Failed to send verification email to: {}", email, e);
            throw new RuntimeException("Failed to send verification email", e);
        }
    }
    
    /**
     * Create styled HTML email template for verification code
     * @param verificationCode The 6-digit verification code
     * @return HTML email content
     */
    private String createEmailTemplate(String verificationCode) {
        String str = "";
        str += "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">";
        str += "<html dir=\"ltr\" lang=\"en\">";
        str += "  <head>";
        str += "    <meta content=\"text/html; charset=UTF-8\" http-equiv=\"Content-Type\" />";
        str += "    <meta name=\"x-apple-disable-message-reformatting\" />";
        str += "  </head>";
        str += "  <div style=\"display:none;overflow:hidden;line-height:1px;opacity:0;max-height:0;max-width:0\">";
        str += "    You have submitted a password change request!";
        str += "  </div>";
        str += "";
        str += "  <body";
        str += "    style=\"margin-left:auto;margin-right:auto;margin-top:auto;margin-bottom:auto;height:100vh;background-color:rgb(245,245,245);padding-left:1rem;padding-right:1rem;padding-bottom:2.5rem;padding-top:1rem;font-family:ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale\"";
        str += "  >";
        str += "    <table";
        str += "      align=\"center\"";
        str += "      width=\"100%\"";
        str += "      border=\"0\"";
        str += "      cellpadding=\"0\"";
        str += "      cellspacing=\"0\"";
        str += "      role=\"presentation\"";
        str += "      style=\"margin-left:auto;margin-right:auto;border-radius:1rem;background-color:rgb(255,255,255);padding:2rem;border-width:1px;border-style:solid;border-color:rgb(229,229,229);max-width:37.5em\"";
        str += "    >";
        str += "      <tbody>";
        str += "        <tr style=\"width:100%\">";
        str += "          <td>";
        str += "            <table";
        str += "              align=\"center\"";
        str += "              width=\"100%\"";
        str += "              id=\"header\"";
        str += "              class=\"\"";
        str += "              border=\"0\"";
        str += "              cellpadding=\"0\"";
        str += "              cellspacing=\"0\"";
        str += "              role=\"presentation\"";
        str += "            >";
        str += "              <tbody>";
        str += "                <tr>";
        str += "                  <td>";
        str += "                    <a";
        str += "                      href=\"https://almobadara.xelops.ma/3a3a86c7b27900bb81d53080c2625948.png\"";
        str += "                      style=\"margin-bottom:1rem;display:block;color:#067df7;text-decoration:none\"";
        str += "                      target=\"_blank\"";
        str += "                      ><img";
        str += "                        alt=\"Auth0 Logo\"";
        str += "                        height=\"32\"";
        str += "                        src=\"https://almobadara.xelops.ma/3a3a86c7b27900bb81d53080c2625948.png\"";
        str += "                        style=\"margin-left:auto;margin-right:auto;display:block;outline:none;border:none;text-decoration:none\"";
        str += "                        width=\"auto\"";
        str += "                    /></a>";
        str += "                    <hr";
        str += "                      style=\"margin-top:1.25rem;margin-bottom:1.25rem;border-width:1px;border-color:rgb(212,212,212);width:100%;border:none;border-top:1px solid #eaeaea\"";
        str += "                    />";
        str += "                  </td>";
        str += "                </tr>";
        str += "              </tbody>";
        str += "            </table>";
        str += "            <table";
        str += "              align=\"center\"";
        str += "              width=\"100%\"";
        str += "              id=\"subject\"";
        str += "              border=\"0\"";
        str += "              cellpadding=\"0\"";
        str += "              cellspacing=\"0\"";
        str += "              role=\"presentation\"";
        str += "            >";
        str += "              <tbody>";
        str += "                <tr>";
        str += "                  <td>";
        str += "                    <table";
        str += "                      align=\"center\"";
        str += "                      width=\"100%\"";
        str += "                      border=\"0\"";
        str += "                      cellpadding=\"0\"";
        str += "                      cellspacing=\"0\"";
        str += "                      role=\"presentation\"";
        str += "                      style=\"height:4rem;width:4rem;border-radius:0.75rem;background-color:rgb(245,245,245);max-width:37.5em\"";
        str += "                    >";
        str += "                      <tbody>";
        str += "                        <tr style=\"width:100%\">";
        str += "                          <td>";
        str += "                            <img";
        str += "                              alt=\"Icon\"";
        str += "                              height=\"24\"";
        str += "                              src=\"https://cdn.auth0.com/website/emails/product/icon-password.png\"";
        str += "                              style=\"margin-left:auto;margin-right:auto;display:block;outline:none;border:none;text-decoration:none\"";
        str += "                              width=\"24\"";
        str += "                            />";
        str += "                          </td>";
        str += "                        </tr>";
        str += "                      </tbody>";
        str += "                    </table>";
        str += "                    <h1";
        str += "                      style=\"padding:0px;text-align:center;font-size:1.5rem;line-height:2rem;font-weight:600;color:rgb(23,23,23)\"";
        str += "                    >";
        str += "                      You have submitted a password change request!";
        str += "                    </h1>";
        str += "                    <hr";
        str += "                      style=\"margin-top:1.25rem;margin-bottom:1.25rem;border-width:1px;border-color:rgb(212,212,212);width:100%;border:none;border-top:1px solid #eaeaea\"";
        str += "                    />";
        str += "                  </td>";
        str += "                </tr>";
        str += "              </tbody>";
        str += "            </table>";
        str += "            <table";
        str += "              align=\"center\"";
        str += "              width=\"100%\"";
        str += "              id=\"body\"";
        str += "              border=\"0\"";
        str += "              cellpadding=\"0\"";
        str += "              cellspacing=\"0\"";
        str += "              role=\"presentation\"";
        str += "            >";
        str += "              <tbody>";
        str += "                <tr>";
        str += "                  <td>";
        str += "                    <p";
        str += "                      style=\"margin-top:1.5rem;text-align:center;font-size:1rem;line-height:1.5rem;color:rgb(23,23,23);margin:16px 0\"";
        str += "                    >";
        str += "                      If it was you, confirm the password change using this code";
        str += "                    </p>";
        str += "                    <p";
        str += "                      style=\"margin-top:1.5rem;text-align:center;font-size:1rem;line-height:1.5rem;font-weight:600;color:rgb(23,23,23);margin:16px 0\"";
        str += "                    >";
        str += "                      Your code is:";
        str += "                    </p>";
        str += "                    <code";
        str += "                      style='margin-bottom:0.5rem;display:block;border-radius:0.5rem;border-width:1px;border-style:solid;border-color:rgb(212,212,212);background-color:rgb(245,245,245);padding-left:0.625rem;padding-right:0.625rem;padding-top:0.75rem;padding-bottom:0.75rem;text-align:center;font-family:ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace;font-size:1.25rem;line-height:1.75rem;font-weight:600;color:rgb(23,23,23)'";
        str += "                      >"+verificationCode+"</code";
        str += "                    >";
        str += "                  </td>";
        str += "                </tr>";
        str += "              </tbody>";
        str += "            </table>";
        str += "            <table";
        str += "              align=\"center\"";
        str += "              width=\"100%\"";
        str += "              id=\"issue-note\"";
        str += "              border=\"0\"";
        str += "              cellpadding=\"0\"";
        str += "              cellspacing=\"0\"";
        str += "              role=\"presentation\"";
        str += "              style=\"text-align:center;margin-top:1.5rem\"";
        str += "            >";
        str += "              <tbody>";
        str += "                <tr>";
        str += "                  <td>";
        str += "                    <table";
        str += "                      align=\"center\"";
        str += "                      width=\"100%\"";
        str += "                      border=\"0\"";
        str += "                      cellpadding=\"0\"";
        str += "                      cellspacing=\"0\"";
        str += "                      role=\"presentation\"";
        str += "                      style=\"width:100%;text-align:center;max-width:37.5em\"";
        str += "                    >";
        str += "                      <tbody>";
        str += "                        <tr style=\"width:100%\">";
        str += "                          <td>";
        str += "                            <p";
        str += "                              style=\"margin:0px;margin-left:2rem;margin-right:2rem;font-size:0.875rem;line-height:1.25rem;color:rgb(163,163,163)\"";
        str += "                            >";
        str += "                              If you are having any issues with your account, please don&#x27;t hesitate to contact us";
        str += "                              by replying to this mail.";
        str += "                            </p>";
        str += "                            <p";
        str += "                              style=\"margin:0px;margin-left:2rem;margin-right:2rem;font-size:0.875rem;line-height:1.25rem;color:rgb(163,163,163)\"";
        str += "                            >";
        str += "                              Thanks!";
        str += "                            </p>";
        str += "                          </td>";
        str += "                        </tr>";
        str += "                      </tbody>";
        str += "                    </table>";
        str += "                  </td>";
        str += "                </tr>";
        str += "              </tbody>";
        str += "            </table>";
        str += "            <table";
        str += "              align=\"center\"";
        str += "              width=\"100%\"";
        str += "              id=\"footer\"";
        str += "              border=\"0\"";
        str += "              cellpadding=\"0\"";
        str += "              cellspacing=\"0\"";
        str += "              role=\"presentation\"";
        str += "              style=\"margin-top:1.5rem\"";
        str += "            >";
        str += "              <tbody>";
        str += "                <tr>";
        str += "                  <td>";
        str += "                    <hr";
        str += "                      style=\"margin-top:0.75rem;margin-bottom:1.5rem;border-width:1px;border-color:rgb(212,212,212);width:100%;border:none;border-top:1px solid #eaeaea\"";
        str += "                    />";
        str += "                    <p";
        str += "                      style=\"text-align:center;font-size:0.75rem;line-height:1rem;color:rgb(163,163,163);margin:16px 0\"";
        str += "                    >";
        str += "                      You&#x27;re receiving this email because you have an account in. If you are not sure why you’re receiving this, please contact us through our Support Center";
        str += "                    </p>";
        str += "                  </td>";
        str += "                </tr>";
        str += "              </tbody>";
        str += "            </table>";
        str += "          </td>";
        str += "        </tr>";
        str += "      </tbody>";
        str += "    </table>";
        str += "    <table";
        str += "      align=\"center\"";
        str += "      width=\"100%\"";
        str += "      border=\"0\"";
        str += "      cellpadding=\"0\"";
        str += "      cellspacing=\"0\"";
        str += "      role=\"presentation\"";
        str += "      style=\"margin-top:1rem;max-width:37.5em\"";
        str += "    >";
        str += "      <tbody>";
        str += "        <tr style=\"width:100%\">";
        str += "          <td>";
        str += "            <table";
        str += "              align=\"center\"";
        str += "              width=\"100%\"";
        str += "              border=\"0\"";
        str += "              cellpadding=\"0\"";
        str += "              cellspacing=\"0\"";
        str += "              role=\"presentation\"";
        str += "              style=\"width:fit-content;opacity:0.5\"";
        str += "            >";
        str += "              <tbody style=\"width:100%\">";
        str += "                <tr style=\"width:100%\">";
        str += "                  <td align=\"right\" data-id=\"__react-email-column\" style=\"height:40px;width:fit-content\">";
        str += "                    <p";
        str += "                      style=\"margin-top:-0.5rem;font-size:0.75rem;line-height:1rem;color:rgb(23,23,23);margin:16px 0\"";
        str += "                    >";
        str += "                      Sent by Almobadara team";
        str += "                    </p>";
        str += "                  ";
        str += "                </tr>";
        str += "              </tbody>";
        str += "            </table>";
        str += "          </td>";
        str += "        </tr>";
        str += "      </tbody>";
        str += "    </table>";
        str += "  </body>";
        str += "</html>";

//        return String.format("""
//                <!DOCTYPE html>
//                <html>
//                <head>
//                    <meta charset="UTF-8">
//                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
//                    <style>
//                        .email-container {
//                            max-width: 600px;
//                            margin: 0 auto;
//                            font-family: Arial, sans-serif;
//                            background-color: #f9f9f9;
//                            padding: 20px;
//                            border-radius: 10px;
//                        }
//                        .email-header {
//                            text-align: center;
//                            background-color: #2c5aa0;
//                            color: white;
//                            padding: 20px;
//                            border-radius: 10px 10px 0 0;
//                        }
//                        .email-content {
//                            background-color: white;
//                            padding: 30px;
//                            text-align: center;
//                        }
//                        .verification-code {
//                            font-size: 32px;
//                            font-weight: bold;
//                            color: #2c5aa0;
//                            background-color: #f0f4f8;
//                            padding: 15px 30px;
//                            border-radius: 8px;
//                            letter-spacing: 5px;
//                            margin: 20px 0;
//                            display: inline-block;
//                        }
//                        .email-footer {
//                            text-align: center;
//                            color: #666;
//                            font-size: 14px;
//                            margin-top: 20px;
//                        }
//                        .warning {
//                            color: #e74c3c;
//                            font-weight: bold;
//                            margin: 15px 0;
//                        }
//                    </style>
//                </head>
//                <body>
//                    <div class="email-container">
//                        <div class="email-header">
//                            <h1>Code de Vérification</h1>
//                            <p>Association Almobadara</p>
//                        </div>
//                        <div class="email-content">
//                            <h2>Bonjour,</h2>
//                            <p>Vous avez demandé un code de vérification pour votre compte Almobadara.</p>
//                            <p>Voici votre code de vérification :</p>
//
//                            <div class="verification-code">%s</div>
//
//                            <p class="warning">⚠️ Ce code expire dans 10 minutes</p>
//                            <p>Si vous n'avez pas demandé ce code, veuillez ignorer cet email.</p>
//                        </div>
//                        <div class="email-footer">
//                            <p>Cordialement,<br>L'équipe Almobadara</p>
//                            <p><small>Cet email a été envoyé automatiquement, merci de ne pas y répondre.</small></p>
//                        </div>
//                    </div>
//                </body>
//                </html>
//                """, verificationCode);
        return str;
    }
}
