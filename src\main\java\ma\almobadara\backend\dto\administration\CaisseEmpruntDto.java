package ma.almobadara.backend.dto.administration;

import lombok.*;

import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CaisseEmpruntDto {
    private Long id;
    private Double amount;
    private Long donorId;
    private Long serviceId;
    private String status;
    private LocalDateTime dateEmprunt;
    private LocalDateTime dateRemboursement;
    private LocalDateTime creationDate;
    private LocalDateTime updateDate;
}
