package ma.almobadara.backend.dto.administration;

import lombok.*;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CaisseEmpruntDto {
    private Long id;
    private Long donorId;
    private Double globalAmount;
    private LocalDateTime lastDateEmprunt;
    private LocalDateTime lastDateRemboursement;
    private String status;
    private LocalDateTime creationDate;
    private LocalDateTime updateDate;

    // List of history records
    private List<CaisseEmpruntHistoryDto> histories;
}
