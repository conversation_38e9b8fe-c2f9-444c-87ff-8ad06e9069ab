package ma.almobadara.backend.service.migration;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.administration.AssistantDTO;
import ma.almobadara.backend.dto.administration.EpsDto;
import ma.almobadara.backend.dto.administration.TagDTO;
import ma.almobadara.backend.dto.administration.ZoneDTO;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.repository.administration.TagRepository;
import ma.almobadara.backend.service.administration.AssistantService;
import ma.almobadara.backend.service.administration.EpsService;
import ma.almobadara.backend.service.administration.ZoneService;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;

import static ma.almobadara.backend.service.migration.util.UtilityClass.getCellValue;

@Service
@RequiredArgsConstructor
@Slf4j
public class ZoneMigrationService {

    private final ZoneService zoneService;
    private final EpsService epsService;
    private final TagRepository tagRepository;

    public void migrateZone(MultipartFile file) throws TechnicalException {
        log.info("Request to migrateZone : {}", file);
        try (Workbook workbook = new XSSFWorkbook(file.getInputStream())) {
            Sheet sheet = workbook.getSheetAt(0);
            for (Row row : sheet) {
                if (row.getRowNum() == 0) continue;
                if (row.getCell(0) == null || row.getCell(0).getCellType() == CellType.BLANK) {
                    break;
                }
                Long epsId = null;
                String epsName = getCellValue(row, 4);

                if (epsName != null && epsName.toLowerCase().startsWith("eps")) {
                    EpsDto eps = createEpsDTO(row);
                    EpsDto savedEps = epsService.createEps(eps);
                    epsId = savedEps.getId();
                }

                ZoneDTO zone = createZoneDTO(row, epsId);
                zoneService.createZone(zone);
            }
        } catch (Exception e) {
            log.error("Error during migration: {}", e.getMessage());
            throw new TechnicalException("Error during migration: " + e.getMessage());
        }
    }


    private EpsDto createEpsDTO(Row row) {
        EpsDto eps = new EpsDto();
        eps.setName(getCellValue(row, 4));
        eps.setNameAr(getCellValue(row, 5));
        eps.setAddress("À compléter");
        eps.setAddressAr("قيد الإكمال");
        eps.setTags(buildTags());
        return eps;
    }
    private List<TagDTO> buildTags() {
        List<TagDTO> tags = new ArrayList<>();
        tagRepository.findByNameAndType("migration", "metier").ifPresent(tag -> tags.add(TagDTO.builder().id(tag.getId()).name(tag.getName()).build()));
        tagRepository.findByNameAndType("à_compléter", "metier").ifPresent(tag -> tags.add(TagDTO.builder().id(tag.getId()).name(tag.getName()).build()));
        return tags;
    }
    private ZoneDTO createZoneDTO(Row row, Long epsId) {
        ZoneDTO zone = new ZoneDTO();
        zone.setEpsId(epsId);
        zone.setCode(getCellValue(row,4));
        zone.setName(getCellValue(row, 5));
        zone.setNameAr(getCellValue(row, 6));
        zone.setOldZone(true);
        return zone;
    }

}
