package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.administration.CacheAdUserDTO;
import ma.almobadara.backend.model.administration.CacheAdUser;
import org.mapstruct.Mapper;
import org.springframework.beans.factory.annotation.Autowired;

@Mapper(componentModel = "spring")
public abstract class CacheAdUserDTOMapper {

    @Autowired
    private ProfileMapper profileMapper;

    @Autowired
    private RoleMapper roleMapper;

    public CacheAdUserDTO mapToDTO(CacheAdUser cacheAdUser) {
        CacheAdUserDTO dto = new CacheAdUserDTO();

        dto.setId(cacheAdUser.getId());
        dto.setMail(cacheAdUser.getMail());
        dto.setAzureDirectoryId(cacheAdUser.getAzureDirectoryId());
        dto.setFirstName(cacheAdUser.getFirstName());
        dto.setLastName(cacheAdUser.getLastName());
        dto.setCreationDate(cacheAdUser.getCreationDate());
        dto.setUpdateDate(cacheAdUser.getUpdateDate());
        dto.setLastLoginInDate(cacheAdUser.getLastLoginInDate());
        // Map profile if it exists
        if (cacheAdUser.getProfile() != null) {
            dto.setProfile(profileMapper.toDTO(cacheAdUser.getProfile()));
        }
        if(cacheAdUser.getRole() != null){
            dto.setRole(roleMapper.toDTO(cacheAdUser.getRole()));
        }
        // Map other fields
        return dto;
    }
}