package ma.almobadara.backend.dto.takenInCharge;

import lombok.*;

import java.util.Date;
import java.util.List;


@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class TakenInChargeAuditDto {

    private String code;

    private Date DateDeDebut;

    private Date DateDeFin;

    private String Service;

    private String Statut;

    private List<String> CodeDonateur;

    private List<String> CodeBeneficiaire;

}
