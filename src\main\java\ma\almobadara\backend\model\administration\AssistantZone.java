package ma.almobadara.backend.model.administration;

import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDate;
@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AssistantZone {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    private Assistant assistant;

    @ManyToOne
    private Zone zone;

    private LocalDate dateAffectation;
    private LocalDate dateEndAffectation;
}