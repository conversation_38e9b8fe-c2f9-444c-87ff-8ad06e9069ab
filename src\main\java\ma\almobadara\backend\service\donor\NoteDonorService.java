package ma.almobadara.backend.service.donor;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.donor.DonorNoteDetailsDTO;
import ma.almobadara.backend.mapper.NoteDonorDetailsMapper;
import ma.almobadara.backend.model.donor.Donor;
import ma.almobadara.backend.model.donor.NoteDonor;
import ma.almobadara.backend.repository.donor.DonorRepository;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
@AllArgsConstructor
@Slf4j
public class NoteDonorService {

    private final DonorRepository donorRepository;
    private final NoteDonorDetailsMapper noteDonorDetailsMapper;

    public List<DonorNoteDetailsDTO> getNotesByDonorId(Long donorId) {
        log.debug("Start service Get Notes by Donor ID: {}", donorId);
        Optional<Donor> optionalDonor = donorRepository.findById(donorId);
        if (optionalDonor.isPresent()) {
            Donor donor = optionalDonor.get();
            List<NoteDonor> noteDonors = donor.getNotes();
            List<DonorNoteDetailsDTO> donorNoteDetailsDTOS =
                    noteDonorDetailsMapper.noteDonorListToDtoList(noteDonors);
            log.debug("End service Get Notes by Donor ID");
            return donorNoteDetailsDTOS;
        } else {
            // Gérez le cas où le Donor n'est pas trouvé, vous pouvez renvoyer null ou une erreur
            return null;
        }
    }

}
