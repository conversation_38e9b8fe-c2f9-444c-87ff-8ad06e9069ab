package ma.almobadara.backend.model.beneficiary;


import jakarta.persistence.*;
import lombok.*;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BeneficiaryStatut {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(unique = true, nullable = false)
    private String nameStatut;

    public BeneficiaryStatut(Long id) {
        this.id = id;
    }

    public void setValue(BeneficiaryStatut beneficiaryStatut) {
        if (beneficiaryStatut != null) {
            this.id = beneficiaryStatut.getId();
            this.nameStatut = beneficiaryStatut.getNameStatut();
        } else {
            throw new IllegalArgumentException("BeneficiaryStatut cannot be null");
        }
    }

}

