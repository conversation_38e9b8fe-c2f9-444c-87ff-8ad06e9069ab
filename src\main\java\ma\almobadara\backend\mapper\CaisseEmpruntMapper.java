package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.administration.CaisseEmpruntDto;
import ma.almobadara.backend.dto.administration.CaisseEmpruntDetailDto;
import ma.almobadara.backend.dto.administration.CaisseEmpruntListDto;
import ma.almobadara.backend.model.administration.CaisseEmprunt;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface CaisseEmpruntMapper {
     CaisseEmpruntDto toDto(CaisseEmprunt caisseEmprunt);
     CaisseEmprunt toEntity(CaisseEmpruntDto caisseEmpruntDto);

     @Mapping(target = "donorName", ignore = true) // Will be set separately in service
     @Mapping(target = "serviceName", ignore = true) // Will be set separately in service
     CaisseEmpruntListDto toListDto(CaisseEmprunt caisseEmprunt);

     @Mapping(target = "donor", ignore = true) // Will be set separately in service
     @Mapping(target = "service", ignore = true) // Will be set separately in service
     CaisseEmpruntDetailDto toDetailDto(CaisseEmprunt caisseEmprunt);
}
