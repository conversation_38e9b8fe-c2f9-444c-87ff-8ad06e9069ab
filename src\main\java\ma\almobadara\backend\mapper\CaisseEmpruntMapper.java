package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.administration.CaisseEmpruntDto;
import ma.almobadara.backend.dto.administration.CaisseEmpruntDetailDto;
import ma.almobadara.backend.dto.administration.CaisseEmpruntListDto;
import ma.almobadara.backend.model.administration.CaisseEmprunt;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring", uses = {CaisseEmpruntHistoryMapper.class})
public interface CaisseEmpruntMapper {

     @Mapping(target = "donorId", source = "donor.id")
     @Mapping(target = "histories", ignore = true) // Will be set separately in service
     CaisseEmpruntDto toDto(CaisseEmprunt caisseEmprunt);

     @Mapping(target = "donor", ignore = true) // Will be set separately in service
     @Mapping(target = "histories", ignore = true) // Will be set separately in service
     CaisseEmprunt toEntity(CaisseEmpruntDto caisseEmpruntDto);

     @Mapping(target = "donorId", source = "donor.id")
     @Mapping(target = "donorName", ignore = true) // Will be set separately in service
     @Mapping(target = "totalHistoryRecords", ignore = true) // Will be calculated in service
     @Mapping(target = "totalEmpruntAmount", ignore = true) // Will be calculated in service
     @Mapping(target = "totalRemboursementAmount", ignore = true) // Will be calculated in service
     @Mapping(target = "remainingAmount", ignore = true) // Will be calculated in service
     CaisseEmpruntListDto toListDto(CaisseEmprunt caisseEmprunt);

     @Mapping(target = "donor", ignore = true) // Will be set separately in service
     @Mapping(target = "histories", ignore = true) // Will be set separately in service
     @Mapping(target = "totalEmpruntAmount", ignore = true) // Will be calculated in service
     @Mapping(target = "totalRemboursementAmount", ignore = true) // Will be calculated in service
     @Mapping(target = "remainingAmount", ignore = true) // Will be calculated in service
     @Mapping(target = "totalHistoryRecords", ignore = true) // Will be calculated in service
     CaisseEmpruntDetailDto toDetailDto(CaisseEmprunt caisseEmprunt);
}
