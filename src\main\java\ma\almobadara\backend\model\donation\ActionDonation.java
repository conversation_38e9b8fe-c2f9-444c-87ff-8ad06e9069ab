package ma.almobadara.backend.model.donation;

import jakarta.persistence.*;
import lombok.*;
import ma.almobadara.backend.model.communs.Action;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@IdClass(ActionDonationId.class)
public class ActionDonation {

    @Id
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "donation_id")
    private Donation donation;

    @Id
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "action_id")
    private Action action;

}
