package ma.almobadara.backend.service.beneficiary;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.beneficiary.BankCardDTO;
import ma.almobadara.backend.dto.referentiel.CardTypeDTO;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.mapper.BankCardMapper;
import ma.almobadara.backend.model.beneficiary.BankCard;
import ma.almobadara.backend.model.beneficiary.Person;
import ma.almobadara.backend.repository.beneficiary.BankCardRepository;
import ma.almobadara.backend.repository.beneficiary.PersonRepository;
import ma.almobadara.backend.service.ReferentialService;
import ma.almobadara.backend.util.times.TimeWatch;
import org.springframework.data.rest.webmvc.ResourceNotFoundException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@RequiredArgsConstructor
@Slf4j
@Service
public class BankCardService {

    private final BankCardMapper bankCardMapper;
    private final BankCardRepository bankCardRepository;
    private final ReferentialService referentialService;
    private final PersonRepository personRepository;

    public BankCardDTO addBankCardToPerson(Long personId, BankCardDTO bankCardDTO) throws TechnicalException {
        TimeWatch watch = TimeWatch.start();
        log.debug("start service creation carte bank for person : {}", personId);

        BankCard bankCard = bankCardMapper.bankCardDTOToBankCard(bankCardDTO);
        Person person = personRepository.findById(personId)
                .orElseThrow(() -> new ResourceNotFoundException("Person not found with id: " + personId));

        if (bankCardDTO.getId() != null) {
            Optional<BankCard> existingBankCard = bankCardRepository.findById(bankCardDTO.getId());
            existingBankCard.ifPresent(existing -> bankCard.setId(existing.getId()));
        }
        bankCard.setPerson(person);


        bankCardRepository.save(bankCard);

        BankCardDTO bankCardDTO1 = bankCardMapper.bankCardToBankCardDTO(bankCard);
        if(bankCardDTO.getCardType() != null){
            CardTypeDTO cardTypeDTO = referentialService.findCardTypeById(bankCard.getCardTypeId());
            bankCardDTO1.setCardType(cardTypeDTO);
        }



        log.debug("end creation carte bank for person : {}, took {}", person.getId(), watch.toMS());
        return bankCardDTO1;
    }

    public void deleteBankCard(Long id) {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service Delete BankCard by ID : {}", id);
        bankCardRepository.deleteById(id);
        log.debug("End service Delete BankCard by ID : {}, took {}", id, watch.toMS());
    }

    public List<BankCardDTO> getBankCardByPersonId(Long personId) throws TechnicalException {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service get BankCard");
        Person person = personRepository.findById(personId)
                .orElseThrow(() -> new ResourceNotFoundException("Person not found with id: " + personId));

        List<BankCard> bankCards = bankCardRepository.findByPerson(person);
        List<BankCardDTO> bankCardDTOS = new ArrayList<>();

        for (BankCard bankCard : bankCards) {
            CardTypeDTO cardTypeDTO = referentialService.findCardTypeById(bankCard.getCardTypeId());
            BankCardDTO bankCardDTO = bankCardMapper.bankCardToBankCardDTO(bankCard);
           bankCardDTO.setCardType(cardTypeDTO);
            bankCardDTOS.add(bankCardDTO);
        }
        log.debug("End service get BankCard, took {}", watch.toMS());
        return bankCardDTOS;
    }

}
