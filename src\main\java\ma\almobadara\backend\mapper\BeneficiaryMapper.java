package ma.almobadara.backend.mapper;


import ma.almobadara.backend.dto.aideComplemenatire.BeneficiaryAideComplemenatireDTO;
import ma.almobadara.backend.dto.beneficiary.*;
import ma.almobadara.backend.dto.donation.DonationDTO;
import ma.almobadara.backend.dto.donor.DonorDTO;
import ma.almobadara.backend.dto.exportentities.BeneficiaryExportDTO;
import ma.almobadara.backend.dto.family.FamilyDTO;
import ma.almobadara.backend.dto.family.FamilyMemberDTO;
import ma.almobadara.backend.dto.getbeneficiary.GetBeneficiaryDTO;
import ma.almobadara.backend.dto.getbeneficiary.GetEducationDTO;
import ma.almobadara.backend.dto.getbeneficiary.GetListDTO;
import ma.almobadara.backend.dto.getbeneficiary.GetServiceDTO;
import ma.almobadara.backend.dto.referentiel.AllergiesDTO;
import ma.almobadara.backend.dto.referentiel.DiseasesDTO;
import ma.almobadara.backend.dto.takenInCharge.*;
import ma.almobadara.backend.model.beneficiary.*;
import ma.almobadara.backend.model.donation.Donation;
import ma.almobadara.backend.model.donor.Donor;
import ma.almobadara.backend.model.family.Family;
import ma.almobadara.backend.model.family.FamilyMember;
import ma.almobadara.backend.model.takenInCharge.TakenInCharge;
import ma.almobadara.backend.model.takenInCharge.TakenInChargeBeneficiary;
import ma.almobadara.backend.model.takenInCharge.TakenInChargeDonor;
import ma.almobadara.backend.model.takenInCharge.TakenInChargeOperation;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


@Mapper(componentModel = "spring", imports = {Collectors.class, AllergiesDTO.class, DiseasesDTO.class, BeneficiaryDisease.class, BeneficiaryAllergy.class} , uses = {ZoneMapper.class})
public interface BeneficiaryMapper {

	@Mapping(target = "typePriseEnChargeIdsList", ignore = true)
	Person mapBeneficiaryAddDTOToPerson(BeneficiaryAddDTO beneficiaryAddDTO);

	@Mapping(target = "rqComplete", ignore = true)
	@Mapping(target = "rqReject", ignore = true)
	Beneficiary mapBeneficiaryAddDTOToBeneficiary(BeneficiaryAddDTO beneficiaryAddDTO);

	@Mapping(source = "beneficiaryAllergies", target = "allergies")
	@Mapping(source = "beneficiaryDiseases", target = "diseases")
	@Mapping(source = "zone", target = "zone")
	@Mapping(source = "sousZone", target = "sousZone")
	@Mapping(source = "beneficiaryStatut", target = "beneficiaryStatut")
	@Mapping(target = "documents", ignore = true)
	BeneficiaryDTO beneficiaryToBeneficiaryDTO(Beneficiary beneficiary);


	@Mapping(target = "personFirstName", source = "person.firstName")
	@Mapping(target = "personLastName", source = "person.lastName")
	BeneficiaryReleveDto toDto(Beneficiary beneficiary);

	@Mapping(target = "personFirstName", source = "person.firstName")
	@Mapping(target = "personLastName", source = "person.lastName")
	Iterable<BeneficiaryReleveDto> toDto(Iterable<Beneficiary> beneficiaries);

	Iterable<BeneficiaryDTO> beneficiaryToBeneficiaryDTO(Iterable<Beneficiary> beneficiaries);

	@Mapping(source = "allergies", target = "beneficiaryAllergies")
	@Mapping(source = "diseases", target = "beneficiaryDiseases")
	@Mapping(source = "zone", target = "zone")
	Beneficiary beneficiaryDTOToBeneficiary(BeneficiaryDTO beneficiaryDTO);

	Iterable<Beneficiary> beneficiaryDTOToBeneficiary(Iterable<BeneficiaryDTO> beneficiaryDTOS);


	@Mapping(target = "typePriseEnChargeIdsList", ignore = true)
	Person mapBeneficiaryAdHocPersonDtoToPerson(BeneficiaryAdHocPersonneDto beneficiaryAdHocPersonneDto);


	Beneficiary mapBeneficiaryAdHocPersonDtoToBeneficiary(BeneficiaryAdHocPersonneDto beneficiaryAdHocPersonneDto);

	GlassesDto glassesModelToDto(Glasses glasses);

	Iterable<GlassesDto> glassesListModelToDto(Iterable<Glasses> glasses);

	Glasses glassesDtoModelToModel(GlassesDto glassesDto);

	Iterable<Glasses> glassesDTOToGlasses(Iterable<GlassesDto> glassesDtos);

	@Mapping(source = "allergyId", target = "id")
    AllergiesDTO beneficiaryAllergyToAllergyDTO(BeneficiaryAllergy beneficiaryAllergy);

	List<AllergiesDTO> beneficiaryAllergyToAllergyDTO(List<BeneficiaryAllergy> beneficiaryAllergies);

	@Mapping(source = "id", target = "allergyId")
	BeneficiaryAllergy allergyDTOToBeneficiaryAllergy(AllergiesDTO allergyDTO);

	List<BeneficiaryAllergy> allergyDTOToBeneficiaryAllergy(List<AllergiesDTO> allergies);

	@Mapping(source = "diseaseId", target = "id")
    DiseasesDTO beneficiaryDiseaseToDiseaseDTO(BeneficiaryDisease beneficiaryDisease);

	List<DiseasesDTO> beneficiaryDiseaseToDiseaseDTO(List<BeneficiaryDisease> beneficiaryDiseases);

	@Mapping(source = "id", target = "diseaseId")
	BeneficiaryDisease DiseaseDTOToBeneficiaryDisease(DiseasesDTO diseaseDTO);

	List<BeneficiaryDisease> DiseaseDTOToBeneficiaryDisease(List<DiseasesDTO> diseases);

	///////////////////////////////////////////////////////////////////////////////////////////

	@Mapping(source = "education.cityId", target = "cityId")
	@Mapping(source = "education.schoolYearId", target = "schoolYearId")
	@Mapping(source = "education.schoolLevelId", target = "schoolLevelId")
	@Mapping(source = "education.honorId", target = "honorId")
	@Mapping(source = "education.majorId", target = "majorId")
	GetEducationDTO educationToGetEducationDTO(Education education);

	Set<GetEducationDTO> educationToGetEducationDTO(Iterable<Education> educations);


	@Mapping(expression = "java( beneficiaryServiceToGetServiceDTO(beneficiary.getBeneficiaryServices()) )", target = "services")
	@Mapping(expression = "java( educationToGetEducationDTO(beneficiary.getEducations()) )", target = "educations")
	@Mapping(source = "beneficiary.person.cityId", target = "cityId")
	@Mapping(source = "beneficiary.person.professionId", target = "professionId")
	@Mapping(source = "beneficiary.person.accommodationTypeId", target = "accommodationTypeId")
	@Mapping(source = "beneficiary.person.accommodationNatureId", target = "accommodationNatureId")
	@Mapping(source = "beneficiary.person.typeIdentityId", target = "typeIdentityId")
	@Mapping(source = "beneficiary.person.firstName", target = "firstName")
	@Mapping(source = "beneficiary.person.lastName", target = "lastName")
	@Mapping(source = "beneficiary.person.firstNameAr", target = "firstNameAr")
	@Mapping(source = "beneficiary.person.lastNameAr", target = "lastNameAr")
	@Mapping(source = "beneficiary.person.sex", target = "sex")
	@Mapping(source = "beneficiary.person.email", target = "email")
	@Mapping(source = "beneficiary.person.phoneNumber", target = "phoneNumber")
	@Mapping(source = "beneficiary.person.address", target = "address")
	@Mapping(source = "beneficiary.person.addressAr", target = "addressAr")
	@Mapping(source = "beneficiary.person.birthDate", target = "birthDate")
	@Mapping(source = "beneficiary.person.identityCode", target = "identityCode")
	@Mapping(target = "typePriseEnChargeIds", ignore = true)
	GetBeneficiaryDTO beneficiaryToGetBeneficiaryDTO(Beneficiary beneficiary);

	Iterable<GetBeneficiaryDTO> beneficiaryToGetBeneficiaryDTO(Iterable<Beneficiary> beneficiaries);

	@Mapping(source = "beneficiaryService.serviceId", target = "serviceId")
	@Mapping(source = "beneficiaryService.statusId", target = "statusId")
	GetServiceDTO beneficiaryServiceToGetServiceDTO(BeneficiaryService beneficiaryService);

	Set<GetServiceDTO> beneficiaryServiceToGetServiceDTO(Iterable<BeneficiaryService> beneficiaryServices);

	@Mapping(expression = "java( beneficiaryServiceToGetServiceDTO(beneficiary.getBeneficiaryServices()) )", target = "services")
	@Mapping(source = "beneficiary.person.cityId", target = "cityId")
	@Mapping(source = "beneficiary.person.firstName", target = "firstName")
	@Mapping(source = "beneficiary.person.lastName", target = "lastName")
	@Mapping(source = "beneficiary.person.firstNameAr", target = "firstNameAr")
	@Mapping(source = "beneficiary.person.lastNameAr", target = "lastNameAr")
	@Mapping(source = "beneficiary.person.sex", target = "sex")
	@Mapping(source = "beneficiary.person.phoneNumber", target = "phoneNumber")
	@Mapping(source = "beneficiary.person.address", target = "address")
	@Mapping(source = "beneficiary.person.addressAr", target = "addressAr")
	@Mapping(source = "beneficiary.person.birthDate", target = "birthDate")
	@Mapping(source = "beneficiary.person.identityCode", target = "identityCode")
	@Mapping(source = "beneficiary.person.typeIdentityId", target = "typeIdentityId")
	@Mapping(source = "beneficiary.person.pictureUrl", target = "pictureUrl")
	@Mapping(source = "beneficiary.beneficiaryStatut.id", target = "beneficiaryStatutId")
	GetListDTO beneficiaryToGetListDTO(Beneficiary beneficiary);


	@Mapping(target = "beneficiaryStatutId", ignore = true)
	Iterable<GetListDTO> beneficiaryToGetListDTO(Iterable<Beneficiary> beneficiaries);


	////////////////////////////////////////////////////////////////////////////////////////////////


	NoteBeneficiaryDTO beneficiaryNoteToBeneficiaryNoteDTO(NoteBeneficiary beneficiaryNote);

	@Mapping(target = "beneficiaryId", ignore = true)
	@Mapping(source = "document.id", target = "document.id")
    DocumentBeneficiaryDTO beneficiaryDocumentToBeneficiaryDocumentDTO(DocumentBeneficiary beneficiaryDocument);

	@Mapping(target = "beneficiary", ignore = true)
    DocumentBeneficiary beneficiaryDocumentDTOToBeneficiaryDocument(DocumentBeneficiaryDTO documentDTO);

	@Mapping(target = "beneficiary", ignore = true)
	@Mapping(source = "cityId", target = "city.id")
	@Mapping(source = "schoolYearId", target = "schoolYear.id")
	@Mapping(source = "schoolLevelId", target = "schoolLevel.id")
	@Mapping(source = "honorId", target = "honor.id")
	@Mapping(source = "majorId", target = "major.id")
	@Mapping(source = "educationSystemTypeId", target = "educationSystemType.id")
	EducationDTO educationToEducationDTO(Education education);

	@Mapping(target = "beneficiary", ignore = true)
	@Mapping(source = "city.id", target = "cityId")
	@Mapping(source = "schoolYear.id", target = "schoolYearId")
	@Mapping(source = "schoolLevel.id", target = "schoolLevelId")
	@Mapping(source = "honor.id", target = "honorId")
	@Mapping(source = "major.id", target = "majorId")
	@Mapping(source = "educationSystemType.id", target = "educationSystemTypeId")
	Education educationDTOToEducation(EducationDTO educationDTO);

	@Mapping(target = "beneficiary", ignore = true)
	@Mapping(source = "serviceId", target = "service.id")
	@Mapping(source = "statusId", target = "status.id")
	BeneficiaryServiceDTO beneficiaryServiceToBeneficiaryServiceDTO(BeneficiaryService beneficiaryService);

	@Mapping(target = "beneficiary", ignore = true)
	@Mapping(source = "service.id", target = "serviceId")
	@Mapping(source = "status.id", target = "statusId")
	BeneficiaryService beneficiaryServiceDTOToBeneficiaryService(BeneficiaryServiceDTO beneficiaryServiceDTO);

	@Mapping(target = "beneficiary", ignore = true)
	@Mapping(source = "epsId", target = "eps.id")
	EpsResidentDTO epsResidentToEpsResidentDTO(EpsResident epsResident);

	@Mapping(target = "beneficiary", ignore = true)
	@Mapping(source = "eps.id", target = "epsId")
	EpsResident epsResidentDTOToEpsResident(EpsResidentDTO epsResidentDTO);

	@Mapping(target = "beneficiary", ignore = true)
	@Mapping(source = "typeId", target = "type.id")
	DiseaseTreatmentDTO DiseaseTreatmentToDiseaseTreatmentDTO(DiseaseTreatment diseaseTreatment);

	@Mapping(target = "beneficiary", ignore = true)
	@Mapping(source = "type.id", target = "typeId")
	DiseaseTreatment diseaseTreatmentDTOToDiseaseTreatment(DiseaseTreatmentDTO diseaseTreatmentDTO);

	@Mapping(target = "beneficiary", ignore = true)
	@Mapping(source = "handicapTypeId", target = "handicapType.id")
	BeneficiaryHandicapDto beneficiaryHandicapToBeneficiaryHandicapDto(BeneficiaryHandicap beneficiaryHandicap);

	@Mapping(target = "beneficiary", ignore = true)
	@Mapping(source = "handicapType.id", target = "handicapTypeId")
	BeneficiaryHandicap beneficiaryHandicapDtoToBeneficiaryHandicap(BeneficiaryHandicapDto beneficiaryHandicapDto);

	@Mapping(target = "beneficiary", ignore = true)
	@Mapping(source = "scholarshipId", target = "scholarship.id")
	@Mapping(source = "currencyId", target = "currency.id")
	ScholarshipBeneficiaryDTO scholarshipBeneficiaryToScholarshipBeneficiaryDTO(ScholarshipBeneficiary scholarshipBeneficiary);

	@Mapping(target = "beneficiary", ignore = true)
	@Mapping(source = "scholarship.id", target = "scholarshipId")
	ScholarshipBeneficiary scholarshipBeneficiaryDTOToScholarshipBeneficiary(ScholarshipBeneficiaryDTO scholarshipBeneficiaryDTO);

	@Mapping(target = "beneficiary", ignore = true)
	@Mapping(source = "typeIdentityId", target = "typeIdentity.id")
	@Mapping(source = "cityId", target = "city.id")
	@Mapping(source = "schoolLevelId", target = "schoolLevel.id")
	@Mapping(source = "professionId", target = "profession.id")
	@Mapping(source = "accommodationTypeId", target = "accommodationType.id")
	@Mapping(source = "accommodationNatureId", target = "accommodationNature.id")
	@Mapping(source = "categoryBeneficiaryId", target = "categoryBeneficiary.id")
	@Mapping(source = "typeKafalatId", target = "typeKafalat.id")
	@Mapping(source = "sourceBeneficiaryId", target = "sourceBeneficiary.id")
	PersonDTO personToPersonDTO(Person person);


	@Mapping(target = "beneficiary", ignore = true)
	@Mapping(source = "city.id", target = "cityId")
	@Mapping(source = "typeIdentity.id", target = "typeIdentityId")
	@Mapping(source = "schoolLevel.id", target = "schoolLevelId")
	@Mapping(source = "profession.id", target = "professionId")
	@Mapping(source = "accommodationType.id", target = "accommodationTypeId")
	@Mapping(source = "accommodationNature.id", target = "accommodationNatureId")
	@Mapping(source = "categoryBeneficiary.id", target = "categoryBeneficiaryId")
	@Mapping(source = "typeKafalat.id", target = "typeKafalatId")
	@Mapping(source = "sourceBeneficiary.id", target = "sourceBeneficiaryId")
	@Mapping(target = "typePriseEnChargeIdsList", ignore = true)
	Person personDTOToPerson(PersonDTO personDTO);

	@Mapping(target = "person", ignore = true)
	@Mapping(source = "cardTypeId", target = "cardType.id")
    BankCardDTO bankCardToBankCardDTO(BankCard bankCard);

	@Mapping(target = "person", ignore = true)
	@Mapping(source = "cardType.id", target = "cardTypeId")
	BankCard bankCardDTOToBankCard(BankCardDTO bankCardDTO);


	@Mapping(target = "person", ignore = true)
	@Mapping(target = "externalIncomes", ignore = true)
	@Mapping(source = "familyRelationshipId", target = "familyRelationship.id")
	FamilyMemberDTO familyMemberToFamilyMemberDTO(FamilyMember familyMember);

	@Mapping(target = "person", ignore = true)
	@Mapping(target = "externalIncomes", ignore = true)
	@Mapping(source = "familyRelationship.id", target = "familyRelationshipId")
	FamilyMember familyMemberDTOToFamilyMember(FamilyMemberDTO familyMemberDTO);


	@Mapping(target = "familyMembers", ignore = true)
	FamilyDTO familyToFamilyDTO(Family family);

	////////////////////////////////////////////////////////////////////////

	//take in charge beneficiary mappers
	@Mapping(target = "beneficiary", ignore = true)
	TakenInChargeBeneficiary takenInChargeBeneficiaryToTakenInChargeBeneficiaryDTO(TakenInChargeBeneficiaryDTO takenInChargeBeneficiaryDTO);

	@Mapping(target = "beneficiary", ignore = true)
	TakenInChargeBeneficiaryDTO takenInChargeBeneficiaryDTOToTakenInChargeBeneficiary(TakenInChargeBeneficiary takenInChargeBeneficiary);

	@Mapping(target = "takenInCharge", ignore = true)
	TakenInChargeDonor takenInChargeDonorTotTakenInChargeDonorDTO(TakenInChargeDonorDTO takenInChargeDonorDTO);

	@Mapping(target = "takenInCharge", ignore = true)
	TakenInChargeDonorDTO takenInChargeDonorDTOToTakenInChargeDonor(TakenInChargeDonor takenInChargeDonor);

	@Mapping(target = "takenInChargeDonor", ignore = true)
	TakenInChargeOperation takenInChargeOperationDTOToTakenInChargeOperation(TakenInChargeOperationDTO takenInChargeOperationDTO);

	@Mapping(target = "takenInChargeDonor", ignore = true)
	TakenInChargeOperationDTO takenInChargeOperationToTakenInChargeOperationDTO(TakenInChargeOperation takenInChargeOperation);

	@Mapping(target = "takenInChargeDonors", ignore = true)
	@Mapping(target = "documentsDonors", ignore = true)
	Donor donorDTOToDonor(DonorDTO donorDTO);

	@Mapping(target = "takenInChargeDonors", ignore = true)
	@Mapping(target = "documentDonors", ignore = true)
	DonorDTO donorToDonorDTO(Donor donor);

	@Mapping(target = "donor", ignore = true)
	@Mapping(target = "documentDonations", ignore = true)
	@Mapping(target = "donationProductNatures", ignore = true)
	Donation donationDTOToDonation(DonationDTO donationDTO);

	@Mapping(target = "donor", ignore = true)
	@Mapping(target = "documentDonations", ignore = true)
	@Mapping(target = "donationProductNatures", ignore = true)
	DonationDTO donationToDonationDTO(Donation donation);


	@Mapping(target = "takenInChargeBeneficiaries", ignore = true)
	TakenInChargeDTO takenInChargeToTakenInChargeDTO(TakenInCharge takenInCharge);


	@Mapping(target = "takenInChargeBeneficiaries", ignore = true)
	TakenInCharge takenInChargeDTOToTakenInCharge(TakenInChargeDTO takenInChargeDTO);


	@Mapping(source = "id", target = "personId")
	@Mapping(target = "typePriseEnChargeIds", ignore = true)
	BeneficiaryAddDTO PersonTomapBeneficiaryAddDTO(Person person);

	@Mapping(source = "id", target = "personId")
	BeneficiaryAddDTO PersonDtoTomapBeneficiaryAddDTO(PersonDTO person);

	@Mapping(target = "centreEps", ignore = true)
	@Mapping(target = "typeIdentite", ignore = true)
	@Mapping(target = "profession", ignore = true)
	@Mapping(source = "firstName", target = "prenom")
	@Mapping(source = "lastName", target = "nom")
	@Mapping(source = "firstNameAr", target = "prenomArabe")
	@Mapping(source = "lastNameAr", target = "nomArabe")
	@Mapping(source = "birthDate", target = "dateDeNaissance")
	@Mapping(source = "sex", target = "sexe")
	@Mapping(source = "phoneNumber", target = "telephone")
	@Mapping(source = "identityCode", target = "numIdentite")
	@Mapping(source = "address", target = "adresse")
	@Mapping(source = "addressAr", target = "adresseArabe")
	@Mapping(source = "addedYear", target = "AnneeAjout")
	@Mapping(source = "accountingCode", target = "codeComptable")
	BeneficiaryAuditDTO beneficiaryDtoToBeneficiaryAudit(BeneficiaryAddDTO beneficiaryAddDTO);



	@Mapping(source = "person.firstName", target = "firstName")
	@Mapping(source = "person.lastName", target = "lastName")
	@Mapping(source = "beneficiary.independent", target = "type", qualifiedByName = "mapType")
	@Mapping(source = "person.phoneNumber", target = "phoneNumber")
	@Mapping(source = "person.address", target = "address")
	@Mapping(source = "person.city.name", target = "city")
	@Mapping(source = "person.city.region.name", target = "region")
	@Mapping(source = "person.info.country.nameFrench", target = "country")
	@Mapping(source = "person.identityCode", target = "identityCode")
	@Mapping(source = "person.typeIdentity.name", target = "typeIdentity")
	@Mapping(source = "person.email", target = "email")
	@Mapping(source = "person.sex", target = "sex")
	@Mapping(source = "person.birthDate", target = "birthDate")
	@Mapping(target = "code", expression = "java(beneficiary.getCodeBeneficiary() != null ? beneficiary.getCodeBeneficiary() : beneficiary.getCode())")
	BeneficiaryExportDTO beneficiaryDTOToBeneficiaryExportDTO(BeneficiaryDTO beneficiary);

	default String getServiceName(Set<BeneficiaryServiceDTO> beneficiaryServices) {
		if (beneficiaryServices != null && !beneficiaryServices.isEmpty()) {
			return beneficiaryServices.iterator().next().getService().getName();
		}
		return null;
	}

	default String getStatusName(Set<BeneficiaryServiceDTO> beneficiaryServices) {
		if (beneficiaryServices != null && !beneficiaryServices.isEmpty()) {
			return beneficiaryServices.iterator().next().getStatus().getName();
		}
		return null;
	}

	@Named("mapType")
	default String mapType(Boolean independent) {
		return independent ? "Indépendant" : "Membre de Famille";
	}


	@Mapping(source = "beneficiary.person.firstName", target = "firstName")
	@Mapping(source = "beneficiary.person.lastName", target = "lastName")
	@Mapping(source = "beneficiary.person.phoneNumber", target = "phoneNumber")
	@Mapping(source = "beneficiary.person.birthDate", target = "birthDate")
	@Mapping(source = "beneficiary.zone", target = "zone")
	@Mapping(source = "beneficiary.beneficiaryStatut", target = "beneficiaryStatut")
	@Mapping(source = "beneficiary.beneficiaryStatut.nameStatut", target = "statut")
	BeneficiaryAideComplemenatireDTO beneficiarytoBeneficiaryAideComplemenatireDTO(Beneficiary beneficiary);


	@Mapping(source = "person.firstName", target = "firstName")
	@Mapping(source = "person.lastName", target = "lastName")
	@Mapping(source = "person.sex", target = "sex")
	@Mapping(source = "person.phoneNumber", target = "phoneNumber")
	@Mapping(source = "person.identityCode", target = "identityCode")
	BeneficiaryForAideComplementaireDTO toBeneficiaryForAideComplementaireDTO(Beneficiary beneficiary);


	//Beneficiary to GetBeneficiariesForTakeInchargeDTO
	@Mapping(source = "person.firstName", target = "firstName")
	@Mapping(source = "person.lastName", target = "lastName")
	@Mapping(source = "zone.name", target = "zoneName")
	@Mapping(source = "beneficiaryStatut.nameStatut", target = "beneficiaryStatut")
	@Mapping(source ="person.categoryBeneficiaryId", target = "categoryBeneficiaryId")
	GetBeneficiariesForTakeInchargeDTO toGetBeneficiariesForTakeInchargeDTO(Beneficiary beneficiary);
}