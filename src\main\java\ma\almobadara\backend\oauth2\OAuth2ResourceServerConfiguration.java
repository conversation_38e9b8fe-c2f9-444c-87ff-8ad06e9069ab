package ma.almobadara.backend.oauth2;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "oauth2.resourceserver")
public class OAuth2ResourceServerConfiguration {
	private String tenantId;
	private String clientId;
	private String jwkSetUri;
	private String issuerUri;
}
