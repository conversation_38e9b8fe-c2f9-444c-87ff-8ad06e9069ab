package ma.almobadara.backend.repository.administration;

import ma.almobadara.backend.model.administration.SousZone;
import ma.almobadara.backend.model.administration.Tag;
import ma.almobadara.backend.model.administration.TypeTag;
import ma.almobadara.backend.model.administration.Zone;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface TagRepository extends JpaRepository<Tag, Long> {

    @Query("SELECT t FROM Tag t WHERE t.name = :name and t.typeTag.id=:tagType")
    Optional<Tag> findByName(@Param("name") String name,@Param("tagType") Long tagType);

    @Query("SELECT t FROM Tag t WHERE t.name = :name and t.typeTag.name=:tagType")
    Optional<Tag> findByNameAndType(@Param("name") String name,@Param("tagType") String tagType);
}
