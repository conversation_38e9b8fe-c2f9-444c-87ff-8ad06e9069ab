package ma.almobadara.backend.dto.referentiel;

import lombok.*;
import org.springframework.hateoas.RepresentationModel;

import java.io.Serializable;
import java.util.Set;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class CategoryDTO extends RepresentationModel<CategoryDTO> implements Serializable {

	private static final long serialVersionUID = -287177998752059310L;

	private Long id;

	private String code;
	private String name;
	private String nameAr;
	private String nameEn;
	private Set<ServiceDTO> services;

	private Set<StatusDTO> statuses;

}
