package ma.almobadara.backend.service.Donation;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.micrometer.common.util.StringUtils;
import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.*;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.Audit.AuditApplicationService;
import ma.almobadara.backend.clients.RefFeignClient;
import ma.almobadara.backend.config.Messages;
import ma.almobadara.backend.controller.referentiel.RefController;
import ma.almobadara.backend.dto.CityWithRegionAndCountryDTO;
import ma.almobadara.backend.dto.administration.TagDTO;
import ma.almobadara.backend.dto.communs.DocumentAndEntityDto;
import ma.almobadara.backend.dto.communs.DocumentDTO;
import ma.almobadara.backend.dto.donation.*;
import ma.almobadara.backend.dto.donor.DonorDTO;
import ma.almobadara.backend.dto.donor.DonorMoralDTO;
import ma.almobadara.backend.dto.donor.DonorPhysicalDTO;
import ma.almobadara.backend.dto.mobile.MobileDonationDto;
import ma.almobadara.backend.dto.exportentities.DonationExportDTO;
import ma.almobadara.backend.dto.exportentities.ExportFileDTO;
import ma.almobadara.backend.dto.referentiel.*;
import ma.almobadara.backend.enumeration.BudgetLineStatus;
import ma.almobadara.backend.enumeration.EntitiesToExport.DonationExportHeaders;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.mapper.*;
import ma.almobadara.backend.model.administration.ServiceCollectEps;
import ma.almobadara.backend.model.administration.Tag;
import ma.almobadara.backend.model.administration.Taggable;
import ma.almobadara.backend.model.aideComplemenatire.AideComplementaire;
import ma.almobadara.backend.model.donation.BudgetLine;
import ma.almobadara.backend.model.donation.Donation;
import ma.almobadara.backend.model.donation.DonationHistory;
import ma.almobadara.backend.model.donation.DonationProductNature;
import ma.almobadara.backend.model.donor.Donor;
import ma.almobadara.backend.model.donor.DonorAnonyme;
import ma.almobadara.backend.model.donor.DonorMoral;
import ma.almobadara.backend.model.donor.DonorPhysical;
import ma.almobadara.backend.model.service.Services;
import ma.almobadara.backend.repository.administration.ServiceCollectEpsRepository;
import ma.almobadara.backend.repository.administration.TaggableRepository;
import ma.almobadara.backend.repository.aideComplemenatire.AideComplementaireRepository;
import ma.almobadara.backend.repository.donation.BudgetLineRepository;
import ma.almobadara.backend.repository.donation.DonationHistoryRepository;
import ma.almobadara.backend.repository.donation.DonationProductNatureRepository;
import ma.almobadara.backend.repository.donation.DonationRepository;
import ma.almobadara.backend.repository.administration.AssistantRepository;
import ma.almobadara.backend.repository.donor.DonorRepository;
import ma.almobadara.backend.repository.services.ServicesRepository;
import ma.almobadara.backend.service.administration.ServiceCollectEpsService;
import ma.almobadara.backend.service.communs.DocumentService;
import ma.almobadara.backend.service.communs.ExportService;
import ma.almobadara.backend.service.donor.MinioService;
import ma.almobadara.backend.util.times.TimeWatch;
import org.springframework.beans.BeanUtils;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static ma.almobadara.backend.service.administration.CacheAdUserService.getUsernameFromJwt;
import static ma.almobadara.backend.util.constants.GlobalConstants.*;

@RefreshScope
@Service
@RequiredArgsConstructor
@Slf4j
public class DonationService {
    private final DonationHistoryRepository donationHistoryRepository;
    private final DonationHistoryMapper donationHistoryMapper;
    private final AideComplementaireRepository aideComplementaireRepository;
    private final BudgetLineRepository budgetLineRepository;
    private final DonationMapper donationMapper;
    private final DonorRepository donorRepository;
    private final DonationRepository donationRepository;
    private final MinioService minioService;
    private final RefFeignClient refFeignClient;
    private final DonationProductNatureRepository donationProductNatureRepository;
    private final DonationProductNatureMapper donationProductNatureMapper;
    private final DonorPhysicalMapper donorPhysicalMapper;
    private final DonorMoralMapper donorMoralMapper;
    private final Messages messages;
    private final RefController refController;
    private final ExportEntitiesMapper exportEntitiesMapper;
    private final ServiceCollectEpsRepository serviceCollectEpsRepository;
    private final EntityManager entityManager;
    private final AuditApplicationService auditApplicationService;
    private final ExportService exportService;
    private final BudgetLineMapper budgetLineMapper;
    private final DonorAnonymeMapper donorAnonymeMapper;
    private final ServicesRepository servicesRepository;
    private final TaggableRepository taggableRepository;
    private final DocumentService documentService;
    private final AssistantRepository assistantRepository;


    @Transactional
    public DonationDTO addDonation(DonationDTO donationDTO) throws TechnicalException, IOException {

        TimeWatch watch = TimeWatch.start();

        // Preliminary Checks and Setup
        if (donationDTO == null) {
            throw new TechnicalException(messages.get(DONATION_NOT_FOUND));
        }
        log.debug("Start service addDonation with type {} and value of {}", donationDTO.getType(), donationDTO.getValue());

        String initialJsonData = null;
        String newDonationJson;
        Donation existingDonation;

        Donation existingDonationfordto = new Donation();
        // Handling Existing Donation
        if (donationDTO.getId() != null) {
            existingDonation = donationRepository.findById(donationDTO.getId())
                    .orElseThrow(() -> new TechnicalException("Donation not found"));
            BeanUtils.copyProperties(existingDonation, existingDonationfordto);
            DonationDTO donationDTO1 = donationMapper.donationToDonationDTO(existingDonation);
            String canalName = getCanalDonationName(donationDTO1);

            initialJsonData = existingDonation.toAuditString(getCurrencyName(donationDTO1), canalName,getDonorName(existingDonation));
        }

        // Mapping and Processing
        donationDTO.setCreatedAt(LocalDateTime.now());
        Donation donation = donationMapper.donationDTOToDonation(donationDTO);
        handleDonorInformation(donationDTO, donation);

        // Generate Code for New Donation
        if (donationDTO.getId() == null) {
            String code = generateDonationCode(donation);
            donation.setCode(code);
        }

        // Handle Currency
        if (donationDTO.getEnableCurrency() != null && !donationDTO.getEnableCurrency() && donationDTO.getId() != null) {
            donation.setValueCurrency(null);
            donation.setCurrencyId(null);
        }

        // Save Donation
        Donation savedDonation = donationRepository.save(donation);
        processKafalatOrNonIdentified(
                donationDTO,
                "Non Identifié",
                donationDTO.getNonIdentifiedValue(),
                donationDTO.getIdNonIdentifed(),
                donationDTO.getNonIdentifiedCurrency(),
                donationDTO.getNonIdentifiedComment(),
                donationDTO.getNonIdentifiedValueCurrency(),
                donationDTO.getNonIdentifiedStatus()
        );


        // Handle Budget Lines
        if (donationDTO.getId() != null) {
            // Fetch the existing donation to update budget lines
            Donation existingDonationBL = donationRepository.findById(donationDTO.getId())
                    .orElseThrow(() -> new RuntimeException("Donation not found"));


            if (donationDTO.getBudgetLines() != null && !donationDTO.getBudgetLines().isEmpty()) {
                handleBudgetLines(donationDTO, existingDonationBL);
            }
        } else {
            // Handle the case where it's a new donation and budget lines are being saved with it
            if (donationDTO.getBudgetLines() != null && !donationDTO.getBudgetLines().isEmpty()) {
                handleBudgetLines(donationDTO, savedDonation);
            }
        }

        // Handle Documenif(
        if(savedDonation.getId() != null && donationDTO.getId() == null) {
            // we should pass also the id
            handleDonationDocuments(donationDTO , savedDonation.getId());
        }


        // Map to DTO and Get Related Information
        DonationDTO newDonationDTO = donationMapper.donationToDonationDTO(savedDonation);
        String currencyName = getCurrencyName(newDonationDTO);
        String canalName = getCanalDonationName(newDonationDTO);
        taggableRepository.deleteAllByTaggableIdAndTaggableType(newDonationDTO.getId(),"donation");

        //taggableRepository.deleteAllByTaggableIdAndTaggableType(newDonationDTO.getId(),"donation");
        if(donationDTO.getTags()!=null) {
            donationDTO.getTags().forEach(tagDTO -> {
                Tag tag = new Tag();
                tag.setId(tagDTO.getId());
                Taggable taggable=new Taggable();
                taggable.setTaggableId(newDonationDTO.getId());
                taggable.setTaggableType("donation");
                taggable.setTag(tag);
                taggableRepository.save(taggable);
            });
        }



        // Generate Audit String for New Donation
        newDonationJson = savedDonation.toAuditString(currencyName, canalName,getDonorName(savedDonation));


        // Audit Processing
        if (donationDTO.getId() != null) {
            auditApplicationService.audit("Modification de la Donation : " + savedDonation.getCode(),
                    getUsernameFromJwt(), "Update Donation",
                    initialJsonData, newDonationJson, DONATION2, UPDATE);
        } else {
            auditApplicationService.audit("Ajout nouvelle Donation : " + savedDonation.getCode(),
                    getUsernameFromJwt(), "Add New Donation",
                    null, newDonationJson, DONATION2, CREATE);
        }

        log.debug("End service addDonation with donation created with id {}, took {}", newDonationDTO.getId(), watch.toMS());
        return newDonationDTO;
    }
    public void handleBudgetLines(DonationDTO donationDTO, Donation donation) throws TechnicalException {
        if (donationDTO.getBudgetLines() == null || donationDTO.getBudgetLines().isEmpty()) {
            throw new TechnicalException("Budget lines are missing for the donation");
        }

        // Retrieve existing budget lines
        List<BudgetLine> existingBudgetLines = budgetLineRepository.findByDonationId(donation.getId());
        List<BudgetLine> updatedBudgetLines = new ArrayList<>();

        // Log existing budget lines for debugging
        log.debug("Existing budget lines: {}", existingBudgetLines);

        // Get the reception date from the donationDTO as a Date
        Date receptionDate = donationDTO.getReceptionDate(); // Adjust this line according to how you get the reception date

        // Convert Date to LocalDateTime
        LocalDateTime createdAtDateTime = null;
        if (receptionDate != null) {
            createdAtDateTime = LocalDateTime.ofInstant(receptionDate.toInstant(), ZoneId.systemDefault());
        }
        // Iterate through each BudgetLineDTO in the donationDTO
        for (BudgetLineDTO budgetLineDTO : donationDTO.getBudgetLines()) {
            BudgetLine budgetLine;
            if (budgetLineDTO.getId() != null) {
                budgetLine = existingBudgetLines.stream()
                        .filter(bl -> bl.getId().equals(budgetLineDTO.getId()))
                        .findFirst()
                        .orElseThrow(() -> new TechnicalException("Budget line not found for update: " + budgetLineDTO.getId()));
                if(budgetLine.getService() != null && !Objects.equals(budgetLine.getService().getId(), budgetLineDTO.getService().getId())) {
                    budgetLine.setService(servicesRepository.findById(budgetLineDTO.getService().getId())
                            .orElseThrow(() -> new RuntimeException("Service not found")));
                }

                if(budgetLine.getServiceCollectEps() != null && !Objects.equals(budgetLine.getServiceCollectEps().getId(), budgetLineDTO.getServiceCollectEpsId())) {
                    budgetLine.setServiceCollectEps(serviceCollectEpsRepository.findById(budgetLineDTO.getServiceCollectEpsId())
                            .orElseThrow(() -> new RuntimeException("service Collect Eps  not found")));
                }
                budgetLine.setAmount(budgetLineDTO.getAmount());
                budgetLine.setComment(budgetLineDTO.getComment());
                budgetLine.setCreatedAt(createdAtDateTime);
                // we shoudl manualy set the part of the prudct category from a list of long to
                if(budgetLineDTO.getProductCategory() != null) {
                    budgetLine.setProductsCategory(budgetLineDTO.getProductCategory());
                }
            } else {
                // Add new budget line
                budgetLine = budgetLineMapper.budgetLineDTOToBudgetLine(budgetLineDTO);
                budgetLine.setCreatedAt(createdAtDateTime);
                if(budgetLineDTO !=null && budgetLineDTO.getService() !=null && budgetLineDTO.getServiceCollectEpsId()!=null) {
                    budgetLine.setServiceCollectEps(serviceCollectEpsRepository.findById(budgetLineDTO.getServiceCollectEpsId())
                            .orElseThrow(() -> new RuntimeException("service Collect Eps  not found")));
                }// Set createdAt to the reception date
                budgetLine.setCode(generateBudgetLineCode(budgetLine));
                if(budgetLineDTO.getProductCategory() != null) {
                    budgetLine.setProductsCategory(budgetLineDTO.getProductCategory());
                }

                // Log the new budget line being added
                log.debug("Adding new budget line: {}", budgetLine);
            }
            budgetLine.setDonation(donation);

            // Add to the list of updated budget lines
            updatedBudgetLines.add(budgetLine);
        }

        // Handle deletion of budget lines not present in donationDTO
        for (BudgetLine existingBudgetLine : existingBudgetLines) {
            boolean existsInUpdatedLines = updatedBudgetLines.stream()
                    .anyMatch(updatedLine -> updatedLine.getId() != null && updatedLine.getId().equals(existingBudgetLine.getId()));

            if (!existsInUpdatedLines) {
                // Budget line exists in the current donation but not in the incoming DTO, so delete it
                log.debug("Deleting budget line: {}", existingBudgetLine);
                budgetLineRepository.delete(existingBudgetLine);
            }
        }

        // Set the updated budget lines to the donation entity
        donation.setBudgetLines(updatedBudgetLines);

        // Log the final budget lines for debugging
        log.debug("Updated budget lines: {}", updatedBudgetLines);
    }
    // add funtions that will handel to call this funions  public DocumentDTO createDocumentAndAssignToEntity(DocumentAndEntityDto documentAndEntityDto) throws

    public void handleDonationDocuments(DonationDTO donationDTO, Long donationId)
            throws TechnicalException, IOException {
        if (donationDTO == null || donationDTO.getDocuments() == null || donationDTO.getDocuments().isEmpty()) {
            log.warn("Skipping document creation: No documents attached to the donation.");
            return;
        }

        // Iterate over the list of documents in the donationDTO
        for (DocumentDTO documentDTO : donationDTO.getDocuments()) {
            // Skip if file or fileName is missing in any document
            if (documentDTO.getFile() == null || documentDTO.getFileName() == null) {
                log.warn("Skipping document creation: No file or file name attached to the document.");
                continue;  // Skip to the next document if file or file name is missing
            }

            // Set the common details for all documents
            documentDTO.setDocumentDate(Date.from(LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant()));
            documentDTO.setLabel(documentDTO.getLabel() != null ? documentDTO.getLabel() : null);
            documentDTO.setComment(documentDTO.getComment() != null ? documentDTO.getComment() : null);
            documentDTO.setExpiryDate(documentDTO.getExpiryDate() != null ? documentDTO.getExpiryDate() : null );

            // Set document type, temporarily using type ID 9 (AUTRE) for testing purposes
            TypeDocumentDonorDTO typeDocumentDonorDTO = new TypeDocumentDonorDTO();
            typeDocumentDonorDTO.setId(14L);
            documentDTO.setType(typeDocumentDonorDTO);

            // Create DocumentAndEntityDto to assign the document to the donation
            DocumentAndEntityDto documentAndEntityDto = DocumentAndEntityDto.builder()
                    .documentDTO(documentDTO)
                    .entityId(donationId)
                    .entityType(DONATION2)  // Ensure this is the correct entity type
                    .build();

            // Call the document service to create the document and assign it to the donation
            documentService.createDocumentAndAssignToEntity(documentAndEntityDto);
        }
    }

    public  void processKafalatOrNonIdentified(DonationDTO donationDTO, String type, Double value, Long budgetLineId, CurrencyDTO currency, String comment, Double valueCurrency, String status) {
        if (value != null && value > 0) {
            BudgetLineDTO budgetLineDTO = new BudgetLineDTO();

            // Check if we are updating an existing budget line
            if (budgetLineId != null) {
                BudgetLine budgetLine = budgetLineRepository.findById(budgetLineId)
                        .orElseThrow(() -> new RuntimeException("Budget line not found"));
                budgetLineDTO = budgetLineMapper.budgetLineToBudgetLineDTO(budgetLine);
            }

            // Set common properties
            budgetLineDTO.setAmount(value);
            budgetLineDTO.setType(type);
            budgetLineDTO.setCurrency(currency);
            budgetLineDTO.setValueCurrency(valueCurrency);
            budgetLineDTO.setCreatedAt(donationDTO.getCreatedAt());
            budgetLineDTO.setComment(comment);
            budgetLineDTO.setStatus(status);


            // Initialize budget lines if null
            if (donationDTO.getBudgetLines() == null) {
                donationDTO.setBudgetLines(new ArrayList<>());
            }

            // Add the budget line to donationDTO
            donationDTO.getBudgetLines().add(budgetLineDTO);
        } else if (donationDTO.getId() != null) {
            // If value is null, check and delete existing budget lines of this type
            Donation donationExist = donationRepository.findById(donationDTO.getId())
                    .orElseThrow(() -> new RuntimeException("Donation not found"));
            if (donationExist.getBudgetLines() != null) {
                List<BudgetLine> budgetLines = donationExist.getBudgetLines();
                for (BudgetLine budgetLine : budgetLines) {
                    if (budgetLine.getType().equals(type)) {
                        budgetLineRepository.delete(budgetLine);
                    }
                }
            }
        }
    }


    // de clare a map to store the counter of the budget line
    public final Map<String, Integer> budgetLineCounterMap = new HashMap<>();

    public String generateBudgetLineCode(BudgetLine budgetLine) {
        String baseCode = "BL";
        if(budgetLine.getNatureBudgetLine() != null && budgetLine.getNatureBudgetLine()) {
            baseCode = "BN";
        }
        // Create a date part for the code using LocalDateTime
        LocalDateTime createdAt = budgetLine.getCreatedAt();
        String datePart = createdAt.format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        // Initialize or increment the counter for this date part
        int counter = budgetLineCounterMap.getOrDefault(datePart, 0) + 1;
        budgetLineCounterMap.put(datePart, counter);

        // Construct the final code
        String code = baseCode + datePart + String.format("%03d", counter);

        // Ensure the code is unique
        while (budgetLineRepository.findByCode(code) != null) {
            counter++;
            code = baseCode + datePart + String.format("%03d", counter);
        }

        return code; // Return the generated unique budget line code
    }




    public void manageRemainingBudgetLine(BudgetLineDTO budgetLineDTO, BudgetLine budgetLine) {
        if (budgetLine.getStatus() == BudgetLineStatus.REMAINING &&
                Boolean.TRUE.equals(budgetLineDTO.getMakeItAvailable())) {

            // Create and save the donation history
            DonationHistory donationHistory = createDonationHistory(budgetLineDTO, budgetLine);
            donationHistoryRepository.save(donationHistory);

            // Update budget line and DTO status
            budgetLineDTO.setStatus("DISPONIBLE");
            budgetLineDTO.setAmountByBeneficiary(null);
            budgetLine.setAideComplementaire(null);
            budgetLine.setExecutionDate(null);
        }
    }

    public DonationHistory createDonationHistory(BudgetLineDTO budgetLineDTO, BudgetLine budgetLine) {
        AideComplementaire aideComplementaire = aideComplementaireRepository.findById(
                        budgetLine.getAideComplementaire().getId())
                .orElseThrow(() -> new RuntimeException("Aide Complementaire not found"));

        Services oldService = servicesRepository.findById(budgetLine.getService().getId())
                .orElseThrow(() -> new RuntimeException("Old Service not found"));

        Services newService = servicesRepository.findById(budgetLineDTO.getService().getId())
                .orElseThrow(() -> new RuntimeException("New Service not found"));

        return DonationHistory.builder()
                .amount(budgetLine.getAmount())
                .oldService(oldService)
                .newService(newService)
                .aideComplementaire(aideComplementaire)
                .donor(budgetLine.getDonation().getDonor())
                .donation(budgetLine.getDonation())
                .executionDate(Date.from(budgetLine.getExecutionDate()
                        .atZone(ZoneId.systemDefault()).toInstant()))
                .build();
    }

    public void handleDonorInformation(DonationDTO donationDTO, Donation donation) {
        if (!donationDTO.isIdentifiedDonor()) {
            donation.setDonor(null);
        } else if (donationDTO.getDonor() != null && donationDTO.getDonor().getId() != null) {
            Optional<Donor> donorOptional = donorRepository.findById(donationDTO.getDonor().getId());
            if (donorOptional.isPresent()) {
                Donor donor = donorOptional.get();
                if (donationDTO.getId() != null) {
                    Optional<Donation> oldDonation = donationRepository.findById(donationDTO.getId());
                    if (oldDonation.isPresent() && donationDTO.getType().equals(TYPE_DONATION_FINNACIERE)) {
                        // we shoud serch into the buget lines if there is a kafalat line with the id doantion
                        List<BudgetLine> OldBudgetLines = budgetLineRepository.findByDonationId(donationDTO.getId());
                        for (BudgetLine budgetLine : OldBudgetLines) {
                            if (budgetLine.getType().equals("Kafalat")) {
                                donor.setBalance(donor.getBalance() + budgetLine.getAmount());
                            }
                        }
                    }
                } else if (donationDTO.getType().equals(TYPE_DONATION_FINNACIERE)) {
                    if (donationDTO.getKafalatvalue() != null) {
                        donor.setBalance(donor.getBalance() + donationDTO.getKafalatvalue());
                    }
                }
                Donor savedDonor = donorRepository.save(donor);
                donation.setDonor(savedDonor);
            }
        }
    }

    public String generateDonationCode(Donation donation) {
        String baseCode = donation.getDonor() == null ? DONATION_WITH_NO_DONOR : DONATION_WITH_DONOR + donation.getDonor().getCode();
        String code = "";
        boolean uniqueCodeGenerated = false;
        int counter = 1;

        while (!uniqueCodeGenerated) {
            code = baseCode + String.format("%03d", counter);
            if (donationRepository.findByCode(code) == null) {
                uniqueCodeGenerated = true;
            } else {
                counter++;
            }
        }

        return code;
    }



    public DonationDTO addProductToNatureDonation(DonationProductNatureDTO donationProductNatureDTO) throws TechnicalException {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service addProductToDonation {}", donationProductNatureDTO);

        if (donationProductNatureDTO == null || donationProductNatureDTO.getDonation() == null) {
            throw new TechnicalException(NULL_ENTITY);
        }

        Long donationId = donationProductNatureDTO.getDonation().getId();
        Optional<Donation> optionalDonation = donationRepository.findById(donationId);

        if (optionalDonation.isEmpty()) {
            throw new TechnicalException(DONATION_NOT_FOUND);
        }

        Donation donation = optionalDonation.get();
        DonationProductNature donationProductNature = donationProductNatureMapper.donationProductNatureDTOToDonationProductNature(donationProductNatureDTO);
        donationProductNature.setDonation(donation);

        // Check if the product already exists in the donation
        Optional<DonationProductNature> existingProductOptional = donationProductNatureRepository.findByDonationIdAndProductNatureId(donationId, donationProductNatureDTO.getProductNature().getId());

        if (existingProductOptional.isPresent()) {
            // Product exists, update the existing product and adjust the total donation value
            DonationProductNature existingProduct = existingProductOptional.get();
            // Subtract the old product value from the total donation value
            donation.setValue(donation.getValue() - existingProduct.getUnitPrice() * existingProduct.getQuantity());
            // Update the existing product with new values
            existingProduct.setUnitPrice(donationProductNatureDTO.getUnitPrice());
            existingProduct.setQuantity(donationProductNatureDTO.getQuantity());
            if (donationProductNatureDTO.getProductUnit() != null) {
                existingProduct.setProductUnitId(donationProductNatureDTO.getProductUnit().getId());
            }
            // Add the new product value to the total donation value
            donation.setValue(donation.getValue() + existingProduct.getUnitPrice() * existingProduct.getQuantity());
            donationProductNatureRepository.save(existingProduct);
        } else {
            // New product, add its value to the total donation value
            donation.setValue(donation.getValue() + donationProductNatureDTO.getUnitPrice() * donationProductNatureDTO.getQuantity());
            donationProductNatureRepository.save(donationProductNature);
        }

        log.debug("End service addProductToDonation with id {}, took {}", donationProductNature.getId(), watch.toMS());
        return donationMapper.donationToDonationDTO(donation);
    }

    // add budget line to donation


    public void deleteProductFromNatureDonation(Long productId) throws TechnicalException {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service deleteProductFromNatureDonation with id {}", productId);

        if (productId == null) {
            throw new TechnicalException(NULL_ENTITY);
        }

        Optional<DonationProductNature> optionalProduct = donationProductNatureRepository.findById(productId);

        if (optionalProduct.isEmpty()) {
            throw new TechnicalException(DONATION_PRODUCT_NOT_FOUND);
        }

        DonationProductNature existingProduct = optionalProduct.get();
        Donation donation = existingProduct.getDonation();

        // Subtract the product value from the total donation value
        donation.setValue(donation.getValue() - existingProduct.getUnitPrice() * existingProduct.getQuantity());

        donation.getDonationProductNatures().remove(existingProduct);
        donationProductNatureRepository.delete(existingProduct);

        log.debug("End service deleteProductFromNatureDonation with id {}, took {}", productId, watch.toMS());
    }


    public String convertMapToJsonString(Map<String, String> map) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.writeValueAsString(map);
        } catch (Exception e) {
            // Gérer les exceptions de sérialisation JSON ici
            e.printStackTrace();
            return "{}"; // Retourne une chaîne JSON vide en cas d'erreur
        }
    }

    public Page<DonationDTO> getAllDonations(Integer page, Integer size, String searchByDonationType, String searchByDonorType, String searchByNom, String searchByPrenom, String lastNameAr, Double minAmount, Double maxAmount, Double canalDonationId, Date minDate, Date maxDate,Long searchByTagId) throws TechnicalException {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service getAllDonations page {}, size {}, searchByDonationType {}, searchByDonorType {}, searchByNom {}, searchByPrenom {}, minAmount {}, maxAmount {}, minDate {}, maxDate {}", page, size, searchByDonationType, searchByDonorType, searchByNom, searchByPrenom, minAmount, maxAmount, minDate, maxDate);
        String canalName = null;
        if (canalDonationId != null) {
            CanalDonationDTO canalDonationDTO = refFeignClient.getMetCanalDonation(canalDonationId.longValue());

            canalName = canalDonationDTO.getName();
        }
        Pageable pageable = PageRequest.of(page, size);

        Map<String, String> searchParams = new HashMap<>();
        if (searchByDonorType != null) {
            searchParams.put("Type de donateur", searchByDonorType);
        }
        if (lastNameAr != null) {
            searchParams.put("Nom arabe du Donateur", lastNameAr);
        }
        if (searchByDonationType != null) {
            searchParams.put("Type de donation", searchByDonationType);
        }
        if (searchByNom != null) {
            searchParams.put("Nom du Donateur", searchByNom);
        }
        if (minAmount != null) {
            searchParams.put("Montant minimum", String.valueOf(minAmount));
        }
        if (maxAmount != null) {
            searchParams.put("Montant maximum", String.valueOf(maxAmount));
        }
        if (canalName != null) {
            //searchParams.put("Canal de donation", String.valueOf(canalDonationId));
            searchParams.put("Canal de donation", canalName);
        }
        if (minDate != null) {
            searchParams.put("Date de reception minimale", String.valueOf(minDate));
        }
        if (maxDate != null) {
            searchParams.put("Date de reception maximale", String.valueOf(maxDate));
        }

        String jsonSearchParams = convertMapToJsonString(searchParams);


        Page<Donation> listDonations;
        if (searchByDonationType != null || searchByDonorType != null || searchByNom != null || searchByPrenom != null || lastNameAr != null || minAmount != null || maxAmount != null || canalDonationId != null || minDate != null || maxDate != null || searchByTagId != null) {
            listDonations = filterDonations(searchByDonationType, searchByDonorType, searchByNom, searchByPrenom, lastNameAr, minAmount, maxAmount, canalDonationId, minDate, maxDate,searchByTagId, pageable);
            auditApplicationService.audit("Recherche par filtre dans liste des Donations", getUsernameFromJwt(), "Liste des Donations",
                    jsonSearchParams, null, DONATION2, VIEW);
        } else {
            listDonations = donationRepository.findAllByArchivedIsNullOrderByCreatedAtDesc(pageable);
            auditApplicationService.audit("Consultation de la liste des donations globale", getUsernameFromJwt(), "Liste des Donations",
                    null, null, DONATION2, CONSULTATION);
        }

        List<DonationDTO> donationDTOList = new ArrayList<>();
        for (Donation donation : listDonations.getContent()) {
            DonationDTO donationDTO = mapDonationToDTO(donation);
            donationDTOList.add(donationDTO);
        }
        donationDTOList=donationDTOList.stream().peek(
                (donationDTO)->{
                 List<Taggable> taggables=taggableRepository.findByTaggableIdAndTaggableType(donationDTO.getId(),"donation");
                    List<TagDTO> tagDTOS=taggables.stream().map(taggable -> {
                        TagDTO tagDTO=new TagDTO();
                        tagDTO.setId(taggable.getTag().getId());
                        tagDTO.setName(taggable.getTag().getName());
                        tagDTO.setColor(taggable.getTag().getColor());
                        return tagDTO;
                    }).collect(Collectors.toList());
                    donationDTO.setTags(tagDTOS);
                }
        ).toList();

        log.debug("End service getAllDonations with {} donations found, took {}", listDonations.getTotalElements(), watch.toMS());
        return new PageImpl<>(donationDTOList, pageable, listDonations.getTotalElements());
    }

    public Page<DonationDTO> getAllDonationsByAssistantZone(Long assistantId, Integer page, Integer size, String searchByDonationType, String searchByDonorType, String searchByNom, String searchByPrenom, String lastNameAr, Double minAmount, Double maxAmount, Double canalDonationId, Date minDate, Date maxDate, Long searchByTagId) throws TechnicalException {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service getAllDonationsByAssistantZone assistantId {}, page {}, size {}, searchByDonationType {}, searchByDonorType {}, searchByNom {}, searchByPrenom {}, minAmount {}, maxAmount {}, minDate {}, maxDate {}", assistantId, page, size, searchByDonationType, searchByDonorType, searchByNom, searchByPrenom, minAmount, maxAmount, minDate, maxDate);

        // Get assistant and their zone
        ma.almobadara.backend.model.administration.Assistant assistant = assistantRepository.findById(assistantId)
                .orElse(null);
        if (assistant == null || assistant.getZone() == null) {
            log.warn("Assistant with ID {} not found or has no zone assigned", assistantId);
            return new PageImpl<>(Collections.emptyList());
        }

        // Get city IDs from the assistant's zone
        List<Long> zoneCityIds = assistant.getZone().getCityIdList();
        if (zoneCityIds.isEmpty()) {
            log.warn("Assistant's zone has no cities assigned");
            return new PageImpl<>(Collections.emptyList());
        }

        Pageable pageable = createPageable(page, size);

        // Build search parameters for audit
        Map<String, String> searchParams = new HashMap<>();
        if (searchByDonationType != null) searchParams.put("Type de donation", searchByDonationType);
        if (searchByDonorType != null) searchParams.put("Type de donateur", searchByDonorType);
        if (searchByNom != null) searchParams.put("Nom", searchByNom);
        if (searchByPrenom != null) searchParams.put("Prénom", searchByPrenom);
        if (lastNameAr != null) searchParams.put("Nom en arabe", lastNameAr);
        if (minAmount != null) searchParams.put("Montant minimum", String.valueOf(minAmount));
        if (maxAmount != null) searchParams.put("Montant maximum", String.valueOf(maxAmount));
        if (canalDonationId != null) searchParams.put("Canal de donation", String.valueOf(canalDonationId));
        if (minDate != null) searchParams.put("Date minimum", minDate.toString());
        if (maxDate != null) searchParams.put("Date maximum", maxDate.toString());
        if (searchByTagId != null) searchParams.put("Tag", String.valueOf(searchByTagId));
        searchParams.put("Zone de l'assistant", assistant.getZone().getName());

        String jsonSearchParams = null;
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            jsonSearchParams = objectMapper.writeValueAsString(searchParams);
        } catch (Exception e) {
            log.error("Error converting search params to JSON: {}", e.getMessage());
        }

        Page<Donation> listDonations;
        if (searchByDonationType != null || searchByDonorType != null || searchByNom != null || searchByPrenom != null || lastNameAr != null || minAmount != null || maxAmount != null || canalDonationId != null || minDate != null || maxDate != null || searchByTagId != null) {
            listDonations = filterDonationsByZone(zoneCityIds, searchByDonationType, searchByDonorType, searchByNom, searchByPrenom, lastNameAr, minAmount, maxAmount, canalDonationId, minDate, maxDate, searchByTagId, pageable);
            auditApplicationService.audit("Recherche par filtre dans liste des Donations par zone d'assistant", getUsernameFromJwt(), "Liste des Donations par zone",
                    jsonSearchParams, null, DONATION2, VIEW);
        } else {
            listDonations = filterDonationsByZone(zoneCityIds, null, null, null, null, null, null, null, null, null, null, null, pageable);
            auditApplicationService.audit("Consultation de la liste des Donations par zone d'assistant", getUsernameFromJwt(), "Liste des Donations par zone",
                    jsonSearchParams, null, DONATION2, VIEW);
        }

        List<DonationDTO> donationDTOList = listDonations.getContent().stream()
                .map(donation -> {
                    try {
                        return mapDonationToDTO(donation);
                    } catch (TechnicalException e) {
                        log.error("Error mapping donation to DTO: {}", e.getMessage());
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        // Add tags to donations
        donationDTOList = donationDTOList.stream().peek(
                (donationDTO) -> {
                    List<Taggable> taggables = taggableRepository.findByTaggableIdAndTaggableType(donationDTO.getId(), "donation");
                    List<TagDTO> tagDTOS = taggables.stream().map(taggable -> {
                        TagDTO tagDTO = new TagDTO();
                        tagDTO.setId(taggable.getTag().getId());
                        tagDTO.setName(taggable.getTag().getName());
                        tagDTO.setColor(taggable.getTag().getColor());
                        return tagDTO;
                    }).collect(Collectors.toList());
                    donationDTO.setTags(tagDTOS);
                }
        ).toList();

        log.debug("End service getAllDonationsByAssistantZone with {} donations found, took {}", listDonations.getTotalElements(), watch.toMS());
        return new PageImpl<>(donationDTOList, pageable, listDonations.getTotalElements());
    }

    public Page<MobileDonationDto> getAllDonationsByAssistantZoneForMobile(Long assistantId, Integer page, Integer size, String searchByDonationType, String searchByDonorType, String searchByNom, String searchByPrenom, String lastNameAr, Double minAmount, Double maxAmount, Double canalDonationId, Date minDate, Date maxDate, Long searchByTagId) throws TechnicalException {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service getAllDonationsByAssistantZoneForMobile assistantId {}, page {}, size {}, searchByDonationType {}, searchByDonorType {}, searchByNom {}, searchByPrenom {}, minAmount {}, maxAmount {}, minDate {}, maxDate {}", assistantId, page, size, searchByDonationType, searchByDonorType, searchByNom, searchByPrenom, minAmount, maxAmount, minDate, maxDate);

        // Get assistant and their zone
        ma.almobadara.backend.model.administration.Assistant assistant = assistantRepository.findById(assistantId)
                .orElse(null);
        if (assistant == null || assistant.getZone() == null) {
            log.warn("Assistant with ID {} not found or has no zone assigned", assistantId);
            return new PageImpl<>(Collections.emptyList());
        }

        // Get city IDs from the assistant's zone
        List<Long> zoneCityIds = assistant.getZone().getCityIdList();
        if (zoneCityIds.isEmpty()) {
            log.warn("Assistant's zone has no cities assigned");
            return new PageImpl<>(Collections.emptyList());
        }

        Pageable pageable = createPageable(page, size);

        // Build search parameters for audit
        Map<String, String> searchParams = new HashMap<>();
        if (searchByDonationType != null) searchParams.put("Type de donation", searchByDonationType);
        if (searchByDonorType != null) searchParams.put("Type de donateur", searchByDonorType);
        if (searchByNom != null) searchParams.put("Nom", searchByNom);
        if (searchByPrenom != null) searchParams.put("Prénom", searchByPrenom);
        if (lastNameAr != null) searchParams.put("Nom en arabe", lastNameAr);
        if (minAmount != null) searchParams.put("Montant minimum", String.valueOf(minAmount));
        if (maxAmount != null) searchParams.put("Montant maximum", String.valueOf(maxAmount));
        if (canalDonationId != null) searchParams.put("Canal de donation", String.valueOf(canalDonationId));
        if (minDate != null) searchParams.put("Date minimum", minDate.toString());
        if (maxDate != null) searchParams.put("Date maximum", maxDate.toString());
        if (searchByTagId != null) searchParams.put("Tag", String.valueOf(searchByTagId));
        searchParams.put("Zone de l'assistant", assistant.getZone().getName());

        String jsonSearchParams = null;
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            jsonSearchParams = objectMapper.writeValueAsString(searchParams);
        } catch (Exception e) {
            log.error("Error converting search params to JSON: {}", e.getMessage());
        }

        Page<Donation> listDonations;
        if (searchByDonationType != null || searchByDonorType != null || searchByNom != null || searchByPrenom != null || lastNameAr != null || minAmount != null || maxAmount != null || canalDonationId != null || minDate != null || maxDate != null || searchByTagId != null) {
            listDonations = filterDonationsByZone(zoneCityIds, searchByDonationType, searchByDonorType, searchByNom, searchByPrenom, lastNameAr, minAmount, maxAmount, canalDonationId, minDate, maxDate, searchByTagId, pageable);
            auditApplicationService.audit("Recherche par filtre dans liste des Donations par zone d'assistant (Mobile)", getUsernameFromJwt(), "Liste des Donations par zone (Mobile)",
                    jsonSearchParams, null, DONATION2, VIEW);
        } else {
            listDonations = filterDonationsByZone(zoneCityIds, null, null, null, null, null, null, null, null, null, null, null, pageable);
            auditApplicationService.audit("Consultation de la liste des Donations par zone d'assistant (Mobile)", getUsernameFromJwt(), "Liste des Donations par zone (Mobile)",
                    jsonSearchParams, null, DONATION2, VIEW);
        }

        List<MobileDonationDto> mobileDonationList = listDonations.getContent().stream()
                .map(donation -> {
                    try {
                        return mapDonationToMobileDto(donation);
                    } catch (Exception e) {
                        log.error("Error mapping donation to mobile DTO: {}", e.getMessage());
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        log.debug("End service getAllDonationsByAssistantZoneForMobile with {} donations found, took {}", listDonations.getTotalElements(), watch.toMS());
        return new PageImpl<>(mobileDonationList, pageable, listDonations.getTotalElements());
    }

    public Predicate buildPredicate(CriteriaBuilder criteriaBuilder, Root<Donation> root, Join<Donation, Donor> donorJoin,
                                     String searchByDonationType, String searchByDonorType, String searchByNom,
                                     String lastNameAr, Double minAmount, Double maxAmount, Double canalDonationId,
                                     Date minDate, Date maxDate,Long searchByTagId) {
        Predicate predicate = criteriaBuilder.conjunction();

        // Filter archived records
        predicate = criteriaBuilder.and(predicate, criteriaBuilder.isNull(root.get("archived")));

        // Filter by donation type
        if (searchByDonationType != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("type"), searchByDonationType));
        }
        if (searchByTagId != null) {
            // Create a subquery to find donors with the specified tag
            Subquery<Long> subquery = criteriaBuilder.createQuery().subquery(Long.class);
            Root<Taggable> taggableRoot = subquery.from(Taggable.class);
            subquery.select(taggableRoot.get("taggableId"))
                    .where(
                            criteriaBuilder.and(
                                    criteriaBuilder.equal(taggableRoot.get("taggableType"), "donation"),
                                    criteriaBuilder.equal(taggableRoot.get("tag").get("id"), searchByTagId)
                            )
                    );

            // Add the subquery condition to the main predicate
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.in(root.get("id")).value(subquery));
        }
        // Handle donor type filtering using treat()
        if (searchByDonorType != null) {
            Predicate donorTypePredicate = criteriaBuilder.or(
                    criteriaBuilder.equal(criteriaBuilder.treat(donorJoin, DonorPhysical.class).get("type"), searchByDonorType),
                    criteriaBuilder.equal(criteriaBuilder.treat(donorJoin, DonorAnonyme.class).get("type"), searchByDonorType),
                    criteriaBuilder.equal(criteriaBuilder.treat(donorJoin, DonorMoral.class).get("type"), searchByDonorType)
            );
            predicate = criteriaBuilder.and(predicate, donorTypePredicate);
        }

        // Filter by amount
        if (minAmount != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.greaterThanOrEqualTo(root.get("value"), minAmount));
        }

        if (maxAmount != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.lessThanOrEqualTo(root.get("value"), maxAmount));
        }

        // Filter by canalDonationId
        if (canalDonationId != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("canalDonationId"), canalDonationId));
        }

        // Filter by reception date range
        if (minDate != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.greaterThanOrEqualTo(root.get("receptionDate"), minDate));
        }

        if (maxDate != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.lessThanOrEqualTo(root.get("receptionDate"), maxDate));
        }

        // Filter by donor's Arabic last name
        if (lastNameAr != null && !lastNameAr.isEmpty()) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(
                    criteriaBuilder.lower(donorJoin.get("lastNameAr")),
                    "%" + lastNameAr.toLowerCase() + "%"
            ));
        }

        // Filter by donor's name or company
        if (searchByNom != null && !searchByNom.isEmpty()) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.or(
                    criteriaBuilder.like(criteriaBuilder.lower(donorJoin.get("lastName")), "%" + searchByNom.toLowerCase() + "%"),
                    criteriaBuilder.like(criteriaBuilder.lower(donorJoin.get("company")), "%" + searchByNom.toLowerCase() + "%"),
                    criteriaBuilder.like(criteriaBuilder.lower(donorJoin.get("name")), "%" + searchByNom.toLowerCase() + "%")
            ));
        }

        return predicate;
    }

    public Page<Donation> filterDonations(String searchByDonationType, String searchByDonorType, String searchByNom,
                                          String searchByPrenom, String lastNameAr, Double minAmount, Double maxAmount,
                                          Double canalDonationId, Date minDate, Date maxDate,Long searchByTagId, Pageable pageable) {
        log.debug("Start service filterDonations with searchByDonationType {}, searchByDonorType {}, searchByNom {}, searchByPrenom {}, minAmount {}, maxAmount {}, minDate {}, maxDate {}",
                searchByDonationType, searchByDonorType, searchByNom, searchByPrenom, minAmount, maxAmount, minDate, maxDate);
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Donation> criteriaQuery = criteriaBuilder.createQuery(Donation.class);
        Root<Donation> root = criteriaQuery.from(Donation.class);

        Join<Donation, Donor> donorJoin = root.join("donor", JoinType.LEFT);
        Predicate predicate = buildPredicate(criteriaBuilder, root, donorJoin, searchByDonationType, searchByDonorType, searchByNom, lastNameAr, minAmount, maxAmount, canalDonationId, minDate, maxDate,searchByTagId);
        criteriaQuery.where(predicate);

        TypedQuery<Donation> typedQuery = entityManager.createQuery(criteriaQuery);
        long totalCount = typedQuery.getResultList().size();
        typedQuery.setFirstResult((int) pageable.getOffset());
        typedQuery.setMaxResults(pageable.getPageSize());
        List<Donation> resultList = typedQuery.getResultList();

        log.debug("End service filterDonations with {} donations found", totalCount);
        return new PageImpl<>(resultList, pageable, totalCount);
    }

    public Page<Donation> filterDonationsByZone(List<Long> zoneCityIds, String searchByDonationType, String searchByDonorType, String searchByNom,
                                                String searchByPrenom, String lastNameAr, Double minAmount, Double maxAmount,
                                                Double canalDonationId, Date minDate, Date maxDate, Long searchByTagId, Pageable pageable) {
        log.debug("Start service filterDonationsByZone with zoneCityIds {}, searchByDonationType {}, searchByDonorType {}, searchByNom {}, searchByPrenom {}, minAmount {}, maxAmount {}, minDate {}, maxDate {}",
                zoneCityIds, searchByDonationType, searchByDonorType, searchByNom, searchByPrenom, minAmount, maxAmount, minDate, maxDate);

        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Donation> criteriaQuery = criteriaBuilder.createQuery(Donation.class);
        Root<Donation> root = criteriaQuery.from(Donation.class);

        Join<Donation, Donor> donorJoin = root.join("donor", JoinType.LEFT);

        // Build the base predicate with existing filters
        Predicate predicate = buildPredicate(criteriaBuilder, root, donorJoin, searchByDonationType, searchByDonorType, searchByNom, lastNameAr, minAmount, maxAmount, canalDonationId, minDate, maxDate, searchByTagId);

        // Add zone filter: donations where donor's cityId is in the zone's city list
        if (zoneCityIds != null && !zoneCityIds.isEmpty()) {
            Predicate zonePredicate = donorJoin.get("cityId").in(zoneCityIds);
            predicate = criteriaBuilder.and(predicate, zonePredicate);
        }

        criteriaQuery.where(predicate);
        criteriaQuery.orderBy(criteriaBuilder.desc(root.get("createdAt")));

        TypedQuery<Donation> typedQuery = entityManager.createQuery(criteriaQuery);
        long totalCount = typedQuery.getResultList().size();
        typedQuery.setFirstResult((int) pageable.getOffset());
        typedQuery.setMaxResults(pageable.getPageSize());
        List<Donation> resultList = typedQuery.getResultList();

        log.debug("End service filterDonationsByZone with {} donations found", totalCount);
        return new PageImpl<>(resultList, pageable, totalCount);
    }

    public DonationDTO mapDonationToDTO(Donation donation) throws TechnicalException {
        DonationDTO donationDTO = donationMapper.donationToDonationDTOForList(donation);
        if (donation.getDonor() instanceof DonorPhysical donorPhysical) {
            donationDTO.setDonor(donorPhysicalMapper.donorPhysicalModelToDtoForList(donorPhysical));
        } else if (donation.getDonor() instanceof DonorMoral donorMoral) {
            DonorMoralDTO donorMoralDTO = donorMoralMapper.donorMoralModelToDtoForList(donorMoral);
            if (!StringUtils.isEmpty(donorMoralDTO.getLogoUrl())) {
                String base64 = Arrays.toString(minioService.ReadFromMinIO(donorMoralDTO.getLogoUrl(), null));
                donorMoralDTO.setLogo64(base64);
            }
            if (donorMoralDTO.getActivitySector() != null && donorMoralDTO.getActivitySector().getId() != null) {
                ActivitySectorDTO activitySectorDTO = refFeignClient.getMetActivitySector(donorMoralDTO.getActivitySector().getId());
                donorMoralDTO.setActivitySector(activitySectorDTO);
            }
            donationDTO.setDonor(donorMoralDTO);
        } else if (donation.getDonor() instanceof DonorAnonyme donorAnonyme) {
            donationDTO.setDonor(donorAnonymeMapper.donorAnonymeModelToDtoForList(donorAnonyme));

        }

        if (donationDTO.getDonor() != null && donationDTO.getDonor().getCity() != null && donationDTO.getDonor().getCity().getId() != null) {
            CityDTO cityDTO = refFeignClient.getParCity(donationDTO.getDonor().getCity().getId());
            donationDTO.getDonor().setCity(cityDTO);
        }

        if (donationDTO.getCanalDonation() != null && donationDTO.getCanalDonation().getId() != null) {
            CanalDonationDTO canalDonationDTO = refFeignClient.getMetCanalDonation(donationDTO.getCanalDonation().getId());
            donationDTO.setCanalDonation(canalDonationDTO);
        }
        if (donation.getArchived() != null) {
            donationDTO.setArchived(donation.getArchived());
        }

        return donationDTO;
    }

    @Transactional
    public void deleteDonation(Long idDonation) throws TechnicalException {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service deleteDonation By Id {}", idDonation);

        if (idDonation == null) {
            throw new TechnicalException(messages.get(DONATION_NOT_FOUND));
        }

        Optional<Donation> donationOptional = donationRepository.findById(idDonation);
        if (donationOptional.isPresent()) {
            Donation donation = donationOptional.get();
            Donation tempDonation = donation;
            // Check if any budget line has executed = true
            boolean hasExecutedBudgetLine = donation.getBudgetLines().stream()
                    .anyMatch(budgetLine ->
                            budgetLine.getStatus().name().equals("EXECUTED") ||
                                    budgetLine.getStatus().name().equals("RESERVED") ||
                                    budgetLine.getStatus().name().equals("REMAINING")
                    );

            if (hasExecutedBudgetLine) {
                throw new TechnicalException("Cannot delete donation with executed budget lines.");
            }
            DonationDTO donationDTO = donationMapper.donationToDonationDTO(donation);
            String canalDonation = getCanalDonationName(donationDTO);
            String currencyName = getCurrencyName(donationDTO);
            // Generate audit string using toAuditString
            String donationAuditString = tempDonation.toAuditString(currencyName, canalDonation,getDonorName(donation));
            auditApplicationService.audit("Suppression de la Donation : " + donation.getCode(), getUsernameFromJwt(),
                    "Delete Donation", donationAuditString, null, DONATION2, DELETE);
            // Delete each budget line and clear from donation
            for (BudgetLine budgetLine : donation.getBudgetLines()) {
                log.debug("Deleting budget line: {}", budgetLine.getId());
                budgetLineRepository.delete(budgetLine);
            }

            // Clear the list to detach budget lines from the donation

            donation.getBudgetLines().clear();
            // Soft delete by marking as archived
            donation.setArchived(true);
            donationRepository.save(donation);  // Now safe to save without error



            // Perform audit logging


        }

        log.debug("End service deleteDonation By Id {}, took {}", idDonation, watch.toMS());
    }


    public DonationDTO getDonationById(Long id) throws TechnicalException {

        TimeWatch watch = TimeWatch.start();
        log.debug("Start service getDonationById: {}", id);

        if (id == null) {
            throw new TechnicalException(messages.get(DONATION_NOT_FOUND));
        }
        Optional<Donation> optionalDonation = donationRepository.findById(id);
        if (optionalDonation.isEmpty()) {
            throw new TechnicalException(messages.get(DONATION_NOT_FOUND));
        }
        String donorName = "";
        Donation donation = optionalDonation.get();
        DonorDTO donorDTO = null;
        DonationDTO donationDTO = donationMapper.donationToDonationDTO(donation);
        if (donation.getBudgetLines() != null && !donation.getBudgetLines().isEmpty()) {
            processBudgetLines(donation, donationDTO);

        }
        Donor oldDonor = donation.getDonor();
        if (oldDonor instanceof DonorPhysical donorPhysical) {
            donorDTO = donorPhysicalMapper.donorPhysicalModelToOneDTO(donorPhysical);
            donorName = donorPhysical.getFirstName() + " " + donorPhysical.getLastName();
            fetchPhysicalDonorDetails((DonorPhysicalDTO) donorDTO);

        } else if (oldDonor instanceof DonorMoral donorMoral) {
            donorDTO = donorMoralMapper.donorMoralModelToOneDTO(donorMoral);
            donorName = donorMoral.getCompany();
            fetchMoralDonorDetails((DonorMoralDTO) donorDTO);
        } else if (oldDonor instanceof DonorAnonyme donorAnonyme) {
            donorDTO = donorAnonymeMapper.toDto(donorAnonyme);
            donorName = donorAnonyme.getName();
        }
        if (donorDTO != null) {
            fetchCityDetails(donorDTO);
            donationDTO.setDonor(donorDTO);
        }
        fetchCurrencyDetails(donationDTO);
        fetchCanalDonationDetails(donationDTO);
        fetchDonationProductNatureDetails(donationDTO);


        //Audit
        Donation donationAudit = donationMapper.donationDTOToDonation(donationDTO);
        String canalDonation = null;
        String currencyName = null;
        if (donationDTO.getCanalDonation().getId() != null) {
            CanalDonationDTO canalDonationDTO = refFeignClient.getMetCanalDonation(donationDTO.getCanalDonation().getId());
            canalDonation = canalDonationDTO.getName();
        }

        if (donationDTO.getCurrency().getId() != null) {
            CurrencyDTO currencyDTO = refFeignClient.getParCurrency(donationDTO.getCurrency().getId());
            currencyName = currencyDTO.getName();
        }
        donationAudit.setBudgetLines(donation.getBudgetLines());

        String donationAuditString = donationAudit.toAuditString(currencyName, canalDonation,donorName);
        log.info("donation string : " + donationAuditString);
        auditApplicationService.audit("Consultation de la Donation : " + donationDTO.getCode(), getUsernameFromJwt(), "Add New Donation",
                donationAuditString, null, DONATION2, CONSULTATION);

        List<Taggable> taggables=taggableRepository.findByTaggableIdAndTaggableType(donation.getId(),"donation");
        List<TagDTO> tags = new ArrayList<>();
        for (Taggable taggable : taggables) {
            TagDTO tagDTO = new TagDTO();
            tagDTO.setColor(taggable.getTag().getColor());
            tagDTO.setId(taggable.getTag().getId());
            tagDTO.setName(taggable.getTag().getName());
            tags.add(tagDTO);
        }
        donationDTO.setTags(tags);
        log.debug("End service getDonationById : {}, took {}", id, watch.toMS());
        return donationDTO;
    }

    public void processBudgetLines(Donation donation, DonationDTO donationDTO) {
        List<BudgetLineDTO> budgetLineDTOS = donation.getBudgetLines().stream()
                .map(budgetLine -> {
                    // Map BudgetLine to BudgetLineDTO
                    BudgetLineDTO budgetLineDTO = budgetLineMapper.budgetLineToBudgetLineDTO(budgetLine);
                    if(budgetLine.getServiceCollectEps()!=null){
                        budgetLineDTO.setServiceCollectEpsId(budgetLine.getServiceCollectEps().getId());
                    }
                    // Extract and set product categories directly
                    List<Long> productCategories = budgetLine.getProductsCategory(); // Assuming this method exists in BudgetLine
                    if (productCategories != null && !productCategories.isEmpty()) {
                        budgetLineDTO.setProductCategory(productCategories); // Assuming setProductCategory can take a List<Long>
                    }

                    return budgetLineDTO;
                })
                .collect(Collectors.toList());

        for (BudgetLineDTO budgetLineDTO : new ArrayList<>(budgetLineDTOS)) { // Using a copy of the list to avoid ConcurrentModificationException
            CurrencyDTO currencyDTO;
            if (budgetLineDTO.getCurrency() != null && budgetLineDTO.getCurrency().getId() != null) {
                currencyDTO = refFeignClient.getParCurrency(budgetLineDTO.getCurrency().getId());
                budgetLineDTO.setCurrency(currencyDTO);
            }
            if(budgetLineDTO.getProductCategory() !=null && !budgetLineDTO.getProductCategory().isEmpty()){
                List<TypeProductNatureDTO> productCategoryDTOS = new ArrayList<>();
                for (Long id : budgetLineDTO.getProductCategory()) {
                    TypeProductNatureDTO productCategoryDTO = refFeignClient.getConsTypeProductNature(id);
                    productCategoryDTOS.add(productCategoryDTO);
                }
                budgetLineDTO.setTypeProductNatures(productCategoryDTOS);
            }

            if (budgetLineDTO.getExecutionDate() != null) {
                // Convert LocalDateTime to Date
                LocalDateTime executionDate = budgetLineDTO.getExecutionDate();
                Date executionDateAsDate = Date.from(executionDate.atZone(ZoneId.systemDefault()).toInstant());

                // Set executionDateBudgetLine to the converted Date
                budgetLineDTO.setExecutionDateBudgetLine(executionDateAsDate);

                // Nullify the LocalDateTime executionDate
                budgetLineDTO.setExecutionDate(null);
            }

            if (budgetLineDTO.getType().equals("Non Identifié")) {
                donationDTO.setNonIdentifiedValue(budgetLineDTO.getAmount());
                donationDTO.setNonIdentifiedCurrency(budgetLineDTO.getCurrency());
                donationDTO.setNonIdentifiedComment(budgetLineDTO.getComment());
                donationDTO.setNonIdentifiedValueCurrency(budgetLineDTO.getValueCurrency());
                donationDTO.setIdNonIdentifed(budgetLineDTO.getId());
                donationDTO.setNonIdentifiedStatus(budgetLineDTO.getStatus());
                budgetLineDTOS.remove(budgetLineDTO);
            }
        }

        donationDTO.setBudgetLines(budgetLineDTOS);
    }

    public void fetchPhysicalDonorDetails(DonorPhysicalDTO donorDTO) {
        // Check if the picture URL is not null
        if (donorDTO.getPictureUrl() != null) {
            try {
                // Retrieve the image from MinIO
                byte[] imageData = minioService.ReadFromMinIO(donorDTO.getPictureUrl(), null);
                String base64Image = Base64.getEncoder().encodeToString(imageData);
                // Set the base64 string to the DonorPhysicalDTO object
                donorDTO.setPicture64(base64Image);
            } catch (TechnicalException ex) {
                ex.printStackTrace();
            }
        }
        TypeIdentityDTO typeIdentityDTO = donorDTO.getTypeIdentity();
        if (typeIdentityDTO != null && typeIdentityDTO.getId() != null) {
            TypeIdentityDTO fullTypeIdentityDTO = refFeignClient.getParTypeIdentity(typeIdentityDTO.getId());
            donorDTO.setTypeIdentity(fullTypeIdentityDTO);
        }
    }

    public void fetchMoralDonorDetails(DonorMoralDTO donorDTO) {
        if (donorDTO.getLogoUrl() != null) {

            try {
                // Retrieve the image from MinIO
                byte[] imageData = minioService.ReadFromMinIO(donorDTO.getLogoUrl(), null);
                String base64Image = Base64.getEncoder().encodeToString(imageData);
                // Set the base64 string to the DonorPhysicalDTO object
                donorDTO.setLogo64(base64Image);
            } catch (TechnicalException ex) {
                ex.printStackTrace();
            }
        }

        if (donorDTO.getActivitySector() != null && donorDTO.getActivitySector().getId() != null) {
            ActivitySectorDTO activitySectorDTO = refFeignClient.getMetActivitySector(donorDTO.getActivitySector().getId());
            donorDTO.setActivitySector(activitySectorDTO);
        }
    }

    public void fetchCityDetails(DonorDTO donorDTO) throws TechnicalException {
        CityDTO cityDTO = donorDTO.getCity();
        if (cityDTO != null && cityDTO.getId() != null) {
            if (cityDTO.getId() == null) {
                throw new TechnicalException(messages.get(CITY_NOT_FOUND));
            }
            CityDTO fullCityDTO = refFeignClient.getParCity(cityDTO.getId());
            RegionDTO regionDTO = refFeignClient.getParRegion(fullCityDTO.getRegion().getId());
            fullCityDTO.setRegion(regionDTO);
            donorDTO.setCity(fullCityDTO);

            CityWithRegionAndCountryDTO fullCountryDto = refController.getCityWithRegionAndCountry(cityDTO.getId()).getBody();
            donorDTO.setInfo(fullCountryDto);
        }
    }

    public void fetchCurrencyDetails(DonationDTO donationDTO) {
        CurrencyDTO currencyDTO = donationDTO.getCurrency();
        if (currencyDTO != null && currencyDTO.getId() != null) {
            CurrencyDTO fullCurrency = refFeignClient.getParCurrency(currencyDTO.getId());
            donationDTO.setCurrency(fullCurrency);
            donationDTO.setEnableCurrency(true);
        }
    }

    public void fetchCanalDonationDetails(DonationDTO donationDTO) {
        CanalDonationDTO canalDonationDTO = donationDTO.getCanalDonation();
        if (canalDonationDTO != null && canalDonationDTO.getId() != null) {
            CanalDonationDTO fullCanalDonationDTO = refFeignClient.getMetCanalDonation(canalDonationDTO.getId());
            donationDTO.setCanalDonation(fullCanalDonationDTO);
        }
    }

    public void fetchDonationProductNatureDetails(DonationDTO donationDTO) {
        if (donationDTO.getDonationProductNatures() != null) {
            List<DonationProductNatureDTO> donationProductNatureDTOS = donationDTO.getDonationProductNatures().stream()
                    .peek(e -> {
                        ProductNatureDTO productNatureDTO = e.getProductNature();
                        if (productNatureDTO != null && productNatureDTO.getId() != null) {
                            ProductNatureDTO fullDonationProductNatureDTO = refFeignClient.getMetProductNature(productNatureDTO.getId());
                            e.setProductNature(fullDonationProductNatureDTO);
                        }
                        assert e.getProductNature() != null;
                        TypeProductNatureDTO typeProductNatureDTO = e.getProductNature().getTypeProductNature();
                        if (typeProductNatureDTO != null && typeProductNatureDTO.getId() != null) {
                            TypeProductNatureDTO fullTypeProductNatureDTO = refFeignClient.getConsTypeProductNature(typeProductNatureDTO.getId());
                            e.getProductNature().setTypeProductNature(fullTypeProductNatureDTO);
                        }
                        ProductUnitDTO productUnitDTO = e.getProductUnit();
                        if (productUnitDTO != null && productUnitDTO.getId() != null) {
                            ProductUnitDTO fullProductUnitDTO = refFeignClient.getParProductUnit(productUnitDTO.getId());
                            e.setProductUnit(fullProductUnitDTO);
                        }
                    }).collect(Collectors.toList());
            donationDTO.setDonationProductNatures(donationProductNatureDTOS);
        }
    }


    public void setDonorDetails(Donation donation, DonationExportDTO donationExportDTO) {
        if (donation.getDonor() instanceof DonorPhysical) {
            donationExportDTO.setDonorName(exportEntitiesMapper.donationPhysicalDonorToDonationExportDTO((DonorPhysical) donation.getDonor()).getDonorName());
            donationExportDTO.setTypeDonor(TYPE_DONOR_PHYSIQUE);
        } else if (donation.getDonor() instanceof DonorMoral) {
            donationExportDTO.setDonorName(exportEntitiesMapper.donationMoralDonorToDonationExportDTO((DonorMoral) donation.getDonor()).getDonorName());
            donationExportDTO.setTypeDonor(TYPE_DONOR_MORAL);
        }
    }

    public void setCanalDonationDetails(DonationExportDTO donationExportDTO) {
        if (donationExportDTO.getCanalDonationId() != null) {
            try {
                CanalDonationDTO canalDonationDTO = refFeignClient.getMetCanalDonation(donationExportDTO.getCanalDonationId());
                donationExportDTO.setCanalDonation(canalDonationDTO.getName());
            } catch (Exception e) {
                log.error("Error while retrieving canal donation details: {}", e.getMessage());
            }
        }
    }

    public List<Donation> filterDonationsForExport(String searchByDonationType, String searchByDonorType, String searchByNom,
                                                   String searchByPrenom, String lastNameAr, Double minAmount, Double maxAmount,
                                                   Double canalDonationId, Date minDate, Date maxDate) {
        log.debug("Start service filterDonationsForExport with searchByDonationType {}, searchByDonorType {}, searchByNom {}, searchByPrenom {}, minAmount {}, maxAmount {}, minDate {}, maxDate {}",
                searchByDonationType, searchByDonorType, searchByNom, searchByPrenom, minAmount, maxAmount, minDate, maxDate);
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Donation> criteriaQuery = criteriaBuilder.createQuery(Donation.class);
        Root<Donation> root = criteriaQuery.from(Donation.class);

        Join<Donation, Donor> donorJoin = root.join("donor", JoinType.LEFT);
        Predicate predicate = buildPredicate(criteriaBuilder, root, donorJoin, searchByDonationType, searchByDonorType, searchByNom, lastNameAr, minAmount, maxAmount, canalDonationId, minDate, maxDate,null);
        criteriaQuery.where(predicate);
        criteriaQuery.orderBy(criteriaBuilder.desc(root.get("createdAt")));
        TypedQuery<Donation> typedQuery = entityManager.createQuery(criteriaQuery);
        List<Donation> resultList = typedQuery.getResultList();

        log.debug("End service filterDonationsForExport with {} donations found", resultList.size());
        return resultList;
    }

    public List<DonationExportDTO> getAllDonationsToExport(List<Donation> listDonations) {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service getAllDonationsToExport");
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
        List<DonationExportDTO> donationExportDTOList = new ArrayList<>();
        for (Donation donation : listDonations) {
            DonationExportDTO donationExportDTO = exportEntitiesMapper.donationToDonationExportDTO(donation);
            setDonorDetails(donation, donationExportDTO);
            setCanalDonationDetails(donationExportDTO);

            if (donation.getReceptionDate() != null) {
                Date receptionDate = donation.getReceptionDate();
                LocalDate localDate = receptionDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                donationExportDTO.setReceptionDate(java.sql.Date.valueOf(localDate));
            }
            donationExportDTOList.add(donationExportDTO);
        }

        log.debug("End service getAllDonationsToExport with {} donations found, took {}", donationExportDTOList.size(), watch.toMS());
        return donationExportDTOList;
    }


    public ExportFileDTO exportFileWithName(String searchByDonationType, String searchByDonorType, String searchByNom, String searchByPrenom, String lastNameAr, Double minAmount, Double maxAmount, Double canalDonationId, Date minDate, Date maxDate) {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service exportDonationFile searchByDonationType {}, searchByDonorType {}, searchByNom {}, searchByPrenom {}, minAmount {}, maxAmount {}, minDate {}, maxDate {}", searchByDonationType, searchByDonorType, searchByNom, searchByPrenom, minAmount, maxAmount, minDate, maxDate);

        // Filter Donation entities based on criteria
        List<Donation> listDonations = filterDonationsForExport(searchByDonationType, searchByDonorType, searchByNom, searchByPrenom, lastNameAr, minAmount, maxAmount, canalDonationId, minDate, maxDate);

        // Convert entities to DTOs
        List<DonationExportDTO> donationExportDTOList = getAllDonationsToExport(listDonations);

        // Define export parameters
        String sheetName = "Rappport des Donations";
        String[] headers = Arrays.stream(DonationExportHeaders.values())
                .map(DonationExportHeaders::getHeaderName)
                .toArray(String[]::new);

        auditApplicationService.audit("Export donation informations", getUsernameFromJwt(), "Export donations", null, null, DONATION2, EXPORT);
        // Perform export
        return exportService.exportEntities(sheetName, headers, donationExportDTOList, this::mapToExportRow);
    }

    public Object[] mapToExportRow(DonationExportDTO dto) {
        return new Object[]{
                dto.getCode(),
                dto.getCreatedAt().format(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss")),
                dto.getValue(),
                dto.getTypeDonation(),
                dto.getIdentifiedDonor(),
                dto.getDonorName(),
                dto.getTypeDonor(),
                dto.getCanalDonation(),
                dto.getFormattedReceptionDate(),
                dto.getComment()
        };
    }

    public List<DonationHistoryDTO> getDonationHistory(Long donationId) throws TechnicalException {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service getDonationHistory with donationId {}", donationId);

        if (donationId == null) {
            throw new TechnicalException(messages.get(DONATION_NOT_FOUND));
        }

        Optional<Donation> donationOptional = donationRepository.findById(donationId);
        if (donationOptional.isEmpty()) {
            throw new TechnicalException(messages.get(DONATION_NOT_FOUND));
        }
        List<DonationHistory> donationHistories = donationHistoryRepository.findByDonationId(donationId);
        // we us ethis "    DonationHistoryDTO  donationHistoryToDonationHistoryDTO(DonationHistory donationHistory);"
        List<DonationHistoryDTO> donationHistoryDTOS = donationHistories.stream()
                .map(donationHistoryMapper::donationHistoryToDonationHistoryDTO)
                .collect(Collectors.toList());

        log.debug("End service getDonationHistory with {} donation histories found, took {}", donationHistoryDTOS.size(), watch.toMS());
        return donationHistoryDTOS;

    }

    public String getCurrencyName(DonationDTO donation) {
        if (donation.getCurrency() != null && donation.getCurrency().getId() != null) {
            CurrencyDTO currencyDTO = refFeignClient.getParCurrency(donation.getCurrency().getId());
            return currencyDTO.getName();
        }
        return null;
    }

    public String getCanalDonationName(DonationDTO donation) {
        if (donation.getCanalDonation() != null && donation.getCanalDonation().getId() != null) {
            CanalDonationDTO canalDonationDTO = refFeignClient.getMetCanalDonation(donation.getCanalDonation().getId());
            return canalDonationDTO.getName();
        }
        return null;
    }

    public String getDonorName(Donation donation) {
        if (donation.getDonor() instanceof DonorPhysical donorPhysical) {
            return donorPhysical.getFirstName() + " " + donorPhysical.getLastName();
        } else if (donation.getDonor() instanceof DonorMoral donorMoral) {
            return donorMoral.getCompany();
        } else if (donation.getDonor() instanceof DonorAnonyme donorAnonyme) {
            return donorAnonyme.getName();
        } else return "test";
    }

    private Pageable createPageable(Integer page, Integer size) {
        log.debug("Page number {}", page);
        Sort.Direction sortDirection = Sort.Direction.DESC;
        String sortBy = "createdAt";
        Sort sort = Sort.by(sortDirection, sortBy);
        return PageRequest.of(page, size, sort);
    }

    /**
     * Map Donation entity to MobileDonationDto
     */
    private MobileDonationDto mapDonationToMobileDto(Donation donation) throws TechnicalException {
        // For donations, the direction is always "Entrée" (incoming money)
        String direction = "Entrée";

        // Get channel name
        String channel = null;
        if (donation.getCanalDonationId() != null) {
            try {
                CanalDonationDTO canalDonationDTO = refFeignClient.getMetCanalDonation(donation.getCanalDonationId());
                channel = canalDonationDTO.getName();
            } catch (Exception e) {
                log.warn("Could not fetch canal donation for ID: {}", donation.getCanalDonationId());
            }
        }

        // Get service name from budget lines
        String service = "Non Identifié";
        if (donation.getBudgetLines() != null && !donation.getBudgetLines().isEmpty()) {
            // Get the first budget line's service
            BudgetLine firstBudgetLine = donation.getBudgetLines().get(0);
            if (firstBudgetLine.getService() != null) {
                service = firstBudgetLine.getService().getName();
            }
        }

        return MobileDonationDto.builder()
                .id(donation.getId())
                .timeCreated(donation.getCreatedAt())
                .montant(donation.getValue())
                .direction(direction)
                .service(service)
                .type(donation.getType())
                .channel(channel)
                .build();
    }

}
