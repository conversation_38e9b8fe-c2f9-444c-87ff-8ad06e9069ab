package ma.almobadara.backend.dto.communs;

import jakarta.validation.constraints.NotNull;
import lombok.*;
import static ma.almobadara.backend.util.constants.GlobalConstants.*;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class NoteAndEntityDto {

    @NotNull(message = ACTION_NULL)
    private NoteDTO noteDto;

    @NotNull(message = ENTITY_ID_NOT_FOUND)
    private Long entityId;


    @NotNull(message = ENTITY_TYPE_NOT_FOUND)
    private String entityType;

}
