CREATE TABLE tutor_history (
                               id BIGINT GE<PERSON>RATED BY DEFAULT AS IDENTITY PRIMARY KEY,
                               created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                               updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                               family_member_id BIGINT NOT NULL,
                               date_debut TIMESTAMP WITHOUT TIME ZONE,
                               date_fin TIMESTAMP WITHOUT TIME ZONE,

                               CONSTRAINT fk_family_member
                                   FOREIGN KEY (family_member_id) REFERENCES family_member (id) ON DELETE CASCADE
);
