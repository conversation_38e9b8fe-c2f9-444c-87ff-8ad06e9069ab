package ma.almobadara.backend.dto.donor;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class DeleteDonorAuditDto {

    private Long id;
    private String code;
    private String Identite;
    private String adresse;
    private String adresseArabe;
    private String anneePremiereDonation;
}
