package ma.almobadara.backend.repository.aideComplemenatire;
import java.util.List;
import java.util.Optional;

import ma.almobadara.backend.model.aideComplemenatire.AideComplementaireBeneficiaryAdHocGroupe;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface AideComplementaireBeneficiaryAdHocGroupeRepository extends JpaRepository<AideComplementaireBeneficiaryAdHocGroupe, Long> {

    List<AideComplementaireBeneficiaryAdHocGroupe> findByAideComplementaire_Id(Long aideId);

    List<AideComplementaireBeneficiaryAdHocGroupe> findByAideComplementaire_IdAndBeneficiaryIsNull(Long aideId);

    Optional<AideComplementaireBeneficiaryAdHocGroupe> findByAideComplementaire_IdAndBeneficiaryAdHocGroupIdAndBeneficiaryIsNull(Long aideId, Long groupeId);

    List<AideComplementaireBeneficiaryAdHocGroupe> findByAideComplementaire_IdAndBeneficiaryAdHocGroupId(Long aideId, Long groupeId);

    Optional<AideComplementaireBeneficiaryAdHocGroupe> findByAideComplementaire_IdAndBeneficiaryAdHocGroupIdAndBeneficiaryId(Long aideId, Long groupeId, Long beneficiaryId);
}
