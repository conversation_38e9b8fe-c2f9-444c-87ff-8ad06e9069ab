package ma.almobadara.backend.controller.error;

import jakarta.validation.ValidationException;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.config.Messages;
import ma.almobadara.backend.exceptions.FunctionalException;
import ma.almobadara.backend.exceptions.TechnicalException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.hateoas.mediatype.problem.Problem;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

@ControllerAdvice
@Slf4j
public class CustomizedResponseEntityExceptionHandler {

    @Autowired
    private Messages messages;

    @ExceptionHandler(ValidationException.class)
    @ResponseStatus(code = HttpStatus.BAD_REQUEST)
    @ResponseBody
    public Problem onValidationException(ValidationException e) {
        return buildProblem(e);
    }

    @ExceptionHandler(TechnicalException.class)
    @ResponseStatus(code = HttpStatus.INTERNAL_SERVER_ERROR)
    @ResponseBody
    public Problem onTechnicalException(TechnicalException e) {
        return buildProblem(e);
    }

    @ExceptionHandler(FunctionalException.class)
    @ResponseStatus(code = HttpStatus.BAD_REQUEST)
    @ResponseBody
    public Problem onFunctionalException(FunctionalException e) {
        return buildProblem(e);
    }

    private Problem buildProblem(Exception e) {
        log.error("Building problem -> {}", e);
        return Problem.create() //
                .withTitle(e.getClass().getSimpleName())
                .withDetail(e.getMessage());
    }

    private Problem buildProblem(ValidationException e) {
        log.error("Building problem -> {}", e);
        return Problem.create() //
                .withTitle(e.getClass().getSimpleName())
                .withDetail(messages.get(e.getMessage()));
    }

}
