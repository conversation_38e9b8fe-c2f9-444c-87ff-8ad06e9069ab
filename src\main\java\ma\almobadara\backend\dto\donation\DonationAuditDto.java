package ma.almobadara.backend.dto.donation;

import lombok.*;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class DonationAuditDto {
    private String Code;

    private Double ValeurDeLaDonation;

    private Date DateReception;

    private boolean DonateurIdentifie;

    private String Commentaire;

    private String TypeDonation;

    private Double Equivalent;

    private String CanalDonation;

    private String CodeDonateur;

    private Long NombredesProduitsEnNature;

    private String Devise ;

}
