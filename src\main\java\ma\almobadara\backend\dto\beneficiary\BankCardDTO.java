package ma.almobadara.backend.dto.beneficiary;

import lombok.*;
import ma.almobadara.backend.dto.referentiel.CardTypeDTO;

import java.io.Serializable;
import java.time.Instant;
import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class BankCardDTO implements Serializable {

	private Long id;

	private String cardNumber;

	private String accountNumber;

	private Date deliveryDate;

	private Date expiryDate;

	private String status;

	private Instant createdAt;

	private CardTypeDTO cardType;

	private PersonDTO person;

}
