package ma.almobadara.backend.repository.beneficiary;

import ma.almobadara.backend.model.beneficiary.ActionBeneficiary;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ActionBeneficiaryRepository extends JpaRepository<ActionBeneficiary, Long> {

    List<ActionBeneficiary> findByBeneficiaryId(Long donorId);

    List<ActionBeneficiary> findByActionId(Long id);


}
