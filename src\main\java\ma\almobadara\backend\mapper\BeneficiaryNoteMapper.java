package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.beneficiary.NoteBeneficiaryDTO;
import ma.almobadara.backend.model.beneficiary.NoteBeneficiary;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface BeneficiaryNoteMapper {

	NoteBeneficiaryDTO beneficiaryNoteToBeneficiaryNoteDTO(NoteBeneficiary beneficiaryNote);

	Iterable<NoteBeneficiaryDTO> beneficiaryNoteToBeneficiaryNoteDTO(Iterable<NoteBeneficiary> beneficiaryNotes);

	NoteBeneficiary beneficiaryNoteDTOToBeneficiaryNote(NoteBeneficiaryDTO beneficiaryNoteDTO);

	Iterable<NoteBeneficiary> beneficiaryNoteDTOToBeneficiaryNote(Iterable<NoteBeneficiaryDTO> beneficiaryNoteDTOS);

}
