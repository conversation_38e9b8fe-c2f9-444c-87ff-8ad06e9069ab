ALTER TABLE role
    ADD COLUMN description VARCHAR(255);

UPDATE cache_ad_user SET role_id = NULL;

DELETE FROM role;

INSERT INTO role (id, name, description) VALUES
                                             (1, 'ROLE_MASTER_ADMIN', 'Master Admin'),
                                             (2, 'ROLE_ASSISTANT', 'Assistant'),
                                             (3, 'ROLE_SERVICE_KAFALAT', 'Service Kafalat'),
                                             (4, 'ROLE_SERVICE_MARKETING', 'Service Marketing');


DELETE FROM role_permissions;


INSERT INTO role_permissions (role_id, permission_id)
SELECT 1, id FROM permission;

INSERT INTO role_permissions (role_id, permission_id)
VALUES
    (2, 3),
    (2, 4);

INSERT INTO role_permissions (role_id, permission_id)
SELECT 3, id FROM permission;

INSERT INTO role_permissions (role_id, permission_id)
SELECT 4, id FROM permission;


UPDATE cache_ad_user SET role_id = 1 WHERE role_id IS NULL;