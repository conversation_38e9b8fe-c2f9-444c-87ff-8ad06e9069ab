package ma.almobadara.backend.service.administration;

import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.administration.ProfileDTO;
import ma.almobadara.backend.enumeration.Functionality;
import ma.almobadara.backend.enumeration.Module;
import ma.almobadara.backend.exceptions.DuplicateFunctionalitiesException;
import ma.almobadara.backend.exceptions.DuplicateProfileException;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.mapper.ProfileMapper;
import ma.almobadara.backend.model.administration.CacheAdUser;
import ma.almobadara.backend.model.administration.Profile;
import ma.almobadara.backend.repository.administration.CacheAdUserRepository;
import ma.almobadara.backend.repository.administration.ProfileRepository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@RequiredArgsConstructor
@Slf4j
public class ProfileService {
    private final CacheAdUserRepository cacheAdUserRepository;
    private final ProfileRepository profileRepository;
    private final ProfileMapper profileMapper;
    private final EntityManager entityManager;

    public Page<ProfileDTO> getAllProfiles(int page, int size, String nameProfile, String[] modules) {
        Pageable pageable = PageRequest.of(page, size);
        Page<Profile> profiles;
        if (nameProfile != null || modules != null) {
            if (nameProfile == null) {
                profiles = profileRepository.findProfilesByModules(Arrays.asList(modules), modules.length, pageable);

            }
            else if(modules == null) {
                profiles = profileRepository.findProfilesByName(nameProfile, pageable);
            }
            else {
                profiles = profileRepository.findProfilesByModulesAndName(Arrays.asList(modules), modules.length, nameProfile, pageable);
            }
        } else {
            profiles = profileRepository.findAllWithDeletedIsFalse(pageable);
        }
        return profiles.map(profileMapper::toDTO);
    }


        public Page<Profile> filterProfiles(String nameProfile, String[] modules, Pageable pageable) {
        StringBuilder queryBuilder = new StringBuilder("SELECT p FROM Profile p WHERE p.isDeleted = false");

        if (nameProfile != null) {
            queryBuilder.append(" AND p.nameProfile LIKE :nameProfile");
        }

        if (modules != null && modules.length > 0) {
            queryBuilder.append(" AND (");
            for (int i = 0; i < modules.length; i++) {
                queryBuilder.append("KEY(p.moduleFunctionalities) = :module").append(i);
                queryBuilder.append(" AND SIZE(VALUE(p.moduleFunctionalities)) > 1");
                if (i < modules.length - 1) {
                    queryBuilder.append(" OR ");
                }
            }
            queryBuilder.append(")");
        }

        TypedQuery<Profile> typedQuery = entityManager.createQuery(queryBuilder.toString(), Profile.class);

        if (nameProfile != null) {
            typedQuery.setParameter("nameProfile", "%" + nameProfile + "%");
        }

      /*  if (modules != null && modules.length > 0) {
            for (int i = 0; i < modules.length; i++) {
                typedQuery.setParameter("module" + i, Module.valueOf(modules[i]));
            }
        }*/

        List<Profile> resultList = typedQuery.getResultList();

        long totalCount = resultList.size();

        typedQuery.setFirstResult((int) pageable.getOffset());
        typedQuery.setMaxResults(pageable.getPageSize());
        resultList = typedQuery.getResultList();

        return new PageImpl<>(resultList, pageable, totalCount);
    }


    public ProfileDTO createProfile(ProfileDTO profileDTO) {
        log.debug("Start service createProfile");
        Map<Module, ArrayList<Functionality>> sortedModuleFunctionalities = sortModuleFunctionalities(profileDTO.getModuleFunctionalities());

        if (profileExistsWithSimilarFunctionalities(sortedModuleFunctionalities)) {
            throw new DuplicateFunctionalitiesException("A profile with similar functionalities already exists.");
        }

        validateProfileName(profileDTO.getNameProfile());

        Profile profile = profileMapper.toEntity(profileDTO);
        profile = profileRepository.save(profile);
        log.debug("End service createProfile");
        return profileMapper.toDTO(profile);
    }


    public ProfileDTO updateProfile(Long profileId, ProfileDTO profileDTO) throws TechnicalException {
        log.debug("Start service updateProfile");
        Optional<Profile> optionalProfile = profileRepository.findById(profileId);
        if (optionalProfile.isEmpty()) {
            throw new TechnicalException("Profile not found with id: " + profileId);
        }

        Profile profile = optionalProfile.get();

        if (!profileDTO.getNameProfile().equals(profile.getNameProfile())) {
            validateProfileName(profileDTO.getNameProfile());
        }

        if ("Master Admin".equals(profile.getNameProfile())) {
            throw new RuntimeException("The master admin profile cannot be updated.");
        }

        Map<Module, ArrayList<Functionality>> sortedModuleFunctionalities = sortModuleFunctionalities(profileDTO.getModuleFunctionalities(), profileId);
// Stop this  verification for the moment
        /*
        if (profileExistsWithSimilarFunctionalities(sortedModuleFunctionalities, profileId)) {
            throw new DuplicateFunctionalitiesException("A profile with similar functionalities already exists.");
        }
        */
        profile.setModuleFunctionalities(sortedModuleFunctionalities);
        profile.setNameProfile(profileDTO.getNameProfile());
        profile = profileRepository.save(profile);
        log.debug("End service updateProfile");
        return profileMapper.toDTO(profile);
    }

    private void validateProfileName(String profileName) {
        Optional<Profile> existingProfile = profileRepository.findByNameProfile(profileName);
        if (existingProfile.isPresent()) {
            throw new DuplicateProfileException("A profile with similar name already exists.");
        }
    }

    private Map<Module, ArrayList<Functionality>> sortModuleFunctionalities(Map<Module, ArrayList<Functionality>> moduleFunctionalities) {
        Map<Module, ArrayList<Functionality>> sortedModuleFunctionalities = new HashMap<>();
        for (Map.Entry<Module, ArrayList<Functionality>> entry : moduleFunctionalities.entrySet()) {
            Module module = entry.getKey();
            ArrayList<Functionality> functionalities = entry.getValue();
            Collections.sort(functionalities);
            sortedModuleFunctionalities.put(module, functionalities);
        }
        return sortedModuleFunctionalities;
    }

    private Map<Module, ArrayList<Functionality>> sortModuleFunctionalities(Map<Module, ArrayList<Functionality>> moduleFunctionalities, Long profileId) {
        Map<Module, ArrayList<Functionality>> sortedModuleFunctionalities = new HashMap<>();
        for (Map.Entry<Module, ArrayList<Functionality>> entry : moduleFunctionalities.entrySet()) {
            Module module = entry.getKey();
            ArrayList<Functionality> functionalities = entry.getValue();
            Collections.sort(functionalities);
            sortedModuleFunctionalities.put(module, functionalities);
        }
        return sortedModuleFunctionalities;
    }

    private boolean profileExistsWithSimilarFunctionalities(Map<Module, ArrayList<Functionality>> moduleFunctionalities) {
        List<Profile> allProfiles = profileRepository.findAllByIsDeletedIsFalse();
        for (Profile profile : allProfiles) {
            if (profile.getModuleFunctionalities().equals(moduleFunctionalities)) {
                return true;
            }
        }
        return false;
    }

    private boolean profileExistsWithSimilarFunctionalities(Map<Module, ArrayList<Functionality>> moduleFunctionalities, Long profileId) {
        List<Profile> allProfiles = profileRepository.findAllByIsDeletedIsFalse();
        for (Profile profile : allProfiles) {
            if (profile.getId().equals(profileId)) {
                continue;
            }
            if (profile.getModuleFunctionalities().equals(moduleFunctionalities)) {
                return true;
            }
        }
        return false;
    }


    public Optional<ProfileDTO> getProfileById(Long profileId) {
        log.debug("Start service getProfileById for profileId: {}", profileId);
        try {
            Optional<Profile> optionalProfile = profileRepository.findById(profileId);
            log.debug("End service getProfileById for profileId: {}", profileId);
            return optionalProfile.map(profileMapper::toDTO);
        } catch (Exception e) {
            log.error("Error in getProfileById service for profileId: {}", profileId, e);
            throw e;
        }
    }

    public void deleteProfile(Long profileId) throws TechnicalException {
        log.debug("Start service deleteProfile for profileId: {}", profileId);
        try {
            Optional<Profile> optionalProfile = profileRepository.findById(profileId);
            if (optionalProfile.isEmpty()) {
                throw new TechnicalException("Profile not found with id: " + profileId);
            }

            // Check if any related users are soft deleted
            List<CacheAdUser> relatedUsers = cacheAdUserRepository.findByProfileId(profileId);
            if (!relatedUsers.isEmpty()) {
                // Handle the case where there are soft deleted users related to the profile
                throw new TechnicalException("Cannot delete profile with id " + profileId + ". It is still referenced by soft deleted users.");
            }

            Profile profile = optionalProfile.get();
            profile.setDeleted(true);
            profileRepository.save(profile); // Physically delete the profile

            log.debug("End service deleteProfile for profileId: {}", profileId);
        } catch (Exception e) {
            log.error("Error in deleteProfile service for profileId: {}", profileId, e);
            throw e;
        }
    }

}
