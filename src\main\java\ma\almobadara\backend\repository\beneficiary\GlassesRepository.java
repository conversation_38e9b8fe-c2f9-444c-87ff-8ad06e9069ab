package ma.almobadara.backend.repository.beneficiary;

import jakarta.transaction.Transactional;
import ma.almobadara.backend.model.beneficiary.Glasses;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface GlassesRepository extends JpaRepository<Glasses, Long> {

    Optional<Glasses> findByBeneficiaryId(Long beneficiaryId);

    @Transactional
    void deleteByBeneficiaryId(Long beneficiaryId);

}
