package ma.almobadara.backend.repository.administration;

import ma.almobadara.backend.model.administration.Feature;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface FeatureRepository extends JpaRepository<Feature, Long> {
    List<Feature> findByModule_Id(Long moduleId);
    Optional<Feature> findByCode(String code);
}

