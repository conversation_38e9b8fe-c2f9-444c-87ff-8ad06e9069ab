package ma.almobadara.backend.dto.dashboard;

import lombok.*;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class ActiveKafalatesByMonthAndCityDTO {

    private Integer month;
    private Integer year;
    private String sex;
    private Long cityId;
    private Long age;
    private String region;
    private Long servicesId;
    private Long totalActiveKafalates;
    private Long totalActiveOrphans;
    private String country;

    public ActiveKafalatesByMonthAndCityDTO(Long cityId, Integer month, Integer year, Long totalActiveOrphans) {
        this.month = month;
        this.year = year;
        this.cityId = cityId;
        this.totalActiveOrphans = totalActiveOrphans;
    }

    public ActiveKafalatesByMonthAndCityDTO(Long cityId, String region,Integer month, Integer year, Long totalActiveOrphans) {
        this.month = month;
        this.year = year;
        this.cityId = cityId;
        this.region = region;
        this.totalActiveOrphans = totalActiveOrphans;
    }

}
