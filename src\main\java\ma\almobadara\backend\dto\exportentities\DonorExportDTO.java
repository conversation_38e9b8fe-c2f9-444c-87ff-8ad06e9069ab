package ma.almobadara.backend.dto.exportentities;

import lombok.*;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class DonorExportDTO {

    private String code;
    private LocalDateTime createdAt;
    private String donorType;
    private String firstName;
    private String lastName;
    private String company;
    private String phoneNumber;
    private String email;
    private String status;
    private double balance;
    private String city;
    private String region;
    private String country;
    private String address;
    private String sex;

}
