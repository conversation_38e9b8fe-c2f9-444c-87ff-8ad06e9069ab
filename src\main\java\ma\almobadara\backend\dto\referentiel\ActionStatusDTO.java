package ma.almobadara.backend.dto.referentiel;

import lombok.*;

import java.io.Serializable;
import java.util.Objects;

@Setter
@Getter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class ActionStatusDTO implements Serializable {

    private Long id;


    private String code;
    private String name;
    private String nameAr;
    private String nameEn;


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof ActionStatusDTO parActionStatusDTO)) {
            return false;
        }

        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, parActionStatusDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "ParActionStatusDTO{" +
                "id=" + getId() +
                ", name='" + getName() + "'" +
                "}";
    }

}
