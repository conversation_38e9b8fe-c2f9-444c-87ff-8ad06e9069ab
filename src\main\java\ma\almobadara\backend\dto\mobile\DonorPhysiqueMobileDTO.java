package ma.almobadara.backend.dto.mobile;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;
import ma.almobadara.backend.dto.administration.CacheAdUserDTO;
import ma.almobadara.backend.dto.referentiel.CanalCommunicationDTO;
import ma.almobadara.backend.dto.referentiel.LanguageCommunicationDTO;
import ma.almobadara.backend.dto.referentiel.ProfessionDTO;
import ma.almobadara.backend.dto.referentiel.TypeIdentityDTO;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;
@Getter
@Setter
@NoArgsConstructor
@SuperBuilder
@ToString
@AllArgsConstructor
public class DonorPhysiqueMobileDTO {

    private Long id;

    private String name;

    private String nameAr;

    private String identityCode;

    private String address;

    private Double totalDonations;

    private Date registrationDate;

    private String sex;

    private String email;

    private String phoneNumber;

    private String pictureUrl;

    private String picture64;

    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private MultipartFile picture;

    private String type = "Physique";


    private TypeIdentityDTO typeIdentity;

    private ProfessionDTO profession;

    private Boolean isPasswordChanged = false;


}
