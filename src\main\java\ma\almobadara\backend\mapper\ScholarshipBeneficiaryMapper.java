package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.beneficiary.BeneficiaryHandicapDto;
import ma.almobadara.backend.model.beneficiary.Beneficiary;
import ma.almobadara.backend.model.beneficiary.BeneficiaryHandicap;
import ma.almobadara.backend.model.beneficiary.ScholarshipBeneficiary;
import ma.almobadara.backend.dto.beneficiary.BeneficiaryDTO;
import ma.almobadara.backend.dto.beneficiary.ScholarshipBeneficiaryDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface ScholarshipBeneficiaryMapper {

	@Mapping(source = "currencyId", target = "currency.id")
	@Mapping(source = "scholarshipId", target = "scholarship.id")
	ScholarshipBeneficiaryDTO scholarshipBeneficiaryToScholarshipBeneficiaryDTO(ScholarshipBeneficiary scholarshipBeneficiary);

	Iterable<ScholarshipBeneficiaryDTO> scholarshipBeneficiaryToScholarshipBeneficiaryDTO(Iterable<ScholarshipBeneficiary> scholarships);

	@Mapping(source = "currency.id", target = "currencyId")
	@Mapping(source = "scholarship.id", target = "scholarshipId")
	ScholarshipBeneficiary scholarshipBeneficiaryDTOToScholarshipBeneficiary(ScholarshipBeneficiaryDTO scholarshipBeneficiaryDTO);

	Iterable<ScholarshipBeneficiary> scholarshipBeneficiaryDTOToScholarshipBeneficiary(Iterable<ScholarshipBeneficiaryDTO> scholarshipDTOS);

	@Mapping(target = "scholarshipBeneficiaries", ignore = true)
	@Mapping(target = "beneficiaryServices", ignore = true)
	@Mapping(target = "educations", ignore = true)
	@Mapping(target = "allergies", ignore = true)
	@Mapping(target = "diseases", ignore = true)
	@Mapping(target = "notes", ignore = true)
	@Mapping(target = "documents", ignore = true)
	@Mapping(target = "epsResidents", ignore = true)
	@Mapping(target = "diseaseTreatments", ignore = true)
	@Mapping(target = "person", ignore = true)
	@Mapping(target = "takenInChargeBeneficiaries", ignore = true)
	BeneficiaryDTO beneficiaryToBeneficiaryDTO(Beneficiary beneficiary);

	@Mapping(target = "beneficiary",ignore = true)
	BeneficiaryHandicapDto beneficiaryHandicapToBeneficiaryHandicapDto(BeneficiaryHandicap beneficiaryHandicap);

}
