package ma.almobadara.backend.model.beneficiary;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class Glasses {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    protected Long id;
    private Boolean useGlasses;
    @OneToOne
    @JoinColumn(name = "beneficiaryId")
    private Beneficiary beneficiary;

}
