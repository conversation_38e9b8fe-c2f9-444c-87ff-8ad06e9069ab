package ma.almobadara.backend.controller.communs;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.communs.UserDTO;
import ma.almobadara.backend.service.communs.UserService;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/users")
public class UserController {

    private final UserService userService;
    @PostMapping(value = "/create", headers = "Accept=application/json")
    @Operation(summary = "Create a User", description = "add a new User", tags = {"SIG"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = UserDTO.class))))})

    public ResponseEntity<UserDTO> createUser(@RequestBody UserDTO user){
        log.info("Start resource Create User {}", "");
        UserDTO created = userService.addUserDonor(user);
        log.info("End resource Create User  {}", "");
        return new ResponseEntity<>(created, new HttpHeaders(), HttpStatus.OK);
    }

    @GetMapping("/all")
    public List<UserDTO> getAllUsers() {
        log.debug("get all users");
        return userService.getAllUsers();
    }

}
