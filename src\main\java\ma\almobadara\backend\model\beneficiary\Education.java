package ma.almobadara.backend.model.beneficiary;

import jakarta.annotation.Nullable;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import lombok.*;

import java.util.Map;

import static ma.almobadara.backend.util.strings.HandleSpecialChars.escapeSpecialChars;

@Entity
@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
@ToString
public class Education extends BaseEntity{

    private String educationType;
    private String schoolName;
    private String schoolNameAr;
    private Boolean succeed;
    private Double mark;
    private Long cityId;
    private Long schoolYearId;
    private Long schoolLevelId;
    @Nullable
    private Long honorId;
    private Long majorId;
    private Long educationSystemTypeId;
    private String studentNumber;
    private String comment;
    private String semestre;
    @ManyToOne
    @JoinColumn(name = "beneficiary_id")
    private Beneficiary beneficiary;

    public String getAudit(Map<String,String> params,String beneficiaryName) {
        return "{"
                + "\"Bénéficiaire\": \"" + escapeSpecialChars(beneficiaryName) + "\","
                + "\"Etablissement\": \"" + escapeSpecialChars(schoolName) + "\","
                + "\"Etablissement Arabe\": \"" + escapeSpecialChars(schoolNameAr) + "\","
                + "\"Niveau scolaire\": \"" + escapeSpecialChars(params.get("schoolLevel")) + "\","
                + "\"Année scolaire\": \"" + escapeSpecialChars(params.get("schoolYear")) + "\","
                + "\"Système éducatif\": \"" + escapeSpecialChars(params.get("educationSystem")) + "\","
                + "\"Numéro d'étudiant\": \"" + escapeSpecialChars(studentNumber) + "\","
                + "\"Pays\": \"" + escapeSpecialChars(params.get("pays")) + "\","
                + "\"Région\": \"" + escapeSpecialChars(params.get("region")) + "\","
                + "\"Ville\": \"" + escapeSpecialChars(params.get("ville")) + "\","
                + "\"Année scolaire réussie\": \"" + escapeSpecialChars(succeed?"Oui":"Non") + "\","
                + "\"Note de fin d'année\": \"" + escapeSpecialChars(String.valueOf(mark)) + "\","
                + "\"Mention\": \"" + escapeSpecialChars(params.get("honor")) + "\","
                + "\"Commentaire\": \"" + escapeSpecialChars(comment) + "\""
                + "}";
    }


}
