package ma.almobadara.backend.model.beneficiary;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Objects;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class NoteEducationId implements Serializable {

    private Long education;
    private Long note;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        NoteEducationId that = (NoteEducationId) o;
        return Objects.equals(education, that.education) && Objects.equals(note, that.note);
    }

    @Override
    public int hashCode() {
        return Objects.hash(education, note);
    }

}
