-- V1_80__update_takeInCharge_table.sql

-- First, we will add the new fields 'type' and 'status' to the 'taken_in_charge' table
ALTER TABLE taken_in_charge
    ADD COLUMN type VARCHAR(255),
    ADD COLUMN status VARCHAR(255);

-- Next, we will change the service_id to a foreign key relationship with the services table
-- Drop the existing service_id and status_id columns if they exist
ALTER TABLE taken_in_charge
DROP COLUMN service_id,
    DROP COLUMN status_id;

-- Add the new service_id column
ALTER TABLE taken_in_charge
    ADD COLUMN service_id BIGINT;

-- Add the foreign key constraint
ALTER TABLE taken_in_charge
    ADD CONSTRAINT fk_service
        FOREIGN KEY (service_id) REFERENCES services(id);
