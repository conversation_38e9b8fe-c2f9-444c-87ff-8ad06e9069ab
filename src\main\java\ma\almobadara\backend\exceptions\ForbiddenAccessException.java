package ma.almobadara.backend.exceptions;

import lombok.Getter;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@Getter
@ResponseStatus(HttpStatus.FORBIDDEN)
public class ForbiddenAccessException extends RuntimeException {
    private final int statusCode;

    public ForbiddenAccessException(String message, int statusCode) {
        super(message);
        this.statusCode = statusCode;
    }
}
