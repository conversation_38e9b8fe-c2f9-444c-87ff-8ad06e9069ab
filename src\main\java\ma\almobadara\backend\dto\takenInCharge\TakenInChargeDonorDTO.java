package ma.almobadara.backend.dto.takenInCharge;

import lombok.*;
import ma.almobadara.backend.dto.donor.DonorDTO;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class TakenInChargeDonorDTO {

	private Long id;

	private Boolean keepanonymous;

	private DonorDTO donor;

	private TakenInChargeDTO takenInCharge;

	private List<TakenInChargeOperationDTO> takenInChargeOperations;

	private Double DonorBalance;

}
