package ma.almobadara.backend.repository.administration;

import ma.almobadara.backend.model.administration.CacheAdUser;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CacheAdUserRepository extends CrudRepository<CacheAdUser, Long> {


    @Query(value = "select u from CacheAdUser u where u.mail = ?1 and u.isDeleted = false")
    CacheAdUser findByMailOrMailNullAndAzureDirectoryIdFromAll(String key);

    CacheAdUser findByMail(String mail);

    CacheAdUser findByAzureDirectoryIdAndIsDeletedIsFalse(String azureDirectoryId);

    Optional<CacheAdUser> findByAzureDirectoryId(String azureDirectoryId);
// we should add in findAll the is deleted is false
    @Query("SELECT u FROM CacheAdUser u WHERE u.isDeleted = false")
    Page<CacheAdUser> findAll(Pageable pageable);

   // get users by profile id and deleted is false
    @Query("SELECT u FROM CacheAdUser u WHERE u.profile.id = :profileId AND u.isDeleted = false")
    List<CacheAdUser> findByProfileId(@Param("profileId") Long profileId);

    @Query("SELECT u FROM CacheAdUser u WHERE " +
            "(:firstName is null or LOWER(u.firstName) LIKE LOWER(CONCAT('%', :firstName, '%'))) AND " +
            "(:lastName is null or LOWER(u.lastName) LIKE LOWER(CONCAT('%', :lastName, '%')))")
    Page<CacheAdUser> findByFirstNameAndLastName(
            @Param("firstName") String firstName,
            @Param("lastName") String lastName,
            Pageable pageable);

    Optional<CacheAdUser> findByIdAndIsDeletedIsFalse(Integer userId);

}
