package ma.almobadara.backend.repository.caisse;

import ma.almobadara.backend.model.aideComplemenatire.AideComplementaire;
import ma.almobadara.backend.model.caisse.Caisse;
import ma.almobadara.backend.model.donation.BudgetLine;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CaisseRepository extends JpaRepository<Caisse, Long> {

   /* Optional<Caisse> findTopByOrderByCodeDesc();
    boolean existsByCode(String code);
    Optional<Caisse> findCaisseByTypePriseEnChargeId(Long typePriseEnChargeId);

    @Query("SELECT c FROM Caisse c WHERE c.isDeleted = false ORDER BY c.updatedAt DESC")
    Page<Caisse> findAllWithDeletedIsFalse(Pageable pageable);

    //Query("SELECT b FROM BudgetLine b WHERE b.typePriseEnChargeId = :typePriseEnChargeId AND (b.executed IS NULL OR b.executed = false)")
    //@Query("SELECT b FROM BudgetLine b WHERE b.typePriseEnChargeId = :typePriseEnChargeId ")
    Page<BudgetLine> findByTypePriseEnChargeAndNotExecuted(@Param("typePriseEnChargeId") Long typePriseEnChargeId, Pageable pageable);

    @Query("SELECT b FROM BudgetLine b WHERE b.type = :type")
   // @Query("SELECT b FROM BudgetLine b WHERE b.type = :type AND (b.executed IS NULL OR b.executed = false)")
    Page<BudgetLine> findByType(String type, Pageable pageable);

  //  @Query("SELECT a FROM AideComplementaire a WHERE a.typePriseEnChargeId = :typePriseEnChargeId ")
    Page<AideComplementaire> findByTypePriseEnChargeId(@Param("typePriseEnChargeId") Long typePriseEnChargeId, Pageable pageable);

   // @Query("SELECT a FROM AideComplementaire a WHERE a.typePriseEnChargeId = :typePriseEnChargeId AND a.statut = :statut")
    Page<AideComplementaire> findByTypePriseEnChargeIdAndStatut(@Param("typePriseEnChargeId") Long typePriseEnChargeId, @Param("statut") String statut, Pageable pageable);
    //@Query("SELECT b FROM BudgetLine b WHERE b.type = :type AND (b.executed IS NULL OR b.executed = false)")
    @Query("SELECT b FROM BudgetLine b WHERE b.type = :type")
    List<BudgetLine> findByType(String type);

*/
}
