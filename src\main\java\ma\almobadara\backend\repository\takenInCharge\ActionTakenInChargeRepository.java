package ma.almobadara.backend.repository.takenInCharge;

import ma.almobadara.backend.model.takenInCharge.ActionTakenInCharge;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ActionTakenInChargeRepository extends JpaRepository<ActionTakenInCharge, Long> {

    List<ActionTakenInCharge> findByTakenInChargeId(Long takenInChargeId);

    List<ActionTakenInCharge> findByActionId(Long id);

}
