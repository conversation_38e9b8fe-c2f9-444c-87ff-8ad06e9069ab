package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.donation.DonationProductNatureDTO;
import ma.almobadara.backend.model.donation.DonationProductNature;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface DonationProductNatureMapper {

	@Mapping(target = "donation", ignore = true)
	@Mapping(source = "productNatureId", target = "productNature.id")
	@Mapping(source = "productUnitId", target = "productUnit.id")
	DonationProductNatureDTO donationProductNatureDtoToDTO(DonationProductNature donationProductNature);

	Iterable<DonationProductNatureDTO> donationProductNatureToDTO(Iterable<DonationProductNature> donationProductNatures);

	@Mapping(source = "productNature.id", target = "productNatureId")
	@Mapping(source = "productUnit.id", target = "productUnitId")
	DonationProductNature donationProductNatureDTOToDonationProductNature(DonationProductNatureDTO donationProductNatureDTO);

	Iterable<DonationProductNature> donationProductNatureDTOToDonationProductNature(Iterable<DonationProductNatureDTO> donationProductNatureDTOS);

	/*@Mapping(target = "productNatures", ignore = true)
	TypeProductNatureDTO typeProductNatureToTypeProductNatureDTO(TypeProductNature typeProductNature);*/


}
