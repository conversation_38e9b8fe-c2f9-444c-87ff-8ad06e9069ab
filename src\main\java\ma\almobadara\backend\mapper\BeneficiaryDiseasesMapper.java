package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.beneficiary.BeneficiaryDiseasesDto;
import ma.almobadara.backend.dto.beneficiary.DiseaseBeneficiaryAddDto;
import ma.almobadara.backend.model.beneficiary.BeneficiaryDisease;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public interface BeneficiaryDiseasesMapper {

    @Mapping(source = "diseaseId", target = "disease.id")
    BeneficiaryDiseasesDto beneficiaryDiseaseToBeneficiaryDiseaseDto(BeneficiaryDisease beneficiaryDisease);

    Iterable<BeneficiaryDiseasesDto> beneficiaryDiseaseToBeneficiaryDiseaseDto(Iterable<BeneficiaryDisease> beneficiaryDiseases);

    @Mapping(source = "disease.id", target = "diseaseId")
    BeneficiaryDisease beneficiaryDiseaseDtoToBeneficiaryDisease(BeneficiaryDiseasesDto beneficiaryDiseaseDto);

    Iterable<BeneficiaryDisease> beneficiaryDiseaseDtoToBeneficiaryDisease(Iterable<BeneficiaryDiseasesDto> beneficiaryDiseaseDtos);

    Iterable<BeneficiaryDisease> beneficiaryDiseaseDtoToBeneficiaryDisease(List<DiseaseBeneficiaryAddDto> diseaseBeneficiaryAddDtoList);

    BeneficiaryDisease DiseaseBeneficiaryAddDtoToBeneficiaryDisease(DiseaseBeneficiaryAddDto diseaseBeneficiaryAddDto);

}
