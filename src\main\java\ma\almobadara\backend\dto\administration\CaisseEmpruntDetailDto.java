package ma.almobadara.backend.dto.administration;

import lombok.*;
import ma.almobadara.backend.dto.donor.DonorDTO;
import ma.almobadara.backend.dto.service.ServicesDTO;

import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CaisseEmpruntDetailDto {
    private Long id;
    private Double amount;
    private String status;
    private LocalDateTime dateEmprunt;
    private LocalDateTime dateRemboursement;
    private LocalDateTime creationDate;
    private LocalDateTime updateDate;
    
    // Detailed donor information
    private DonorDTO donor;
    
    // Detailed service information
    private ServicesDTO service;
}
