package ma.almobadara.backend.dto.administration;

import lombok.*;
import ma.almobadara.backend.dto.donor.DonorDTO;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CaisseEmpruntDetailDto {
    private Long id;
    private Double globalAmount;
    private LocalDateTime lastDateEmprunt;
    private LocalDateTime lastDateRemboursement;
    private String status;
    private LocalDateTime creationDate;
    private LocalDateTime updateDate;

    private Long donorId;
    private String donorName;

    // Complete history of emprunts and remboursements
    private List<CaisseEmpruntHistoryDto> histories;

    // Summary calculations
    private Double totalEmpruntAmount;
    private Double totalRemboursementAmount;
    private Double remainingAmount;
    private Integer totalHistoryRecords;
}
