package ma.almobadara.backend.repository.donor;

import jakarta.transaction.Transactional;
import ma.almobadara.backend.model.donor.DonorPhysicalLanguageCommunication;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface DonorPhysicalLanguageCommunicationRepository extends JpaRepository<DonorPhysicalLanguageCommunication, Long> {

    @Transactional
    @Modifying
    @Query(value = "DELETE FROM donor_physical_language_communication WHERE donor_physical_id = :donorId", nativeQuery = true)
    void deleteAllLanguageByDonorId(Long donorId);

}
