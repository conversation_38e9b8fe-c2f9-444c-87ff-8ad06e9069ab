package ma.almobadara.backend.repository.administration;

import ma.almobadara.backend.model.administration.CaisseEmprunt;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface CaisseEmpruntRepository extends JpaRepository<CaisseEmprunt, Long> {

    @Query("SELECT ce FROM CaisseEmprunt ce ORDER BY ce.updateDate DESC")
    Page<CaisseEmprunt> findAllOrderByUpdateDateDesc(Pageable pageable);

    Optional<CaisseEmprunt> findByDonorId(Long donorId);

}
