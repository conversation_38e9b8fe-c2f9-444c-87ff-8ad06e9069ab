package ma.almobadara.backend.repository.donor;

import ma.almobadara.backend.dto.referentiel.CityDTO;
import ma.almobadara.backend.dto.donor.DonorDTO;
import ma.almobadara.backend.dto.referentiel.DonorStatusDTO;
import ma.almobadara.backend.model.donor.Donor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DonorCustomizedRepository {

    Page<Donor> filterDonor(Pageable pageable, DonorDTO donorDTO, String type, String donorName , String columnName, String sortType, String phone, String mail, List<CityDTO> cityDTOList, List<DonorStatusDTO> StatusDTOList) ;

}
