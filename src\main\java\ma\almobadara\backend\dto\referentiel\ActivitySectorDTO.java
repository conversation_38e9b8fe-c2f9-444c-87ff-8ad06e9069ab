package ma.almobadara.backend.dto.referentiel;

import lombok.*;
import org.springframework.hateoas.RepresentationModel;

import java.io.Serializable;

@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class ActivitySectorDTO extends RepresentationModel<ActivitySectorDTO> implements Serializable {

	private Long id;

	private String code;
	private String name;
	private String nameAr;
	private String nameEn;

}
