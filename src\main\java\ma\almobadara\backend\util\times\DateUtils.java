package ma.almobadara.backend.util.times;

public class DateUtils {
    private static final String[] MONTHS = {
            "janvier", "février", "mars", "avril", "mai", "juin",
            "juillet", "août", "septembre", "octobre", "novembre", "décembre"
    };

    public static String getMonthName(int month) {
        return MONTHS[month - 1];
    }

    public static java.time.LocalDate parseLocalDateFromString(String dateStr) throws ma.almobadara.backend.exceptions.TechnicalException {
        try {
            java.time.format.DateTimeFormatter formatter = java.time.format.DateTimeFormatter.ofPattern("dd/MM/yyyy");
            return java.time.LocalDate.parse(dateStr, formatter);
        } catch (Exception e) {
            throw new ma.almobadara.backend.exceptions.TechnicalException("Invalid date format: " + dateStr);
        }
    }

}
