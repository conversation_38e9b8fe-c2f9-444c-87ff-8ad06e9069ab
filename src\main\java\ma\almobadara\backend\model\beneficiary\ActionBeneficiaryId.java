package ma.almobadara.backend.model.beneficiary;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Objects;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ActionBeneficiaryId implements Serializable {

    private Long beneficiary;
    private Long action;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ActionBeneficiaryId that = (ActionBeneficiaryId) o;
        return Objects.equals(beneficiary, that.beneficiary) && Objects.equals(action, that.action);
    }

    @Override
    public int hashCode() {
        return Objects.hash(beneficiary, action);
    }

}
