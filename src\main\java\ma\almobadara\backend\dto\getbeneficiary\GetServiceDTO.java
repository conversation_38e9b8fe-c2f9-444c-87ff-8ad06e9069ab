package ma.almobadara.backend.dto.getbeneficiary;

import lombok.*;
import org.springframework.hateoas.RepresentationModel;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class GetServiceDTO extends RepresentationModel<GetServiceDTO> implements Serializable {

	private static final long serialVersionUID = -4677900036494852561L;

	private Long serviceId;

	private String service;

	private Long categoryId;

	private String category;

	private Long statusId;

	private String status;

}
