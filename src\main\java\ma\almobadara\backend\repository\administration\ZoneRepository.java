package ma.almobadara.backend.repository.administration;

import ma.almobadara.backend.model.administration.Zone;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import javax.swing.text.html.Option;
import java.util.List;
import java.util.Optional;

@Repository
public interface ZoneRepository extends JpaRepository<Zone, Long> {
    // findAll by deleted is false and with pageable and sorted by updated at
    @Query("SELECT z FROM Zone z WHERE z.isDeleted = false ORDER BY z.updatedAt DESC")
    Page<Zone> findAllWithDeletedIsFalse(Pageable pageable);

    @Query("SELECT z FROM Zone z WHERE z.name = :name AND z.isDeleted = false")
    Optional<Zone> findByName(String name);

    @Query("SELECT z FROM Zone z WHERE z.code = :code AND z.isDeleted = false")
    Optional<Zone> findByCode(String code);

    @Query("SELECT z FROM Zone z WHERE  z.isDeleted = false")
    List<Zone> findAllByIsDeletedIsFalse();

    // get the list of zones that are not asign to an assistant

    @Query("SELECT z FROM Zone z WHERE z.isDeleted = false AND z.assistant IS NULL AND z.status = TRUE ")
    List<Zone> findAllByAssistantIsNullAndIsDeletedIsFalse();

    //findLastZoneCode
    @Query("SELECT z FROM Zone z WHERE z.isDeleted = false ORDER BY z.code DESC")
    Optional<Zone> findLastZoneCode();

    Zone findFirstByOrderByCodeDesc();

    // find all the active zones
    @Query("SELECT z FROM Zone z WHERE z.isDeleted = false AND z.status = TRUE")
    List<Zone> findAllByStatusIsTrueAndIsDeletedIsFalse();

    //find by assistnt id
    //existsByAssistantIf just a bollean
    boolean existsByAssistantId(Long id);
}