package ma.almobadara.backend.model.family;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Objects;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class DocumentFamilyId implements Serializable {

    private Long family;
    private Long document;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DocumentFamilyId that = (DocumentFamilyId) o;
        return Objects.equals(family, that.family) && Objects.equals(document, that.document);
    }

    @Override
    public int hashCode() {
        return Objects.hash(family, document);
    }

}
