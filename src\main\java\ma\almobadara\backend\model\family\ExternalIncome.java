package ma.almobadara.backend.model.family;

import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import lombok.*;
import ma.almobadara.backend.model.beneficiary.BaseEntity;

import java.util.Date;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ExternalIncome extends BaseEntity {

    private Double amount;
    private int periodicity;
    private String comment;
    private Long incomeSourceId;
    private Date startDate;
    private Date endDate;
    @ManyToOne
    @JoinColumn(name = "family_member_id")
    private FamilyMember familyMember;

}
