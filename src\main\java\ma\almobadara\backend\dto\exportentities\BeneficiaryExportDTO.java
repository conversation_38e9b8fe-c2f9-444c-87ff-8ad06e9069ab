package ma.almobadara.backend.dto.exportentities;

import lombok.*;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class BeneficiaryExportDTO {
    private String code;
    private Instant createdAt;
    private String firstName;
    private String lastName;
    private String type;
    private String phoneNumber;
    private String address;
    private String city;
    private String region;
    private String country;
    private String birthDate;
    private String typeIdentity;
    private String identityCode;
    private String email;
    private String sex;

    public String getFormattedCreatedAt() {
        if (createdAt != null) {
            LocalDateTime localDateTime = LocalDateTime.ofInstant(createdAt, ZoneId.systemDefault());
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss");
            return localDateTime.format(formatter);
        } else {
            return "";
        }
    }

    public String getFormattedBirthDate() {
        if (birthDate != null) {
            LocalDateTime localDateTime = LocalDateTime.ofInstant(createdAt, ZoneId.systemDefault());
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
            return localDateTime.format(formatter);
        } else {
            return "";
        }
    }
}

