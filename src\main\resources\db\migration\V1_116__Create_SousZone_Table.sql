CREATE TABLE sous_zone (
                           id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    code VA<PERSON>HAR(255) NOT NULL UNIQUE,
    name VARCHAR(255) NOT NULL,
    name_ar VARCHAR(255) NOT NULL,
    detail TEXT,
    zone_id BIGINT NOT NULL,
    creation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    update_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    CONSTRAINT fk_sous_zone_zone FOREIGN KEY (zone_id) REFERENCES zone (id) ON DELETE CASCADE
);


