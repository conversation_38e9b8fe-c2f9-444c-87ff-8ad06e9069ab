package ma.almobadara.backend.model.beneficiary;


import jakarta.persistence.*;
import lombok.*;
import ma.almobadara.backend.model.communs.Note;


@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@IdClass(NoteBeneficiaryId.class)
public class NoteBeneficiary {

    @Id
    @ManyToOne
    @JoinColumn(name = "beneficiary_id")
    private Beneficiary beneficiary;

    @Id
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "note_id")
    private Note note;

}
