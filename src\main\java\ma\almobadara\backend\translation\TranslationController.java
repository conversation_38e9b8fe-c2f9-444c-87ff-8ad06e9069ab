package ma.almobadara.backend.translation;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
@RestController
public class TranslationController {

    private final TranslationService translationService;

    public TranslationController(TranslationService translationService) {
        this.translationService = translationService;
    }

    @GetMapping("/translate")
    public Map<String, String> translateText(@RequestParam String text,  @RequestParam String sourceLang) {
        try {
            return translationService.translate(text, sourceLang);
        } catch (Exception e) {
            e.printStackTrace();
            return Map.of("error", "Translation failed: " + e.getMessage());
        }
    }
}