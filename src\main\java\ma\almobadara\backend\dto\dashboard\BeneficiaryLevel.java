package ma.almobadara.backend.dto.dashboard;

import lombok.*;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class BeneficiaryLevel {

    private Long schoolLevel;
    private Long successfulCount;
    private Long failedCount;
    private String schoolLevelName;

//    public BeneficiaryLevel(Long schoolLevel, Long successfulCount, Long failedCount) {
//        this.schoolLevel = schoolLevel;
//        this.successfulCount = successfulCount;
//        this.failedCount = failedCount;
//    }

}
