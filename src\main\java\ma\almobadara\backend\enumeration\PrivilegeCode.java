package ma.almobadara.backend.enumeration;

public enum PrivilegeCode {
    READ("READ"),
    WRITE("WRITE"),
    UPDATE("UPDATE"),
    CREATE("CREATE"),
    DELETE("DELETE"),
    VALIDATE("VALIDATE"),
    REJECT("REJECT"),
    ARCHIVE("ARCHIVE"),
    COMPLETE("COMPLETE");

    private final String code;

    PrivilegeCode(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public static PrivilegeCode fromCode(String code) {
        for (PrivilegeCode privilegeCode : PrivilegeCode.values()) {
            if (privilegeCode.getCode().equalsIgnoreCase(code)) {
                return privilegeCode;
            }
        }
        throw new IllegalArgumentException("Unknown privilege code: " + code);
    }
}

