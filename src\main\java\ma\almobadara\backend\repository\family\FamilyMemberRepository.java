package ma.almobadara.backend.repository.family;

import ma.almobadara.backend.model.administration.Eps;
import ma.almobadara.backend.model.family.Family;
import ma.almobadara.backend.model.family.FamilyMember;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
public interface FamilyMemberRepository extends JpaRepository<FamilyMember, Long> {
    @Query("SELECT f FROM FamilyMember f WHERE f.isDeleted = false AND (LOWER(f.person.firstName) LIKE LOWER(CONCAT('%', :query, '%')) OR LOWER(f.person.lastName) LIKE LOWER(CONCAT('%', :query, '%')) OR LOWER(f.person.email) LIKE LOWER(CONCAT('%', :query, '%')) OR LOWER(f.person.phoneNumber) LIKE LOWER(CONCAT('%', :query, '%')) OR LOWER(f.code) LIKE LOWER(CONCAT('%', :query, '%')))")
    Page<FamilyMember> searchMembers(@Param("query") String query, Pageable pageable);

    @Query("SELECT f FROM FamilyMember f WHERE f.family = :family AND f.isDeleted = false")
    List<FamilyMember> findAllByFamily(@Param("family") Family family);

    @Query("SELECT f FROM FamilyMember f WHERE f.person.id = :personId AND f.isDeleted = false")
    Optional<FamilyMember> findByPersonId(@Param("personId") Long personId);

    @Query("SELECT f FROM FamilyMember f WHERE f.person.id IN :personIds AND f.isDeleted = false")
    List<FamilyMember> findByPersonIdIn(@Param("personIds") Set<Long> personIds);

    @Query("SELECT fm FROM FamilyMember fm JOIN fm.person p WHERE p.identityCode = :firstIdentityCode AND fm.isDeleted = false")
    FamilyMember findByFirstIdentityCode(@Param("firstIdentityCode") String firstIdentityCode);

    @Query("SELECT COUNT(f) FROM FamilyMember f WHERE f.family.id = :familyId AND f.isDeleted = false")
    Long countByFamilyId(@Param("familyId") Long familyId);
}
