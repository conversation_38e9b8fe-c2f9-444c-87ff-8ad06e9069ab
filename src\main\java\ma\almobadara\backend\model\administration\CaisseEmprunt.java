package ma.almobadara.backend.model.administration;

import jakarta.persistence.*;
import lombok.*;
import ma.almobadara.backend.model.donor.Donor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.List;

@Entity
@Getter
@Setter
@ToString
@Data
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class CaisseEmprunt {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    @JoinColumn(name = "donor_id")
    private Donor donor;

    private Double globalAmount;
    private LocalDateTime lastDateEmprunt;
    private LocalDateTime lastDateRemboursement;
    private String status;

    @OneToMany(mappedBy = "caisseEmprunt", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<CaisseEmpruntHistory> histories;

    @CreationTimestamp
    @Column(updatable = false)
    private LocalDateTime creationDate;

    @UpdateTimestamp
    private LocalDateTime updateDate;
}
