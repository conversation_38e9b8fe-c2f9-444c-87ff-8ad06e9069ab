package ma.almobadara.backend.model.administration;

import jakarta.persistence.*;
import lombok.*;
import ma.almobadara.backend.model.donor.Donor;
import ma.almobadara.backend.model.service.Services;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.Date;

@Entity
@Getter
@Setter
@ToString
@Data
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class CaisseEmprunt {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private LocalDateTime dateEmprunt;
    private LocalDateTime dateRemboursement;

    private Double  amount ;

    @ManyToOne
    @JoinColumn(name = "donor_id")
    private Donor donor;
    @ManyToOne
    @JoinColumn(name = "service_id")
    private Services services;
    private String status;

    @CreationTimestamp
    @Column(updatable = false)
    private LocalDateTime creationDate;

    @UpdateTimestamp
    private LocalDateTime updateDate;
}
