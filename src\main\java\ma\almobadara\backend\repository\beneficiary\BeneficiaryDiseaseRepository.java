package ma.almobadara.backend.repository.beneficiary;

import ma.almobadara.backend.model.beneficiary.BeneficiaryDisease;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface BeneficiaryDiseaseRepository extends JpaRepository<BeneficiaryDisease, Long> {
    List<BeneficiaryDisease> findByBeneficiaryId(Long id);

}