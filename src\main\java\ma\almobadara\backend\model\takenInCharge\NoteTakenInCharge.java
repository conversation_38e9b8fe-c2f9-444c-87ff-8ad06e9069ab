package ma.almobadara.backend.model.takenInCharge;

import jakarta.persistence.*;
import lombok.*;
import ma.almobadara.backend.model.communs.Note;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@IdClass(NoteTakenInChargeId.class)
public class NoteTakenInCharge {

    @Id
    @ManyToOne
    @JoinColumn(name = "taken_in_charge_id")
    private TakenInCharge takenInCharge;

    @Id
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "note_id")
    private Note note;

}
