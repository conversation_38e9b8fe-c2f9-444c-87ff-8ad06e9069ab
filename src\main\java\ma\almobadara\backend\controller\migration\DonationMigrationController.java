package ma.almobadara.backend.controller.migration;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.service.migration.DonationMigrationService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/donation-migration")
@Slf4j
@RequiredArgsConstructor
public class DonationMigrationController {

    private final DonationMigrationService donationMigrationService;

    @PostMapping("/upload")
    public ResponseEntity<String> migrateDonations(@RequestParam("file") MultipartFile file) {
        log.info("Received request to migrate donations");
        try {
            donationMigrationService.migrateDonations(file);
            return ResponseEntity.ok("Donation migration completed successfully.");
        } catch (Exception e) {
            log.error("Error during donation migration: {}", e.getMessage());
            return ResponseEntity.internalServerError().body("Error during donation migration: " + e.getMessage());
        }
    }
} 