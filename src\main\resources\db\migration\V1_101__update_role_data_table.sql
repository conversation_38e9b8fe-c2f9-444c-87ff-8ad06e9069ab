UPDATE cache_ad_user SET role_id = NULL;

ALTER SEQUENCE role_id_seq RESTART WITH 1;

INSERT INTO role (code, name, description, creation_date, update_date)
VALUES
    ('ADMIN', 'Admin', 'Rôle d''administrateur avec tous les privilèges', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('ASSISTANT', 'Assistant', 'Rôle d''assistant avec des privilèges limités', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('GESTIONNAIRE_KAFALAT', 'Gestionnaire Ka<PERSON>lat', 'Rôle de gestionnaire pour la gestion des Kafalats', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('GESTIONNAIRE_MARKETING', 'Gestionnaire Marketing', 'Rôle de gestionnaire pour le département marketing', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

UPDATE cache_ad_user SET role_id = 1;
ALTER SEQUENCE privilege_id_seq RESTART WITH 1;


INSERT INTO privilege (label, code, creation_date, update_date)
VALUES
    ('Read', 'READ', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('Write', 'WRITE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('Update', 'UPDATE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('Create', 'CREATE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('Delete', 'DELETE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('Validate', 'VALIDATE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('Reject', 'REJECT', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('Complete', 'COMPLETE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('Archive', 'ARCHIVE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);