package ma.almobadara.backend.repository.beneficiary;

import ma.almobadara.backend.model.beneficiary.BankCard;
import ma.almobadara.backend.model.beneficiary.Person;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface BankCardRepository extends JpaRepository<BankCard, Long> {

    List<BankCard> findByPerson(Person person);

    @Query(value = "SELECT * FROM bank_card WHERE status = 'activated' AND person_id IN (" +
            "SELECT person_id FROM beneficiary WHERE id = :id LIMIT 1) " +
            "AND expiry_date > NOW() ORDER BY created_at DESC LIMIT 1",
            nativeQuery = true)
    BankCard findActifBankCardForBeneficiary(@Param("id") Long id);

    @Query(value = "SELECT * FROM bank_card WHERE status = 'activated' AND person_id IN (" +
            "SELECT person_id FROM family_member WHERE person_id = :id LIMIT 1) " +
            "AND expiry_date > NOW() ORDER BY created_at DESC LIMIT 1",
            nativeQuery = true)
    BankCard findActifBankCardForFamilyMember(@Param("id") Long id);


}
