package ma.almobadara.backend.service.administration;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.administration.RoleDTO;
import ma.almobadara.backend.mapper.RoleMapper;
import ma.almobadara.backend.model.administration.Role;
import ma.almobadara.backend.repository.administration.RoleRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class RoleService {

    private final RoleRepository roleRepository;
    private final RoleMapper roleMapper;

    public Page<RoleDTO> getAllRoles(int page , int size){
        log.info("Start service getAllRoles with page: {}, size: {}", page, size);
        Pageable pageable = PageRequest.of(page, size);
        Page<Role> roles = roleRepository.findAll(pageable);

        log.info("End service getAllRoles with page: {}, size: {}", page, size);
        return roles.map(roleMapper::toDTO);
    }
}
