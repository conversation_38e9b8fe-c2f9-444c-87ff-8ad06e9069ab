package ma.almobadara.backend.controller.mobile;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.config.HasAccessToModule;
import ma.almobadara.backend.config.JwtUtils;
import ma.almobadara.backend.dto.administration.LoginDTO;
import ma.almobadara.backend.dto.donor.DonorDTO;
import ma.almobadara.backend.dto.donor.DonorContactDTO;
import ma.almobadara.backend.dto.donor.DonorMoralDTO;
import ma.almobadara.backend.dto.donor.DonorPhysicalDTO;
import ma.almobadara.backend.dto.mobile.DonorAuthResponseDTO;
import ma.almobadara.backend.dto.mobile.DonorMoralMobileDTO;
import ma.almobadara.backend.dto.mobile.DonorPhysiqueMobileDTO;
import ma.almobadara.backend.dto.mobile.RefreshTokenRequestDTO;
import ma.almobadara.backend.dto.mobile.RefreshTokenResponseDTO;
import ma.almobadara.backend.dto.mobile.ResetPasswordDTO;
import ma.almobadara.backend.dto.mobile.EmailVerificationRequestDTO;
import ma.almobadara.backend.dto.mobile.EmailVerificationResponseDTO;
import ma.almobadara.backend.dto.mobile.CodeValidationRequestDTO;
import ma.almobadara.backend.dto.mobile.CodeValidationResponseDTO;
import ma.almobadara.backend.enumeration.Functionality;
import ma.almobadara.backend.enumeration.Module;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.model.donor.Donor;
import ma.almobadara.backend.service.donor.DonorService;
import ma.almobadara.backend.service.mobile.DonorMobileService;
import ma.almobadara.backend.service.mobile.EmailVerificationService;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.util.HashMap;

import static ma.almobadara.backend.service.administration.CacheAdUserService.logUserInfo;

@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/mobile/donor")
public class DonorMobileController {

    private final DonorMobileService donorMobileService;
    private final DonorService donorService;
    private final JwtUtils jwtUtils;
    private final EmailVerificationService emailVerificationService;



    @PostMapping("/login/donorPhysique")
    public ResponseEntity<?> loginDonorPhysique(@RequestBody LoginDTO loginDTO) {

        logUserInfo("loginDonorPhysique", loginDTO.getEmail());

        DonorPhysiqueMobileDTO donorPhysiqueMobileDTO = null;
        HttpStatus status;
        try {
            donorPhysiqueMobileDTO = donorMobileService.loginDonorPhysique(loginDTO);

            // Generate JWT tokens
            String accessToken = jwtUtils.generateToken(loginDTO.getEmail());
            String refreshToken = jwtUtils.generateRefreshToken(loginDTO.getEmail());

            // Create response with donor data and tokens
            DonorAuthResponseDTO<DonorPhysiqueMobileDTO> response = DonorAuthResponseDTO.<DonorPhysiqueMobileDTO>builder()
                    .donor(donorPhysiqueMobileDTO)
                    .accessToken(accessToken)
                    .refreshToken(refreshToken)
                    .build();

            status = HttpStatus.OK;
            log.info("End resource loginDonorPhysique : {}. Retrieved donor: {}, OK", loginDTO.getEmail(), donorPhysiqueMobileDTO);
            return new ResponseEntity<>(response, status);
        } catch (Exception e) {
            status = HttpStatus.NOT_FOUND;
            log.error("End resource loginDonorPhysique : {}. KO: {}", loginDTO.getEmail(), e.getMessage());
            return new ResponseEntity<>(null, status);
        }
    }

    @GetMapping(value = "/findByCriteria", produces = "application/json")
    public ResponseEntity<Page<DonorDTO>> findDonorsByCriteria(
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String typeDonor,
            @RequestParam(required = false) String firstName,
            @RequestParam(required = false) String lastName,
            @RequestParam(required = false) String companyName,
            @RequestParam(required = false) final String anonymeDonor,
            @RequestParam(required = false) final String searchByDonorType,
            @RequestParam(required = false) final String searchByNom,
            @RequestParam(required = false) final String lastNameAr,
            @RequestParam(required = false) final String searchByPrenom,
            @RequestParam(required = false) final String searchByPhoneNum,
            @RequestParam(required = false) final String searchByEmail,
            @RequestParam(required = false) final Integer searchByStatus,
            @RequestParam(required = false) final Long searchByTagId

    ) {

        logUserInfo("findDonorsByCriteria", typeDonor, firstName, lastName, companyName);

        Page<DonorDTO> donorsByCriteria = null;
        HttpStatus status;
        try {
            donorsByCriteria = donorService.getDonorsByCriteria(page, size, typeDonor, firstName, lastName, companyName ,anonymeDonor, searchByDonorType, searchByNom, lastNameAr, searchByPrenom, searchByPhoneNum, searchByEmail, searchByStatus, searchByTagId);
            status = HttpStatus.OK;
            log.info("End resource : findDonorsByCriteria. Retrieved {} donors , OK ", donorsByCriteria.getContent().size());
        } catch (Exception e) {
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            log.error("End resource : findDonorsByCriteria. Ko: {}", e.getMessage());
        }

        return new ResponseEntity<>(donorsByCriteria, status);
    }

    @PostMapping("/login/donorMoral")
    public ResponseEntity<?> loginDonorMoral(@RequestBody LoginDTO loginDTO) {

        logUserInfo("loginDonorPhysique", loginDTO.getEmail());

        DonorMoralMobileDTO donorMoralMobileDTO = null;
        HttpStatus status;
        try {
            donorMoralMobileDTO = donorMobileService.loginDonorMoral(loginDTO);

            // Generate JWT tokens
            String accessToken = jwtUtils.generateToken(loginDTO.getEmail());
            String refreshToken = jwtUtils.generateRefreshToken(loginDTO.getEmail());

            // Create response with donor data and tokens
            DonorAuthResponseDTO<DonorMoralMobileDTO> response = DonorAuthResponseDTO.<DonorMoralMobileDTO>builder()
                    .donor(donorMoralMobileDTO)
                    .accessToken(accessToken)
                    .refreshToken(refreshToken)
                    .build();

            status = HttpStatus.OK;
            log.info("End resource loginDonorPhysique : {}. Retrieved donor: {}, OK", loginDTO.getEmail(), donorMoralMobileDTO);
            return new ResponseEntity<>(response, status);
        } catch (Exception e) {
            status = HttpStatus.NOT_FOUND;
            log.error("End resource loginDonorPhysique : {}. KO: {}", loginDTO.getEmail(), e.getMessage());
            return new ResponseEntity<>(null, status);
        }
    }

    @GetMapping("/donor-physique/{idDonor}")
    public ResponseEntity<DonorPhysiqueMobileDTO> getDonorPhysiqueByID(@PathVariable Long idDonor) {

        logUserInfo("getDonorByID", String.valueOf(idDonor));

        DonorPhysiqueMobileDTO donorPhysiqueMobileDTO = null;
        HttpStatus status;
        try {
            donorPhysiqueMobileDTO = donorMobileService.getDonorPhysiqueById(idDonor);
            status = HttpStatus.OK;
            log.info("End resource getDonorByID : {}. Retrieved donor: {}, OK", idDonor, donorPhysiqueMobileDTO);
        } catch (Exception e) {
            status = HttpStatus.NOT_FOUND;
            log.error("End resource getDonorByID : {}. KO: {}", idDonor, e.getMessage());
        }

        return new ResponseEntity<>(donorPhysiqueMobileDTO, status);
    }


    @GetMapping("/donor-moral/{idDonor}")
    public ResponseEntity<DonorMoralMobileDTO> getDonorMoralByID(@PathVariable Long idDonor) {

        logUserInfo("getDonorByID", String.valueOf(idDonor));

        DonorMoralMobileDTO donorMoralMobileDTO = null;
        HttpStatus status;
        try {
            donorMoralMobileDTO = donorMobileService.getDonorMoralById(idDonor);
            status = HttpStatus.OK;
            log.info("End resource getDonorByID : {}. Retrieved donor: {}, OK", idDonor, donorMoralMobileDTO);
        } catch (Exception e) {
            status = HttpStatus.NOT_FOUND;
            log.error("End resource getDonorByID : {}. KO: {}", idDonor, e.getMessage());
        }

        return new ResponseEntity<>(donorMoralMobileDTO, status);
    }

    @PutMapping("/donor-physique")
    public ResponseEntity<?> updateDonorPhysique(@RequestBody DonorPhysicalDTO physicalDonorDto) {
        if (physicalDonorDto.getId() == null) {
            return ResponseEntity.badRequest().body("Donor ID is required for update");
        }
        try {
            DonorPhysicalDTO donorDTO = donorMobileService.updateDonorPhysique(physicalDonorDto);
            DonorPhysiqueMobileDTO donorPhysiqueMobileDTO = donorMobileService.getDonorPhysiqueById(donorDTO.getId());
            log.info("End resource updateDonorPhysique : {}. Updated donor: {}, OK", physicalDonorDto.getId(), donorPhysiqueMobileDTO);
            return ResponseEntity.ok(donorPhysiqueMobileDTO);
        } catch (TechnicalException e) {
            log.error("Technical error in updateDonorPhysique : {}. Error: {}", physicalDonorDto.getId(), e.getMessage());
            return ResponseEntity.badRequest().body(e.getMessage());
        } catch (Exception e) {
            log.error("Unexpected error in updateDonorPhysique : {}. Error: {}", physicalDonorDto.getId(), e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("An unexpected error occurred");
        }
    }

    @PutMapping("/donor-moral")
    public ResponseEntity<?> updateDonorMoral(@RequestBody DonorMoralDTO moralDonorDto) {
        if (moralDonorDto.getId() == null) {
            return ResponseEntity.badRequest().body("Donor ID is required for update");
        }
        try {
            DonorMoralDTO donorDTO = donorMobileService.updateDonorMoral(moralDonorDto);
            DonorMoralMobileDTO donorMoralMobileDTO = donorMobileService.getDonorMoralById(donorDTO.getId());
            log.info("End resource updateDonorMoral : {}. Updated donor: {}, OK", moralDonorDto.getId(), donorMoralMobileDTO);
            return ResponseEntity.ok(donorMoralMobileDTO);
        } catch (TechnicalException e) {
            log.error("Technical error in updateDonorMoral : {}. Error: {}", moralDonorDto.getId(), e.getMessage());
            return ResponseEntity.badRequest().body(e.getMessage());
        } catch (Exception e) {
            log.error("Unexpected error in updateDonorMoral : {}. Error: {}", moralDonorDto.getId(), e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("An unexpected error occurred");
        }
    }

    @PutMapping("/donor-contact")
    public ResponseEntity<?> updateDonorContact(@RequestBody DonorContactDTO contactDto) {
        if (contactDto.getId() == null) {
            return ResponseEntity.badRequest().body("Contact ID is required for update");
        }
        try {
            DonorContactDTO updatedContact = donorMobileService.updateDonorContact(contactDto);
            log.info("End resource updateDonorContact : {}. Updated contact: {}, OK", contactDto.getId(), updatedContact);
            return ResponseEntity.ok(updatedContact);
        } catch (TechnicalException e) {
            log.error("Technical error in updateDonorContact : {}. Error: {}", contactDto.getId(), e.getMessage());
            return ResponseEntity.badRequest().body(e.getMessage());
        } catch (Exception e) {
            log.error("Unexpected error in updateDonorContact : {}. Error: {}", contactDto.getId(), e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("An unexpected error occurred");
        }
    }

    @PostMapping("/refresh")
    public ResponseEntity<?> refreshToken(@RequestBody RefreshTokenRequestDTO refreshTokenRequest) {
        logUserInfo("refreshToken", "Attempting to refresh token");

        String refreshToken = refreshTokenRequest.getRefreshToken();

        // Check if refresh token is provided
        if (refreshToken == null || refreshToken.isEmpty()) {
            log.error("End resource refreshToken. KO: Refresh token is missing");
            return ResponseEntity.badRequest().body(
                RefreshTokenResponseDTO.builder()
                    .message("Refresh token is required")
                    .build()
            );
        }

        try {
            // Validate refresh token
            if (!jwtUtils.isTokenValid(refreshToken)) {
                log.error("End resource refreshToken. KO: Invalid or expired refresh token");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(
                    RefreshTokenResponseDTO.builder()
                        .message("Invalid or expired refresh token")
                        .build()
                );
            }

            // Extract email from refresh token
            String email = jwtUtils.extractEmail(refreshToken);

            // Generate new access token
            String newAccessToken = jwtUtils.generateToken(email);

            // Create response with new access token
            RefreshTokenResponseDTO response = RefreshTokenResponseDTO.builder()
                .accessToken(newAccessToken)
                .message("Access token refreshed successfully")
                .build();

            log.info("End resource refreshToken. Access token refreshed successfully for user: {}", email);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("End resource refreshToken. KO: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
                RefreshTokenResponseDTO.builder()
                    .message("An error occurred while refreshing the token: " + e.getMessage())
                    .build()
            );
        }
    }

    @PostMapping("/reset-password")
    public ResponseEntity<?> resetPassword(@RequestBody ResetPasswordDTO resetPasswordDTO) {
        logUserInfo("resetPassword", resetPasswordDTO.getEmail());

        // Validate input
        if (resetPasswordDTO.getEmail() == null || resetPasswordDTO.getEmail().isEmpty() ||
            resetPasswordDTO.getPassword() == null || resetPasswordDTO.getPassword().isEmpty() ||
            resetPasswordDTO.getNewPassword() == null || resetPasswordDTO.getNewPassword().isEmpty()) {
            log.error("End resource resetPassword : {}. KO: Missing required fields", resetPasswordDTO.getEmail());
            return ResponseEntity.badRequest().body("Email, current password, and new password are required");
        }

        try {
            // Call service to reset password
            boolean success = donorMobileService.resetPassword(resetPasswordDTO);

            if (success) {
                log.info("End resource resetPassword : {}. Password reset successful, OK", resetPasswordDTO.getEmail());
                return ResponseEntity.ok("Password reset successful");
            } else {
                log.error("End resource resetPassword : {}. KO: Invalid email or password", resetPasswordDTO.getEmail());
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid email or password");
            }
        } catch (TechnicalException e) {
            log.error("Technical error in resetPassword : {}. Error: {}", resetPasswordDTO.getEmail(), e.getMessage());
            return ResponseEntity.badRequest().body(e.getMessage());
        } catch (Exception e) {
            log.error("Unexpected error in resetPassword : {}. Error: {}", resetPasswordDTO.getEmail(), e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("An unexpected error occurred");
        }
    }

    @PostMapping("/login/send-verification-code")
    public ResponseEntity<EmailVerificationResponseDTO> sendVerificationCode(@Valid @RequestBody EmailVerificationRequestDTO request) {
        logUserInfo("sendVerificationCode", request.getEmail());

        try {
            // Generate and send verification code
            emailVerificationService.generateAndSendVerificationCode(request.getEmail());

            EmailVerificationResponseDTO response = EmailVerificationResponseDTO.builder()
                    .success(true)
                    .message("Verification code sent successfully to " + request.getEmail())
                    .email(request.getEmail())
                    .build();

            log.info("End resource sendVerificationCode : {}. Verification code sent successfully, OK", request.getEmail());
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("End resource sendVerificationCode : {}. KO: {}", request.getEmail(), e.getMessage());

            EmailVerificationResponseDTO response = EmailVerificationResponseDTO.builder()
                    .success(false)
                    .message("Failed to send verification code: " + e.getMessage())
                    .email(request.getEmail())
                    .build();

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @PostMapping("/login/validate-verification-code")
    public ResponseEntity<CodeValidationResponseDTO> validateVerificationCode(@Valid @RequestBody CodeValidationRequestDTO request) {
        logUserInfo("validateVerificationCode", request.getEmail());

        try {
            // Validate the verification code
            boolean isValid = emailVerificationService.validateVerificationCode(request.getEmail(), request.getCode());

            CodeValidationResponseDTO response = CodeValidationResponseDTO.builder()
                    .valid(isValid)
                    .message(isValid ? "Verification code is valid" : "Invalid or expired verification code")
                    .email(request.getEmail())
                    .build();

            HttpStatus status = isValid ? HttpStatus.OK : HttpStatus.BAD_REQUEST;

            log.info("End resource validateVerificationCode : {}. Code validation result: {}, {}",
                    request.getEmail(), isValid, isValid ? "OK" : "KO");

            return ResponseEntity.status(status).body(response);

        } catch (Exception e) {
            log.error("End resource validateVerificationCode : {}. KO: {}", request.getEmail(), e.getMessage());

            CodeValidationResponseDTO response = CodeValidationResponseDTO.builder()
                    .valid(false)
                    .message("An error occurred while validating the code: " + e.getMessage())
                    .email(request.getEmail())
                    .build();

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
}
