package ma.almobadara.backend.repository.beneficiary;

import ma.almobadara.backend.model.beneficiary.BeneficiaryAllergy;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface BeneficiaryAllergyRepository extends JpaRepository<BeneficiaryAllergy, Long> {
    List<BeneficiaryAllergy> findByBeneficiaryId(Long id);

}