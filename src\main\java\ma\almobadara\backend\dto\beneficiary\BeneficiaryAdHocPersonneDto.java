package ma.almobadara.backend.dto.beneficiary;

import lombok.*;
import ma.almobadara.backend.model.aideComplemenatire.AideComplementaire;

import java.time.Instant;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Builder
public class BeneficiaryAdHocPersonneDto {

    private Long id;
    private String code;
    private String firstName;
    private String lastName;
    private String firstNameAr;
    private String lastNameAr;
    private String statusBeneficiaryAdHoc;
    private String identityCode;
    private Long typeIdentityId;
    private String comment;
    private List<AideComplementaire> aideComplementaires;
    private Long personId;
    private Instant createdAt;
    private List<Long> typePriseEnChargeIds;

}
