package ma.almobadara.backend.repository.communs;

import ma.almobadara.backend.model.administration.CacheAdUser;
import ma.almobadara.backend.model.communs.Action;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.query.Param;

@Repository
public interface ActionRepository extends JpaRepository<Action, Long> {

    List<Action> findByCreatedByOrAffectedTo(CacheAdUser createdBy, CacheAdUser affectedTo);

    @Query("SELECT a FROM Action a WHERE a.status IN :statuses")
    List<Action> findActionByStatus(@Param("statuses") List<Integer> statuses, Pageable pageable);

}
