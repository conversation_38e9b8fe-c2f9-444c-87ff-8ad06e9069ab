package ma.almobadara.backend.dto.donor;

import lombok.*;
import ma.almobadara.backend.dto.referentiel.CanalDonationDTO;
import ma.almobadara.backend.dto.service.ServicesDTO;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;


@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class ReleveDonorFilterDto {

    private Double montantEntree;
    private Date receptionDate;
    private LocalDate dateExecution;
    private String type;
    private CanalDonationDTO canalDonation;
    private String typeDonationKafalat;
    private Double montantSortie;
    private ServicesDTO services;
}
