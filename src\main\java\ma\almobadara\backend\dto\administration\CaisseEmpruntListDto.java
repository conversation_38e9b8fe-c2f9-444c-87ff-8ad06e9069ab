package ma.almobadara.backend.dto.administration;

import lombok.*;

import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CaisseEmpruntListDto {
    private Long id;
    private Long donorId;
    private String donorName;
    private Double globalAmount;
    private LocalDateTime lastDateEmprunt;
    private LocalDateTime lastDateRemboursement;
    private String status;
    private LocalDateTime creationDate;
    private LocalDateTime updateDate;

    // Summary information
    private Integer totalHistoryRecords;
    private Double totalEmpruntAmount;
    private Double totalRemboursementAmount;
    private Double remainingAmount;
}
