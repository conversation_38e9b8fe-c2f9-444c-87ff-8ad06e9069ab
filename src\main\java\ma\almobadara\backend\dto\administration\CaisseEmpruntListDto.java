package ma.almobadara.backend.dto.administration;

import lombok.*;

import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CaisseEmpruntListDto {
    private Long id;
    private Double amount;
    private Long donorId;
    private String donorName;
    private Long serviceId;
    private String serviceName;
    private String status;
    private LocalDateTime dateEmprunt;
    private LocalDateTime dateRemboursement;
    private LocalDateTime creationDate;
    private LocalDateTime updateDate;
}
