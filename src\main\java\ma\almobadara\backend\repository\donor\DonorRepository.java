package ma.almobadara.backend.repository.donor;

import ma.almobadara.backend.dto.dashboard.BeneficiaryLevel;
import ma.almobadara.backend.dto.dashboard.DonorActive;
import ma.almobadara.backend.dto.dashboard.NombreDonateurByMonth;
import ma.almobadara.backend.model.donor.Donor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface DonorRepository extends JpaRepository<Donor, Long> , JpaSpecificationExecutor<Donor> {

    Optional<Donor> findByCodeComptabilite(String codeComptabilite);

    Page<Donor> findByDeletedAtIsNull (Pageable pageable);
    @Query("SELECT count(d) FROM Donor d WHERE d.deletedAt IS NULL")
    long countByDeletedAtIsNull();

    @Query(value = """
    SELECT
        TO_CHAR(DATE_TRUNC('month', created_at), 'YYYY-MM') AS month,
        COUNT(*) AS count
    FROM donor
    WHERE created_at >= CURRENT_DATE - INTERVAL '12 months'
      AND deleted_at IS NULL
    GROUP BY DATE_TRUNC('month', created_at)
    ORDER BY DATE_TRUNC('month', created_at) ASC
    """, nativeQuery = true)
    List<Object[]> countDonorsByMonthLastYear();



    @Query("SELECT COUNT(d), d.cityId FROM Donor d WHERE d.deletedAt IS NULL AND d.cityId IS NOT NULL GROUP BY d.cityId ORDER BY COUNT(d) desc")
    List<Object[]> countDonorsGroupedByCity();

    long countByDonorStatusIdAndDeletedAtIsNull(Long donorStatusId);

    @Query(value = "SELECT d FROM DonorPhysical d WHERE TYPE(d) = DonorPhysical AND (" +
            // Case 1: Exact match with firstName and lastName
            "(LOWER(d.firstName) LIKE LOWER(CONCAT('%', :firstName, '%')) AND LOWER(d.lastName) LIKE LOWER(CONCAT('%', :lastName, '%'))) " +
            // Case 2: Reversed match with lastName and firstName
            "OR (LOWER(d.firstName) LIKE LOWER(CONCAT('%', :lastName, '%')) AND LOWER(d.lastName) LIKE LOWER(CONCAT('%', :firstName, '%'))) " +
            // Case 3: Both input values match firstName
            "OR (LOWER(d.firstName) LIKE LOWER(CONCAT('%', :firstName, '%')) AND LOWER(d.firstName) LIKE LOWER(CONCAT('%', :lastName, '%'))) " +
            // Case 4: Both input values match lastName
            "OR (LOWER(d.lastName) LIKE LOWER(CONCAT('%', :firstName, '%')) AND LOWER(d.lastName) LIKE LOWER(CONCAT('%', :lastName, '%'))) " +
            ")")
    Page<Donor> searchDonorByFirstNameAndLastName(@Param("firstName") String firstName, @Param("lastName") String lastName, Pageable pageable);

    @Query("SELECT d FROM DonorMoral d WHERE TYPE(d) = DonorMoral and d.deletedAt IS NULL")
    Page<Donor> findByDonorMoral(Pageable pageable);

    @Query("SELECT d FROM DonorAnonyme d WHERE TYPE(d) = DonorAnonyme and d.deletedAt IS NULL")
    Page<Donor> findByDonorAnonyme(Pageable pageable);


    @Query("SELECT d FROM DonorPhysical d WHERE TYPE(d) = DonorPhysical and d.deletedAt IS NULL")
    Page<Donor> findByDonorPhysical(Pageable pageable);


    @Query(value = "SELECT dm FROM DonorMoral dm WHERE LOWER(dm.company) LIKE %:company% and dm.deletedAt IS NULL")
    Page<Donor> searchDonorByCompany(@Param("company") String company, Pageable page);

    //searchDonorByAnonyme by attirbute name
    @Query(value = "SELECT da FROM DonorAnonyme da WHERE LOWER(da.name) LIKE %:name% and da.deletedAt IS NULL")
    Page<Donor> searchDonorByAnonyme(@Param("name") String name, Pageable page);


    @Query(value = "SELECT d from Donor d where type(d) = DonorMoral  ")
    Page<Donor> findByDeletedAtIsNullAndPhysical(Pageable pageable);

    @Query(value = "SELECT d from Donor d where type(d) = DonorMoral  ")
    Page<Donor> findByDeletedAtIsNullAndMoral(Pageable pageable);

    @Query(value = "SELECT d from Donor d  where lower(treat (d As DonorMoral ).company) like lower(concat('%', :value,'%')) ")
    Page<Donor> searchDonorByNameMoral(@Param("value") String value, Pageable page);

    @Query(value = "SELECT d from Donor d  where lower(treat (d As DonorPhysical ).firstName) like lower(concat('%', :value1,'%')) and lower(treat (d As DonorPhysical ).lastName) like lower(concat('%', :value2,'%'))")
    Page<Donor> searchDonorByNamePhysical(@Param("value1") String value1, @Param("value2") String value2, Pageable page);


    @Query(value = "SELECT d.* from Donor d LEFT JOIN donor_moral m ON d.id = m.id LEFT JOIN donor_physical p ON d.id = p.id LEFT JOIN donor_contact c ON c.donor_id= m.id where 1=1 and (c.phone_number like concat('%', :value,'%') or  p.phone_number like concat('%', :value,'%')) " ,nativeQuery = true)
    Page<Donor> searchDonorByPhone(@Param("value") String value, Pageable page);


    @Query(value = "SELECT d.* from Donor d LEFT JOIN donor_moral m ON d.id = m.id LEFT JOIN donor_physical p ON d.id = p.id LEFT JOIN donor_contact c ON c.donor_id= m.id where 1=1 and (c.email like concat('%', :value,'%') or  p.email like concat('%', :value,'%')) " ,nativeQuery = true )
    Page<Donor> searchDonorByMail(@Param("value") String value, Pageable page);

    @Query(value = "SELECT d from Donor d order by d.code ASC")
    Page<Donor> orderByCodeAsc(Pageable page);

    @Query(value = "SELECT d from Donor d order by d.code DESC")
    Page<Donor> orderByCodeDesc(Pageable page);

    @Query(value = "SELECT d from Donor d  where d.code like upper(concat('%', :value,'%')) ")
    Page<Donor> SearchByCode(@Param("value") String value, Pageable page);

    @Query(value = "SELECT d from Donor d order by trim(CONCAT(COALESCE(lower(treat (d As DonorPhysical).lastName),' '),'_', COALESCE(lower(treat (d As DonorPhysical).firstName),' '),'_',COALESCE(lower(treat (d As DonorMoral).shortCompany),' '),'_',COALESCE(lower(treat (d As DonorMoral).shortCompany),' '))) ASC")
    Page<Donor> orderDonorByNameAsc( Pageable page);

    @Query(value = "SELECT d from Donor d order by trim(CONCAT(COALESCE(lower(treat (d As DonorPhysical).lastName),' '),'_', COALESCE(lower(treat (d As DonorPhysical).firstName),' '),'_',COALESCE(lower(treat (d As DonorMoral).shortCompany),' '),'_',COALESCE(lower(treat (d As DonorMoral).shortCompany),' '))) DESC")
    Page<Donor> orderDonorByNameDesc( Pageable page);

    @Query(value = "SELECT d from Donor d order by lower(treat (d As DonorPhysical).email) DESC")
    Page<Donor> orderByEmailDesc(Pageable page);

    @Query(value = "SELECT d from Donor d order by lower(treat (d As DonorPhysical).email) ASC")
    Page<Donor> orderByEmailAsc(Pageable page);

    @Query(value = "SELECT d from Donor d  order by lower(treat (d As DonorPhysical).phoneNumber) DESC ")
    Page<Donor> orderByphoneNumberDesc(Pageable page);

    @Query(value = "SELECT d from Donor d order by lower(treat (d As DonorPhysical).phoneNumber) ASC")
    Page<Donor> orderByphoneNumberAsc(Pageable page);


    @Query(value = "SELECT d from Donor d order by d.cityId ASC")
    Page<Donor> orderByCityAsc(Pageable page);
    @Query(value = "SELECT d from Donor d order by d.cityId DESC")
    Page<Donor> orderByCityDesc(Pageable page);

    @Query(value = "SELECT d from Donor d order by type(d) DESC")
    Page<Donor> orderByTypeDesc(Pageable page);

    @Query(value = "SELECT d from Donor d order by type(d) ASC")
    Page<Donor> orderByTypeAsc(Pageable page);

    @Query(value = "SELECT d from Donor d order by d.donorStatusId DESC")
    Page<Donor> orderByStatutDesc(Pageable page);

    @Query(value = "SELECT d from Donor d order by d.donorStatusId ASC")
    Page<Donor> orderByStatutAsc(Pageable page);


    @Query(value = "SELECT d from Donor d where d.donorStatusId= :value" )
    Page<Donor> SearchByStatut(@Param("value") Long value,Pageable page);

    @Query(value = "SELECT d from Donor d where d.cityId= :value")
    Page<Donor> SearchByCity(@Param("value") Long value,Pageable page);


    @Query(value = "SELECT d from Donor d where lower(treat (d As DonorPhysical).email) like lower(concat('%', :value,'%')) ")
    Page<Donor> searchAllDonorByMail(@Param("value") String value, Pageable page);

// add and deleteAt is null
    @Query("SELECT d FROM DonorPhysical d WHERE  TYPE(d) = DonorPhysical AND (LOWER(d.firstName) LIKE %:name% OR LOWER(d.lastName) LIKE %:name%) AND d.deletedAt IS NULL")
    Page<Donor> searchDonorByFirstNameOrLastName(@Param("name") String name, Pageable pageable);


    @Query(value = "SELECT \n" + "new ma.almobadara.backend.dto.dashboard.NombreDonateurByMonth(" +
            "    EXTRACT(MONTH FROM d.createdAt) AS month," +
            "    EXTRACT(YEAR FROM d.createdAt) AS year," +
            "    COUNT(CASE WHEN dph.id IS NOT NULL THEN 1 END) AS physicalDonors," +
            "    COUNT(CASE WHEN dm.id IS NOT NULL THEN 1 END))" +
            "FROM \n" +
            "    Donor d \n" +
            "LEFT JOIN\n" +
            "    DonorPhysical dph ON d.id = dph.id\n" +
            "LEFT JOIN\n" +
            "    DonorMoral dm ON d.id = dm.id\n" +
            "WHERE \n" +
            "     d.createdAt >= :maxDate\n" +
            "GROUP BY \n" +
            "    EXTRACT(month FROM d.createdAt),\n" +
            "    EXTRACT(year FROM d.createdAt)\n" +
            "ORDER BY \n" +
            "    year DESC, month DESC")
    List<NombreDonateurByMonth> getNumberDonorByMonth(LocalDateTime maxDate);


    @Query(value = "SELECT \n" + "new ma.almobadara.backend.dto.dashboard.NombreDonateurByMonth(" +
            "    EXTRACT(MONTH FROM d.createdAt) AS month," +
            "    EXTRACT(YEAR FROM d.createdAt) AS year," +
            "    COUNT(CASE WHEN dph.id IS NOT NULL THEN 1 END) AS physicalDonors," +
            "    COUNT(CASE WHEN dm.id IS NOT NULL THEN 1 END) AS moralDonors," +
            "    dph.sex AS physicalSex ," +
            "    dc.sex AS moralSex)" +
            "FROM \n" +
            "    Donor d \n" +
            "LEFT JOIN\n" +
            "    DonorPhysical dph ON d.id = dph.id\n" +
            "LEFT JOIN\n" +
            "    DonorMoral dm ON d.id = dm.id\n" +
            "LEFT JOIN\n" +
            "    DonorContact dc ON dm.id = dc.donor.id\n" +
            "WHERE \n" +
            "    d.donorStatusId = 1 \n" +
            "    AND d.createdAt >= :maxDate\n" +
            "GROUP BY \n" +
            "    EXTRACT(month FROM d.createdAt),\n" +
            "    EXTRACT(year FROM d.createdAt),\n" +
            "    dph.sex,\n" +
            "    dc.sex\n" +
            "ORDER BY \n" +
            "    year DESC, month DESC")
    List<NombreDonateurByMonth> getNumberDonorActiveByMonthAndSex(LocalDateTime maxDate);


    @Query(value = "SELECT \n" + "new ma.almobadara.backend.dto.dashboard.DonorActive(" +
            "    d.cityId AS ville," +
            "    EXTRACT(MONTH FROM d.createdAt) AS month," +
            "    EXTRACT(YEAR FROM d.createdAt) AS year," +
            "    COUNT(CASE WHEN dph.id IS NOT NULL THEN 1 END) AS donors)" +
            "FROM \n" +
            "    Donor d \n" +
            " INNER JOIN\n" +
            "    DonorPhysical dph ON d.id = dph.id\n" +
            "WHERE \n" +
            "    d.donorStatusId = 1 \n" +
            "    AND d.createdAt >= :maxDate\n" +
            "GROUP BY \n" +
            "    EXTRACT(month FROM d.createdAt),\n" +
            "    EXTRACT(year FROM d.createdAt),\n" +
            "    d.cityId\n" +
            "ORDER BY \n" +
            "    year DESC, month DESC, ville")
    List<DonorActive> getDonorPhysicalActiveByMonthAndCity(LocalDateTime maxDate);

    @Query(value = "SELECT \n" + "new ma.almobadara.backend.dto.dashboard.DonorActive(" +
            "    d.cityId AS ville," +
            "    EXTRACT(MONTH FROM d.createdAt) AS month," +
            "    EXTRACT(YEAR FROM d.createdAt) AS year," +
            "    COUNT(CASE WHEN dm.id IS NOT NULL THEN 1 END) AS donors)" +
            "FROM \n" +
            "    Donor d \n" +
            " INNER JOIN\n" +
            "    DonorMoral dm ON d.id = dm.id\n" +
            "WHERE \n" +
            "    d.donorStatusId = 1 \n" +
            "    AND d.createdAt >= :maxDate\n" +
            "GROUP BY \n" +
            "    EXTRACT(month FROM d.createdAt),\n" +
            "    EXTRACT(year FROM d.createdAt),\n" +
            "    d.cityId\n" +
            "ORDER BY \n" +
            "    year DESC, month DESC, ville")
    List<DonorActive> getDonorMoralActiveByMonthAndCity(LocalDateTime maxDate);


    @Query("SELECT " +
            "new ma.almobadara.backend.dto.dashboard.BeneficiaryLevel(" +
            "    e.schoolLevelId AS schoolLevel, " +
            "    COUNT(CASE WHEN e.succeed = true THEN 1 END) AS successfulCount, " +
            "    COUNT(CASE WHEN e.succeed = false THEN 1 END) AS failedCount) " +
            "FROM Education e " +
            "GROUP BY e.schoolLevelId")
    List<BeneficiaryLevel> getBeneficiaryLevel();

//************************************************
    @Query("SELECT COALESCE(SUM(bl.amount), 0) " +
            "FROM Donor d " +
            "JOIN d.donations don " +
            "JOIN don.budgetLines bl " +
            "WHERE d.id = :donorId ")
    Double findTotalDonatedByDonor(@Param("donorId") Long donorId);

    @Query("SELECT COALESCE(SUM(bl.amount), 0) " +
            "FROM Donor d " +
            "JOIN d.donations don " +
            "JOIN don.budgetLines bl " +
            "WHERE d.id = :donorId " +
            "AND bl.type = :type " +
            "AND bl.status = 'DISPONIBLE'")
    Double findAvailableBalanceByDonorAndType(@Param("donorId") Long donorId, @Param("type") String type);

    @Query("SELECT COALESCE(SUM(bl.amount), 0) " +
            "FROM Donor d " +
            "JOIN d.donations don " +
            "JOIN don.budgetLines bl " +
            "WHERE d.id = :donorId " +
            "AND bl.status = 'EXECUTED'")
    Double findExecutedBalanceByDonor(@Param("donorId") Long donorId);

    @Query("SELECT COALESCE(SUM(bl.amount), 0) " +
            "FROM Donor d " +
            "JOIN d.donations don " +
            "JOIN don.budgetLines bl " +
            "WHERE d.id = :donorId " +
            "AND bl.status = 'RESERVED'")
    Double findReservedBalanceByDonor(@Param("donorId") Long donorId);


    //find all disponible balance by donor
    @Query("SELECT COALESCE(SUM(bl.amount), 0) " +
            "FROM Donor d " +
            "JOIN d.donations don " +
            "JOIN don.budgetLines bl " +
            "WHERE d.id = :donorId " +
            "AND bl.status = 'DISPONIBLE'")
    Double findAvailableBalanceByDonor(@Param("donorId") Long donorId);





}
