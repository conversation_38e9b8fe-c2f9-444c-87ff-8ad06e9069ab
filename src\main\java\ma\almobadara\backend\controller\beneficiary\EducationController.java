package ma.almobadara.backend.controller.beneficiary;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.beneficiary.EducationDTO;
import ma.almobadara.backend.mapper.EducationMapper;
import ma.almobadara.backend.model.beneficiary.Beneficiary;
import ma.almobadara.backend.model.beneficiary.Education;
import ma.almobadara.backend.repository.beneficiary.BeneficiaryRepository;
import ma.almobadara.backend.service.beneficiary.EducationService;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.rest.webmvc.ResourceNotFoundException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static ma.almobadara.backend.service.administration.CacheAdUserService.logUserInfo;

@RefreshScope
@RestController
@AllArgsConstructor
@Slf4j
@RequestMapping("/educations")
public class EducationController {

    private final EducationService educationService;
    private final BeneficiaryRepository beneficiaryRepository;
    private final EducationMapper educationMapper;

    @DeleteMapping(value = "{idEducation}", headers = "Accept=application/json")
    @Operation(summary = "Delete education", description = "delete education", tags = {"SIG"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "successful operation", content = @Content(schema = @Schema()))})
    public ResponseEntity<Void> deleteEducation(@PathVariable Long idEducation) {

        logUserInfo("deleteEducation", String.valueOf(idEducation));

        HttpStatus status;
        try {
            educationService.deleteEducation(idEducation);
            status = HttpStatus.NO_CONTENT;
            log.info("End resource Delete education with ID: {}, OK", idEducation);
        } catch (ResourceNotFoundException e) {
            status = HttpStatus.NOT_FOUND;
            log.error("End resource Delete education with ID: {}, KO: {}", idEducation, e.getMessage());
        }

        return new ResponseEntity<>(status);
    }

//    @PostMapping("/{beneficiaryId}")
//    public ResponseEntity<EducationDTO> addEducationToBeneficiary(@PathVariable Long beneficiaryId, @RequestBody EducationDTO educationDTO) {
//        log.info("Start service create Education by ID : {}", beneficiaryId);
//        Beneficiary beneficiary = beneficiaryRepository.findById(beneficiaryId)
//                .orElseThrow(() -> new ResourceNotFoundException("Beneficiary not found with id: " + beneficiaryId));
//
//        Education education = educationService.addEducationToBeneficiary(beneficiary, educationDTO);
//        EducationDTO createdEducationDTO = educationMapper.educationToEducationDTO(education);
//
//        log.info("End service created Education by ID : {}", beneficiaryId);
//
//        return new ResponseEntity<>(createdEducationDTO, new HttpHeaders() , HttpStatus.OK);
//    }

    @PostMapping("/{beneficiaryId}")
    public ResponseEntity<EducationDTO> addEducationToBeneficiary(@PathVariable Long beneficiaryId, @RequestBody EducationDTO educationDTO) {

        logUserInfo("addEducationToBeneficiary", String.valueOf(beneficiaryId));

        HttpStatus status;
        try {
            Beneficiary beneficiary = beneficiaryRepository.findById(beneficiaryId)
                    .orElseThrow(() -> new ResourceNotFoundException("Beneficiary not found with id: " + beneficiaryId));

            Education education = educationService.addEducationToBeneficiary(beneficiary, educationDTO);
            EducationDTO createdEducationDTO = educationMapper.educationToEducationDTO(education);

            status = HttpStatus.OK;
            log.info("End service created Education by ID : {}, OK", beneficiaryId);
            return new ResponseEntity<>(createdEducationDTO, new HttpHeaders(), status);
        } catch (ResourceNotFoundException e) {
            status = HttpStatus.NOT_FOUND;
            log.error("End service created Education by ID : {}, KO: {}", beneficiaryId, e.getMessage());
            return new ResponseEntity<>(status);
        }
    }

//    @GetMapping("/{beneficiaryId}/list-educations")
//    public ResponseEntity<List<EducationDTO>> getEducationsByBeneficiaryId(@PathVariable Long beneficiaryId) {
//        log.info("Start resource Create Education {}", "");
//        List<EducationDTO> educations = educationService.getEducationsByBeneficiaryId(beneficiaryId);
//        log.info("End resource get Education {}", "");
//        return ResponseEntity.ok(educations);
//    }

    @GetMapping("/{beneficiaryId}/list-educations")
    public ResponseEntity<List<EducationDTO>> getEducationsByBeneficiaryId(@PathVariable Long beneficiaryId) {

        logUserInfo("getEducationsByBeneficiaryId", String.valueOf(beneficiaryId));

        List<EducationDTO> educations = educationService.getEducationsByBeneficiaryId(beneficiaryId);
        log.info("End resource Get Educations by Beneficiary ID: {}, OK", beneficiaryId);

        return ResponseEntity.ok(educations);
    }

}
