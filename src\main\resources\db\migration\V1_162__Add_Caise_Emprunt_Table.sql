CREATE TABLE caisse_emprunt (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    donor_id BIGINT NOT NULL,
    service_id BIGINT NOT NULL,
    date_emprunt TIMESTAMP,
    date_remboursement TIMESTAMP,
    status VARCHAR(50),
    creation_date TIMES<PERSON>MP DEFAULT CURRENT_TIMESTAMP,
    update_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    amount FLOAT,
    CONSTRAINT fk_donor FOREIGN KEY (donor_id) REFERENCES donor(id),
    CONSTRAINT fk_service FOREIGN KEY (service_id) REFERENCES services(id)
);
