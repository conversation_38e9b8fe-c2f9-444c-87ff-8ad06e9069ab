package ma.almobadara.backend.dto.takenInCharge;

import lombok.*;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class TakenInChargeOperationDTO {

	private Long id;

	private String code;

	private double amount;
	private boolean reserved;

	private double managementFees;

	private Date planningDate;

	private Date executionDate;

	private Date closureDate;

	private String comment;

	private TakenInChargeDonorDTO takenInChargeDonor;

	private LocalDateTime createdAt;

	private List<TakenInChargeOperationHistoriqueDTO> historiques;
	private String status;







 }
