package ma.almobadara.backend.dto.communs;

import jakarta.validation.constraints.NotNull;
import lombok.*;
import ma.almobadara.backend.dto.administration.CacheAdUserDTO;

import java.time.LocalDate;

import static ma.almobadara.backend.util.constants.GlobalConstants.USER_ID_AUTHOR_BY_NOT_FOUND;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class CommentActionDto {

    private Long id;
    private LocalDate date;
    private String content;
    private Boolean labelComment;
    @NotNull(message = USER_ID_AUTHOR_BY_NOT_FOUND)
    private CacheAdUserDTO author;

}
