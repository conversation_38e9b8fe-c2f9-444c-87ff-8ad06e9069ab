package ma.almobadara.backend.dto.administration;

import lombok.Getter;
import lombok.Setter;

import java.time.Instant;
import java.util.Date;

@Getter
@Setter
public class ZoneDetailsDTO {
    private Long id;
    private String beneficiaryCode;
    private String beneficiaryName;
    private String beneficiaryLastName;
    private String beneficiarySex;
    private String beneficiaryPhoneNumber;
    private String beneficiaryAddress;
    private String beneficiaryAddressAr;
    private Date beneficiaryBirthDate;
    protected Instant beneficiaryCreatedAt;

}
