CREATE TABLE donation_history (
                                  id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
                                  amount DOUBLE PRECISION,
                                  created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                                  updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                                  executed BOOLEAN DEFAULT FALSE,
                                  donation_id BIGINT NOT NULL,
                                  donor_id BIGINT NOT NULL,
                                  new_service_id BIGINT,
                                  old_service_id BIGINT,
                                  aide_complementaire_id BIGINT,
                                  execution_date TIMESTAMP WITHOUT TIME ZONE,

                                  CONSTRAINT fk_donation FOREIGN KEY (donation_id) REFERENCES donation(id) ON DELETE CASCADE,
                                  CONSTRAINT fk_donor FOREIGN KEY (donor_id) REFERENCES donor(id) ON DELETE CASCADE,
                                  CONSTRAINT fk_new_service FOREIGN KEY (new_service_id) REFERENCES services(id) ON DELETE SET NULL,
                                  CONSTRAINT fk_old_service FOREIGN KEY (old_service_id) REFERENCES services(id) ON DELETE SET NULL
);

ALTER TABLE budget_line DROP COLUMN description;

