package ma.almobadara.backend.service.beneficiary;

import jakarta.transaction.Transactional;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.clients.RefFeignClient;
import ma.almobadara.backend.dto.beneficiary.BeneficiaryAuditDTO;
import ma.almobadara.backend.dto.beneficiary.EducationDTO;
import ma.almobadara.backend.dto.beneficiary.HistoryBeneficiaryDTO;
import ma.almobadara.backend.dto.beneficiary.ScholarshipBeneficiaryDTO;
import ma.almobadara.backend.dto.referentiel.AllergiesDTO;
import ma.almobadara.backend.dto.referentiel.DiseaseTreatmentTypeDTO;
import ma.almobadara.backend.dto.referentiel.DiseasesDTO;
import ma.almobadara.backend.dto.referentiel.HandicapTypeDTO;
import ma.almobadara.backend.mapper.HistoryBeneficiaryMapper;
import ma.almobadara.backend.model.beneficiary.Beneficiary;
import ma.almobadara.backend.model.beneficiary.Education;
import ma.almobadara.backend.model.beneficiary.HistoryBeneficiary;
import ma.almobadara.backend.model.beneficiary.ScholarshipBeneficiary;
import ma.almobadara.backend.repository.beneficiary.BeneficiaryRepository;
import ma.almobadara.backend.repository.beneficiary.HistoryBeneficiaryRepository;
import org.springframework.stereotype.Service;

import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


@Service
@AllArgsConstructor
@Slf4j
public class HistoryBeneficiaryService {

    private HistoryBeneficiaryRepository historyBeneficiaryRepository;
    private BeneficiaryRepository beneficiaryRepository;
    private final RefFeignClient refFeignClient;

    private final HistoryBeneficiaryMapper historyBeneficiaryMapper = HistoryBeneficiaryMapper.INSTANCE;

    // Get history for a beneficiary
    public List<HistoryBeneficiaryDTO> getHistoryByBeneficiaryId(Long beneficiaryId) {
        List<HistoryBeneficiary> historyList = historyBeneficiaryRepository.findByBeneficiaryId(beneficiaryId);
        return historyList.stream()
                .map(historyBeneficiaryMapper::toDTO)
                .collect(Collectors.toList());
    }

    // clear history for a beneficiary
    @Transactional
    public void clearHistoryByBeneficiaryId(Long beneficiaryId) {
        List<HistoryBeneficiary> historyList = historyBeneficiaryRepository.findByBeneficiaryId(beneficiaryId);
        historyBeneficiaryRepository.deleteAll(historyList);
    }

    public void saveHistoryBeneficiary(String title, String description, Long beneficiaryId, String createdBy) {
        // Retrieve the beneficiary
        Beneficiary beneficiary = beneficiaryRepository.findById(beneficiaryId)
                .orElseThrow(() -> new IllegalArgumentException("Beneficiary not found"));

        // Create and save the history entry
        HistoryBeneficiary historyBeneficiary = HistoryBeneficiary.builder()
                .title(title)
                .description(description)
                .beneficiary(beneficiary)
                .createdBy(createdBy)
                .build();

        historyBeneficiaryRepository.save(historyBeneficiary);
    }


    public void saveHistoryBeneficiary(BeneficiaryAuditDTO oldBeneficiary, BeneficiaryAuditDTO newBeneficiary, String createdBy, Long beneficiaryId) {
        // Retrieve the beneficiary and user from the repositories
        Optional<Beneficiary> beneficiaryOpt = beneficiaryRepository.findById(beneficiaryId);

        // Check if the beneficiary and user exist
        if (beneficiaryOpt.isEmpty()) {
            throw new IllegalArgumentException("Beneficiary  not found");
        }
        // Retrieve the beneficiary and user
        Beneficiary beneficiary = beneficiaryOpt.get();

        if (oldBeneficiary == null) {
            // Creating a new beneficiary
            HistoryBeneficiary historyBeneficiary = HistoryBeneficiary.builder()
                    .title("Création du bénéficiaire")
                    .description("Création du bénéficiaire  : " + newBeneficiary.getPrenom() + " " + newBeneficiary.getNom())
                    .beneficiary(beneficiary)
                    .createdBy(createdBy)
                    .build();
            historyBeneficiaryRepository.save(historyBeneficiary);
        } else {
            // Compare and save changes
            compareAndSaveChange("Type Bénéficiaire", (oldBeneficiary.getIndependent() ? "Indépendant" : "Memebre de famille"), (newBeneficiary.getIndependent() ? "Indépendant" : "Memebre de famille"), beneficiary, createdBy);
            compareAndSaveChange("Prénom", oldBeneficiary.getPrenom(), newBeneficiary.getPrenom(), beneficiary, createdBy);
            compareAndSaveChange("Nom", oldBeneficiary.getNom(), newBeneficiary.getNom(), beneficiary, createdBy);
            compareAndSaveChange("Prénom Arabe", oldBeneficiary.getPrenomArabe(), newBeneficiary.getPrenomArabe(), beneficiary, createdBy);
            compareAndSaveChange("Nom Arabe", oldBeneficiary.getNomArabe(), newBeneficiary.getNomArabe(), beneficiary, createdBy);
            compareAndSaveChange("Date de Naissance", oldBeneficiary.getDateDeNaissance().toInstant().atZone(ZoneId.systemDefault()).toLocalDate(), newBeneficiary.getDateDeNaissance().toInstant().atZone(ZoneId.systemDefault()).toLocalDate(), beneficiary, createdBy);
            compareAndSaveChange("Sexe", oldBeneficiary.getSexe(), newBeneficiary.getSexe(), beneficiary, createdBy);
            compareAndSaveChange("Email", oldBeneficiary.getEmail(), newBeneficiary.getEmail(), beneficiary, createdBy);
            compareAndSaveChange("Téléphone", oldBeneficiary.getTelephone(), newBeneficiary.getTelephone(), beneficiary, createdBy);
            compareAndSaveChange("Type Identité", oldBeneficiary.getTypeIdentite(), newBeneficiary.getTypeIdentite(), beneficiary, createdBy);
            compareAndSaveChange("Num Identité", oldBeneficiary.getNumIdentite(), newBeneficiary.getNumIdentite(), beneficiary, createdBy);
            compareAndSaveChange("Adresse", oldBeneficiary.getAdresse(), newBeneficiary.getAdresse(), beneficiary, createdBy);
            compareAndSaveChange("Adresse Arabe", oldBeneficiary.getAdresseArabe(), newBeneficiary.getAdresseArabe(), beneficiary, createdBy);
            compareAndSaveChange("Année Ajout", oldBeneficiary.getAnneeAjout(), newBeneficiary.getAnneeAjout(), beneficiary, createdBy);
            compareAndSaveChange("Code Comptable", oldBeneficiary.getCodeComptable(), newBeneficiary.getCodeComptable(), beneficiary, createdBy);
            compareAndSaveChange("Profession", oldBeneficiary.getProfession(), newBeneficiary.getProfession(), beneficiary, createdBy);
            compareAndSaveChange("Type Hébergement", oldBeneficiary.getTypeHebergement(), newBeneficiary.getTypeHebergement(), beneficiary, createdBy);
            compareAndSaveChange("Catégorie", oldBeneficiary.getCategorie(), newBeneficiary.getCategorie(), beneficiary, createdBy);
            compareAndSaveChange("Centre EPS", oldBeneficiary.getCentreEps(), newBeneficiary.getCentreEps(), beneficiary, createdBy);
            compareAndSaveChange("Zone", oldBeneficiary.getZone(), newBeneficiary.getZone(), beneficiary, createdBy);
            compareAndSaveChange("Ville", oldBeneficiary.getVille(), newBeneficiary.getVille(), beneficiary, createdBy);
            compareAndSaveChange("Type Kafalat", oldBeneficiary.getTypeKafalat(), newBeneficiary.getTypeKafalat(), beneficiary, createdBy);
        }
    }


    private void compareAndSaveChange(String fieldName, Object oldValue, Object newValue, Beneficiary beneficiary, String createdBy) {
        if (oldValue == null && newValue != null) {
            // Addition detected
            String title = "Information personnelle ";
            String description = String.format("Ajout de %s: %s", fieldName, newValue);
            HistoryBeneficiary historyBeneficiary = HistoryBeneficiary.builder()
                    .title(title)
                    .description(description)
                    .beneficiary(beneficiary)
                    .createdBy(createdBy)
                    .build();
            historyBeneficiaryRepository.save(historyBeneficiary);
        } else if (oldValue != null && newValue == null) {
            // Removal detected
            String title = "Information personnelle";
            String description = String.format("Suppression de %s: %s", fieldName, oldValue);
            HistoryBeneficiary historyBeneficiary = HistoryBeneficiary.builder()
                    .title(title)
                    .description(description)
                    .beneficiary(beneficiary)
                    .createdBy(createdBy)
                    .build();
            historyBeneficiaryRepository.save(historyBeneficiary);
        } else if (oldValue != null && !oldValue.equals(newValue)) {
            // Modification detected
            String title = "Information personnelle";
            String description = String.format("Modification de %s: De %s à %s", fieldName, oldValue, newValue);
            HistoryBeneficiary historyBeneficiary = HistoryBeneficiary.builder()
                    .title(title)
                    .description(description)
                    .beneficiary(beneficiary)
                    .createdBy(createdBy)
                    .build();
            historyBeneficiaryRepository.save(historyBeneficiary);
        }
    }

    public void saveHistoryBeneficiaryHandicap(List<Long> existingIds, List<Long> updatedIds, Long beneficiaryId, String createdBy) {
        // Retrieve and validate the user

        Beneficiary beneficiary = beneficiaryRepository.findById(beneficiaryId)
                .orElseThrow(() -> new IllegalArgumentException("Beneficiary not found"));

        // Calculate the differences between existing and updated IDs
        List<Long> idsToRemove = new ArrayList<>(existingIds != null ? existingIds : Collections.emptyList());
        List<Long> idsToAdd = new ArrayList<>(updatedIds != null ? updatedIds : Collections.emptyList());
        idsToRemove.removeAll(updatedIds);
        idsToAdd.removeAll(existingIds);

        // Only proceed if there are changes
        if (!idsToRemove.isEmpty() || !idsToAdd.isEmpty()) {
            // Fetch and map handicap details for both existing and updated IDs
            List<String> removedHandicaps = idsToRemove.stream()
                    .map(refFeignClient::getMetHandicapType)
                    .map(HandicapTypeDTO::getName)
                    .toList();

            List<String> addedHandicaps = idsToAdd.stream()
                    .map(refFeignClient::getMetHandicapType)
                    .map(HandicapTypeDTO::getName)
                    .toList();

            // Determine title and description based on the operation performed
            String title;
            String description;

            if (idsToRemove.isEmpty()) {
                title = "Information sanitaire";
                description = String.format("Ajoutés types de handicaps: %s", addedHandicaps);
            } else if (idsToAdd.isEmpty()) {
                title = "Information sanitaire";
                description = String.format("Supprimés types de handicaps: %s", removedHandicaps);
            } else {
                title = "Information sanitaire";
                description = String.format("Modification des handicaps de %s à %s", removedHandicaps, addedHandicaps);
            }

            // Create and save the history entry
            HistoryBeneficiary historyBeneficiary = HistoryBeneficiary.builder()
                    .title(title)
                    .description(description)
                    .beneficiary(beneficiary)
                    .createdBy(createdBy)
                    .build();

            historyBeneficiaryRepository.save(historyBeneficiary);
        }
    }

    public void saveHistoryBeneficiaryAllergy(List<Long> existingIds, List<Long> updatedIds, Long beneficiaryId, String createdBy) {

        // Retrieve and validate the beneficiary
        Beneficiary beneficiary = beneficiaryRepository.findById(beneficiaryId)
                .orElseThrow(() -> new IllegalArgumentException("Beneficiary not found"));

        // Calculate the differences between existing and updated IDs
        List<Long> idsToRemove = new ArrayList<>(existingIds != null ? existingIds : Collections.emptyList());
        List<Long> idsToAdd = new ArrayList<>(updatedIds != null ? updatedIds : Collections.emptyList());
        idsToRemove.removeAll(updatedIds);
        idsToAdd.removeAll(existingIds);

        // Only proceed if there are changes
        if (!idsToRemove.isEmpty() || !idsToAdd.isEmpty()) {
            // Fetch and map allergy details for both existing and updated IDs
            List<String> removedAllergies = idsToRemove.stream()
                    .map(refFeignClient::getMetAllergies)
                    .map(AllergiesDTO::getName)
                    .toList();

            List<String> addedAllergies = idsToAdd.stream()
                    .map(refFeignClient::getMetAllergies)
                    .map(AllergiesDTO::getName)
                    .toList();

            // Determine title and description based on the operation performed
            String title;
            String description;

            if (idsToRemove.isEmpty()) {
                title = "Information sanitaire";
                description = String.format("Ajoutées allergies: %s", addedAllergies);
            } else if (idsToAdd.isEmpty()) {
                title = "Information sanitaire";
                description = String.format("Supprimées allergies: %s", removedAllergies);
            } else {
                title = "Information sanitaire";
                description = String.format("Modification des allergies de %s à %s", removedAllergies, addedAllergies);
            }

            // Create and save the history entry
            HistoryBeneficiary historyBeneficiary = HistoryBeneficiary.builder()
                    .title(title)
                    .description(description)
                    .beneficiary(beneficiary)
                    .createdBy(createdBy)
                    .build();

            historyBeneficiaryRepository.save(historyBeneficiary);
        }
    }

    public void saveHistoryBeneficiaryDisease(List<Long> existingIds, List<Long> updatedIds, Long beneficiaryId, String createdBy) {

        // Retrieve and validate the beneficiary
        Beneficiary beneficiary = beneficiaryRepository.findById(beneficiaryId)
                .orElseThrow(() -> new IllegalArgumentException("Beneficiary not found"));

        // Calculate differences
        List<Long> existingIdsCopy = new ArrayList<>(existingIds != null ? existingIds : Collections.emptyList());
        List<Long> updatedIdsCopy = new ArrayList<>(updatedIds != null ? updatedIds : Collections.emptyList());
        existingIdsCopy.removeAll(updatedIds);
        updatedIdsCopy.removeAll(existingIds);

        // Only proceed if there are changes
        if (!existingIdsCopy.isEmpty() || !updatedIdsCopy.isEmpty()) {
            // Fetch and map disease details
            List<String> existingDiseases = existingIdsCopy.stream()
                    .map(refFeignClient::getMetDiseases)
                    .map(DiseasesDTO::getName)
                    .toList();

            List<String> updatedDiseases = updatedIdsCopy.stream()
                    .map(refFeignClient::getMetDiseases)
                    .map(DiseasesDTO::getName)
                    .toList();

            // Determine title and description
            String title;
            String description;
            if (existingIdsCopy.isEmpty()) {
                title = "Information sanitaire";
                description = String.format("Ajoutées maladies: %s", updatedDiseases);
            } else {
                title = "Information sanitaire";
                description = String.format("Modification des maladies de %s à %s", existingDiseases, updatedDiseases);
            }

            // Create and save the history entry
            HistoryBeneficiary historyBeneficiary = HistoryBeneficiary.builder()
                    .title(title)
                    .description(description)
                    .beneficiary(beneficiary)
                    .createdBy(createdBy)
                    .build();

            historyBeneficiaryRepository.save(historyBeneficiary);
        }
    }

    public void saveHistoryBeneficiaryDiseaseTreatment(List<Long> existingIds, List<Long> updatedIds, Long beneficiaryId, String createdBy) {
        Beneficiary beneficiary = beneficiaryRepository.findById(beneficiaryId)
                .orElseThrow(() -> new IllegalArgumentException("Beneficiary not found"));

        // Calculate the differences between existing and updated IDs
        List<Long> idsToRemove = new ArrayList<>(existingIds != null ? existingIds : Collections.emptyList());
        List<Long> idsToAdd = new ArrayList<>(updatedIds != null ? updatedIds : Collections.emptyList());
        idsToRemove.removeAll(updatedIds);
        idsToAdd.removeAll(existingIds);

        // Only proceed if there are changes
        if (!idsToRemove.isEmpty() || !idsToAdd.isEmpty()) {
            // Fetch and map disease treatment details for both existing and updated IDs
            List<String> removedTreatments = idsToRemove.stream()
                    .map(refFeignClient::getMetDiseaseTreatmentType)
                    .map(DiseaseTreatmentTypeDTO::getName)
                    .toList();

            List<String> addedTreatments = idsToAdd.stream()
                    .map(refFeignClient::getMetDiseaseTreatmentType)
                    .map(DiseaseTreatmentTypeDTO::getName)
                    .toList();

            // Determine title and description based on the operation performed
            String title;
            String description;

            if (idsToRemove.isEmpty()) {
                title = "Information sanitaire";
                description = String.format("Ajout des traitements: %s", addedTreatments);
            } else if (idsToAdd.isEmpty()) {
                title = "Information sanitaire";
                description = String.format("Suppression des traitements: %s", removedTreatments);
            } else {
                title = "Information sanitaire";
                description = String.format("Modification des traitements de %s à %s", removedTreatments, addedTreatments);
            }

            // Create and save the history entry
            HistoryBeneficiary historyBeneficiary = HistoryBeneficiary.builder()
                    .title(title)
                    .description(description)
                    .beneficiary(beneficiary)
                    .createdBy(createdBy)
                    .build();

            historyBeneficiaryRepository.save(historyBeneficiary);
        }
    }

    public void saveHistoryBeneficiaryGlasses(Long beneficiaryId, Boolean useGlasses, String createdBy) {

        // Retrieve and validate the beneficiary
        Beneficiary beneficiary = beneficiaryRepository.findById(beneficiaryId)
                .orElseThrow(() -> new IllegalArgumentException("Beneficiary not found"));

        if (useGlasses != null) {
            // Determine title and description based on the presence of glasses
            String title = "Information sanitaire";
            String description = useGlasses ? "Port de lunettes" : "Pas de lunettes";

            // Create and save the history entry
            HistoryBeneficiary historyBeneficiary = HistoryBeneficiary.builder()
                    .title(title)
                    .description(description)
                    .beneficiary(beneficiary)
                    .createdBy(createdBy)
                    .build();

            historyBeneficiaryRepository.save(historyBeneficiary);
        }
    }

    public void saveEducationHistory(Education existingEducation, EducationDTO educationDTO, Beneficiary beneficiary, String createdBy) {

        if (existingEducation != null) {
            if (educationDTO != null) {
                compareAndSaveChangeEducation("Nom Etablissement", existingEducation.getSchoolName(), educationDTO.getSchoolName(), beneficiary, createdBy, false);
                compareAndSaveChangeEducation("Nom arabe Etablissement", existingEducation.getSchoolNameAr(), educationDTO.getSchoolNameAr(), beneficiary, createdBy, false);
                compareAndSaveChangeEducation("Ville", existingEducation.getCityId(), educationDTO.getCity().getId(), beneficiary, createdBy, false);
                compareAndSaveChangeEducation("Année scolaire", existingEducation.getSchoolYearId(), educationDTO.getSchoolYear().getId(), beneficiary, createdBy, false);
                compareAndSaveChangeEducation("Niveau scolaire", existingEducation.getSchoolLevelId(), educationDTO.getSchoolLevel().getId(), beneficiary, createdBy, false);
                compareAndSaveChangeEducation("Mention", existingEducation.getHonorId(), educationDTO.getHonor().getId(), beneficiary, createdBy, false);
                compareAndSaveChangeEducation("Note", existingEducation.getMark(), educationDTO.getMark(), beneficiary, createdBy, false);
            } else {
                // Handle case for removing existing education
                saveHistory("Information Scolaire", "Éducation supprimée avec le nom de l'établissement : " + existingEducation.getSchoolName(), beneficiary, createdBy);
            }
        } else if (educationDTO != null) {
            // Handle case for adding new education
            saveHistory("Information Scolaire", "Éducation ajoutée avec le nom de l'établissement : " + educationDTO.getSchoolName(), beneficiary, createdBy);
        }
    }

    public void saveScholarshipHistory(ScholarshipBeneficiary existingScholarship, ScholarshipBeneficiaryDTO scholarshipBeneficiaryDTO, Beneficiary beneficiary, String createdBy) {

        if (existingScholarship != null) {
            if (scholarshipBeneficiaryDTO != null) {
                Long newCurrencyId = (scholarshipBeneficiaryDTO.getCurrency() != null) ? scholarshipBeneficiaryDTO.getCurrency().getId() : null;
                Long oldCurrencyId = existingScholarship.getCurrencyId();

                compareAndSaveChangeEducation("Bourse", existingScholarship.getScholarshipId(), scholarshipBeneficiaryDTO.getScholarship().getId(), beneficiary, createdBy, true);
                compareAndSaveChangeEducation("Montant", existingScholarship.getAmount(), scholarshipBeneficiaryDTO.getAmount(), beneficiary, createdBy, true);
                compareAndSaveChangeEducation("Date de début",
                        (existingScholarship.getStartDate() != null) ? existingScholarship.getStartDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate() : null,
                        (scholarshipBeneficiaryDTO.getStartDate() != null) ? scholarshipBeneficiaryDTO.getStartDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate() : null,
                        beneficiary, createdBy, true);

                compareAndSaveChangeEducation("Date de fin",
                        (existingScholarship.getEndDate() != null) ? existingScholarship.getEndDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate() : null,
                        (scholarshipBeneficiaryDTO.getEndDate() != null) ? scholarshipBeneficiaryDTO.getEndDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate() : null,
                        beneficiary, createdBy, true);

                compareAndSaveChangeEducation("Durée", existingScholarship.getPeriodicity(), scholarshipBeneficiaryDTO.getPeriodicity(), beneficiary, createdBy, true);
            } else {
                // Handle case for removing existing scholarship
                saveHistory("Information Boursière", "Bourse supprimée : " + getNameById("Bourse", existingScholarship.getScholarshipId()), beneficiary, createdBy);
            }
        } else if (scholarshipBeneficiaryDTO != null) {
            // Handle case for adding new scholarship
            saveHistory("Information Boursière", "Bourse ajoutée : " + getNameById("Bourse", scholarshipBeneficiaryDTO.getScholarship().getId()), beneficiary, createdBy);
        }
    }


    // Utility method to compare old and new values and store the names
    private void compareAndSaveChangeEducation(String fieldName, Object oldValue, Object newValue, Beneficiary beneficiary, String createdBy, Boolean isScholarship) {
        if ((oldValue == null && newValue != null) || (oldValue != null && !oldValue.equals(newValue))) {
            String oldName;
            if (oldValue instanceof Long) {
                oldName = getNameById(fieldName, (Long) oldValue);
            } else {
                assert oldValue != null;
                oldName = oldValue.toString();
            }
            String newName = newValue instanceof Long ? getNameById(fieldName, (Long) newValue) : newValue.toString();
            if (isScholarship) {
                saveHistory("Information Boursière", String.format("Modification de %s: De %s à %s", fieldName, oldName, newName), beneficiary, createdBy);
            } else {
                saveHistory("Information Scolaire", String.format("Modification de %s: De %s à %s", fieldName, oldName, newName), beneficiary, createdBy);
            }
        }
    }

    private String getNameById(String fieldName, Long id) {
        return switch (fieldName) {
            case "Ville" -> id != null ? refFeignClient.getParCity(id).getName() : "Non défini";
            case "Année scolaire" -> id != null ? refFeignClient.getParSchoolYear(id).getName() : "Non défini";
            case "Niveau scolaire" -> id != null ? refFeignClient.getParSchoolLevel(id).getName() : "Non défini";
            case "Mention" -> id != null ? refFeignClient.getParHonor(id).getName() : "Non défini";
            case "Bourse" -> id != null ? refFeignClient.getMetScholarship(id).getName() : "Non défini";
            case "Monnaie" -> id != null ? refFeignClient.getParCurrency(id).getName() : "Non défini";
            default -> "Inconnu";
        };
    }

    // Utility method to save history records
    private void saveHistory(String title, String description, Beneficiary beneficiary, String createdBy) {
        HistoryBeneficiary historyBeneficiary = HistoryBeneficiary.builder()
                .title(title)
                .description(description)
                .beneficiary(beneficiary)
                .createdBy(createdBy)
                .build();
        historyBeneficiaryRepository.save(historyBeneficiary);
    }

    public void saveHistoryBeneficiaryStatus(Long newStatusId, Long beneficiaryId, String createdBy) {
        // Retrieve and validate the beneficiary
        Beneficiary beneficiary = beneficiaryRepository.findById(beneficiaryId)
                .orElseThrow(() -> new IllegalArgumentException("Beneficiary not found"));

        String detailComplement = beneficiary.getRqComplete();
        String detailRejet = beneficiary.getRqReject();

        // Generate the description based on the new status
        String description = generateStatusDescription(newStatusId);
        String deatils = generateStatusDetailsHistory(newStatusId, detailComplement, detailRejet);

        if (description != null) {
            // Save the history with the generated description
            saveHistoryBeneficiary(description, deatils, beneficiary.getId(), createdBy);
        }
    }

    private String generateStatusDescription(Long newStatusId) {
        return switch (newStatusId.intValue()) {
            case 1 -> "Création du Pré-candidat";
            case 2 -> "Pré-candidat validé par l'assistant";
            case 3 -> "Pré-candidat validé par Service Kafalat";
            case 4 -> "Demande de complément pour l'assistant";
            case 5 -> "Pré-candidat rejeté";
            case 10 -> "Candidat validé";
            case 6 -> "Bénéficiaire Actif";
            case 7 -> "Bénéficiaire rejeté";
            case 8 -> "Demande de complément pour Service Kafalat";
            case 9 -> "Bénéficiaire archivé";
            case 13 -> "Candidat actualisé";
            default -> throw new IllegalArgumentException("Unknown status ID: " + newStatusId);
        };
    }

    private String generateStatusDetailsHistory(Long newStatusId, String detailComplement, String detailRejet) {
        return switch (newStatusId.intValue()) {
            case 4, 8 -> detailComplement;
            case 5, 7, 9 -> detailRejet;
            default -> null;
        };
    }

}


