INSERT INTO Profile (id, name_profile,is_deleted)
VALUES (2, 'Assistant' , false);

-- Insert into user_profile_module_functionality table
INSERT INTO user_profile_module_functionality (profile_id, functionality, module_functionalities_key)
SELECT (SELECT id FROM Profile ORDER BY id DESC LIMIT 1), ARRAY[0,1,2,3], module_functionalities_key
FROM (VALUES
    ('BENEFICIARY'),
    ('FAMILLE'),
    ('TAKEINCHARGE')
    ) AS keys(module_functionalities_key);

INSERT INTO Profile (id, name_profile,is_deleted)
VALUES (3, 'Kafalat Service' , false);

-- Insert into user_profile_module_functionality table
INSERT INTO user_profile_module_functionality (profile_id, functionality, module_functionalities_key)
SELECT (SELECT id FROM Profile ORDER BY id DESC LIMIT 1), ARRAY[0,1,2,3], module_functionalities_key
FROM (VALUES
    ('DONOR'),
    ('BENEFICIARY'),
    ('FAMILLE'),
    ('DONATION'),
    ('TA<PERSON>INCHAR<PERSON>'),
    ('USER')
    ) AS keys(module_functionalities_key);

INSERT INTO Profile (id, name_profile,is_deleted)
VALUES (4, 'Marketing Service' , false);

-- Insert into user_profile_module_functionality table

INSERT INTO user_profile_module_functionality (profile_id, functionality, module_functionalities_key)
SELECT (SELECT id FROM Profile ORDER BY id DESC LIMIT 1), ARRAY[0,1,2,3], module_functionalities_key
FROM (VALUES
    ('DONOR'),
    ('BENEFICIARY'),
    ('FAMILLE'),
    ('DONATION'),
    ('TAKEINCHARGE'),
    ('USER')
    ) AS keys(module_functionalities_key);

