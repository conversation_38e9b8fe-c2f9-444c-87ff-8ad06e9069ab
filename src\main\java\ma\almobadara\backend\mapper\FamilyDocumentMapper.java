package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.family.DocumentFamilyDTO;
import ma.almobadara.backend.model.family.DocumentFamily;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface FamilyDocumentMapper {

	DocumentFamilyDTO familyDocumentToFamilyDocumentDTO(DocumentFamily familyDocument);

	Iterable<DocumentFamilyDTO> familyDocumentToFamilyDocumentDTO(Iterable<DocumentFamily> familyDocuments);

    DocumentFamily familyDocumentDTOToFamilyDocument(DocumentFamilyDTO familyDocumentDTO);

	Iterable<DocumentFamily> familyDocumentDTOToFamilyDocument(Iterable<DocumentFamilyDTO> familyDocumentDTOS);

}
