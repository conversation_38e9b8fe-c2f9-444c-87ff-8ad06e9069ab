package ma.almobadara.backend.dto.beneficiary;

import lombok.*;
import ma.almobadara.backend.dto.referentiel.CurrencyDTO;
import ma.almobadara.backend.dto.referentiel.ScholarshipDTO;

import java.time.Instant;
import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class ScholarshipBeneficiaryDTO {

	private Long id;

	private ScholarshipDTO scholarship;

	private Double amount;

	private int periodicity;

	private Date startDate;

	private Date endDate;

	private String comment;

	private Instant createdAt;

	private BeneficiaryDTO beneficiary;

	private CurrencyDTO currency;

	private Double valueCurrency;

}
