package ma.almobadara.backend.dto.referentiel;

import lombok.*;
import org.springframework.hateoas.RepresentationModel;

import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor

public class AccommodationTypeDTO extends RepresentationModel<ProfessionDTO> implements Serializable {

    private static final long serialVersionUID = 8246846375436100506L;

    private Long id;
    private String code;
    private String name;
    private String nameAr;
    private String nameEn;

}
