package ma.almobadara.backend.service.beneficiary;

import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.*;
import jakarta.servlet.http.HttpServletRequest;
import ma.almobadara.backend.Audit.AuditApplicationService;
import ma.almobadara.backend.clients.RefFeignClient;
import ma.almobadara.backend.controller.referentiel.RefController;
import ma.almobadara.backend.dto.CityWithRegionAndCountryDTO;
import ma.almobadara.backend.dto.beneficiary.*;
import ma.almobadara.backend.dto.getbeneficiary.GetListDTO;
import ma.almobadara.backend.dto.getbeneficiary.GetServiceDTO;
import ma.almobadara.backend.dto.referentiel.*;
import ma.almobadara.backend.enumeration.BeneficiaryStatus;
import ma.almobadara.backend.exceptions.FunctionalException;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.mapper.*;
import ma.almobadara.backend.model.administration.Zone;
import ma.almobadara.backend.model.beneficiary.*;
import ma.almobadara.backend.model.takenInCharge.TakenInChargeBeneficiary;
import ma.almobadara.backend.repository.administration.AssistantRepository;
import ma.almobadara.backend.repository.administration.CacheAdUserRepository;
import ma.almobadara.backend.repository.administration.TaggableRepository;
import ma.almobadara.backend.repository.administration.ZoneRepository;
import ma.almobadara.backend.repository.aideComplemenatire.AideComplementaireDonorBeneficiaryRepository;
import ma.almobadara.backend.repository.beneficiary.*;
import ma.almobadara.backend.repository.communs.DocumentDonorRepository;
import ma.almobadara.backend.repository.communs.DocumentRepository;
import ma.almobadara.backend.repository.family.FamilyDocumentRepository;
import ma.almobadara.backend.repository.family.FamilyMemberRepository;
import ma.almobadara.backend.service.administration.TokenImpersonationService;
import ma.almobadara.backend.service.communs.ExportService;
import ma.almobadara.backend.service.communs.MailSenderService;
import ma.almobadara.backend.service.donor.DonorService;
import ma.almobadara.backend.service.donor.MinioService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.*;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;
import static org.mockito.internal.verification.VerificationModeFactory.times;

@ExtendWith(MockitoExtension.class)
public class BeneficiaryServiceTest {

    @InjectMocks
    private BeneficiaryService beneficiaryService;

    @Mock private AssistantRepository assistantRepository;
    @Mock private HistoryBeneficiaryRepository historyBeneficiaryRepository;
    @Mock private ZoneRepository zoneRepository;
    @Mock private PersonRepository personRepository;
    @Mock private FamilyMemberRepository familyMemberRepository;
    @Mock private BeneficiaryRepository beneficiaryRepository;
    @Mock private DocumentBeneficiaryRepository documentBeneficiaryRepository;
    @Mock private FamilyDocumentRepository familyDocumentRepository;
    @Mock
    private CriteriaBuilder criteriaBuilder;

    @Mock
    private CriteriaQuery<Beneficiary> criteriaQuery;

    @Mock
    private Root<Beneficiary> root;
    @Mock private Join<Beneficiary, Person> personJoin;


    @Mock
    private TypedQuery<Beneficiary> typedQuery;



    @Mock
    private Join<Beneficiary, BeneficiaryStatut> statutJoin;

    @Mock
    private Predicate predicate1, finalPredicate;

    @Mock private BeneficiaryYearCountRepository beneficiaryYearCountRepository;
    @Mock private MinioService minioService;
    @Mock private EducationRepository educationRepository;
    @Mock private ScholarshipBeneficiaryRepository scholarshipRepository;
    @Mock private EpsResidentRepository epsResidentRepository;
    @Mock private DiseaseTreatmentRepository diseaseTreatmentRepository;
    @Mock private BeneficiaryHandicapRepository beneficiaryHandicapRepository;
    @Mock private BeneficiaryServiceRepository beneficiaryServiceRepository;
    @Mock private BeneficiaryAllergyRepository beneficiaryAllergyRepository;
    @Mock private BeneficiaryDiseaseRepository beneficiaryDiseaseRepository;
    @Mock private GlassesRepository glassesRepository;
    @Mock private RefFeignClient refFeignClient;
    @Mock private DonorService donorService;
    @Mock private RefController refController;
    @Mock private  EntityManager entityManager;
    @Mock private AuditApplicationService auditApplicationService;
    @Mock private ExportService exportService;
    @Mock private BeneficiaryStatutRepository beneficiaryStatutRepository;
    @Mock private HistoryBeneficiaryService historyBeneficiaryService;
    @Mock private MailSenderService mailSenderService;
    @Mock private TaggableRepository taggableRepository;
    @Mock private TokenImpersonationService impersonationService;

    @Mock private CacheAdUserRepository cacheAdUserRepository;
    @Mock private DocumentRepository documentRepository;
    @Mock private AideComplementaireDonorBeneficiaryRepository aideComplementaireDonorBeneficiaryRepository;

    @Mock private DocumentDonorRepository documentDonorRepository;
    @Mock
    private Path<Long> statutIdPath;

    // Initializing the mappers (without mocking)
    private final BeneficiaryMapper beneficiaryMapper = new BeneficiaryMapperImpl();
    private final EducationMapper educationMapper = new EducationMapperImpl();
    private final ScholarshipBeneficiaryMapper scholarshipMapper = new ScholarshipBeneficiaryMapperImpl();
    private final EpsResidentMapper epsResidentMapper = new EpsResidentMapperImpl();
    private final DiseaseTreatmentMapper diseaseTreatmentMapper = new DiseaseTreatmentMapperImpl();
    private final BeneficiaryHandicapMapper beneficiaryHandicapMapper = new BeneficiaryHandicapMapperImpl();
    private final BeneficiaryAllergyMapper beneficiaryAllergyMapper = new BeneficiaryAllergyMapperImpl();
    private final BeneficiaryDiseasesMapper beneficiaryDiseasesMapper = new BeneficiaryDiseasesMapperImpl();
    private final DocumentMapper documentMapper = new DocumentMapperImpl();
    private final ServicesMapper servicesMapper = new ServicesMapperImpl();

    @Mock private  HttpServletRequest httpServletRequest ;

    @BeforeEach
    void setUp(){

        MockitoAnnotations.openMocks(this);
        beneficiaryService=new BeneficiaryService(assistantRepository,historyBeneficiaryRepository,impersonationService,zoneRepository,httpServletRequest,aideComplementaireDonorBeneficiaryRepository,beneficiaryMapper,educationMapper,scholarshipMapper,personRepository,familyMemberRepository,beneficiaryRepository,documentBeneficiaryRepository,familyDocumentRepository,beneficiaryYearCountRepository,minioService,educationRepository,scholarshipRepository,epsResidentRepository,epsResidentMapper,diseaseTreatmentRepository,beneficiaryHandicapRepository,diseaseTreatmentMapper,beneficiaryHandicapMapper,beneficiaryAllergyMapper,beneficiaryDiseasesMapper,beneficiaryServiceRepository,beneficiaryAllergyRepository,beneficiaryDiseaseRepository,glassesRepository,refFeignClient,donorService,refController,entityManager,auditApplicationService,exportService,taggableRepository,beneficiaryStatutRepository,historyBeneficiaryService,mailSenderService,cacheAdUserRepository,documentMapper,documentRepository,documentDonorRepository,servicesMapper);
    }


    // Test find All Condidate

    @Test
    void BeneficiaryService_getAllCandidates_SuccessWithoutFilter1(){
        Pageable pageable = createPageable(Optional.of(1));
        Person person= Person
                .builder()
                .firstName("firstName")
                .lastName("lastName")
                .cityId(1L)
                .identityCode("identityCode")
                .categoryBeneficiaryId(1L)
                .professionId(1L)
                .sex("male")
                .typeIdentityId(1L)
                .accommodationTypeId(1L)
                .accommodationNatureId(1L)
                .educated(false)
                .build();
        Beneficiary beneficiary=Beneficiary.builder()
                .epsResidents(List.of(EpsResident.builder().epsId(1L).build()))
                .addedYear("2002")
                .independent(true)
                .code("code")
                .scholarshipBeneficiaries(List.of())
                .diseaseTreatments(List.of(DiseaseTreatment.builder().comment("comment").build()))
                .beneficiaryServices(List.of())
                .handicapped(List.of(BeneficiaryHandicap.builder().handicapTypeId(1L).build()))
                .person(person)
                .takenInChargeBeneficiaries(List.of())
                .beneficiaryStatut(new BeneficiaryStatut(BeneficiaryStatus.BENEFICIAIRE_EN_ATTENTE.getId()))
                .build();
        beneficiary.setId(1L);
        Page<Beneficiary> beneficiaryPage=new PageImpl<>
                (
                        List.of(

                                beneficiary
                        ),
                        pageable,
                        1);


        when(beneficiaryRepository.searchBeneficiaryByName("hamza","nachid",pageable)).thenReturn(beneficiaryPage);
        when(beneficiaryRepository.findById(anyLong())).thenReturn(Optional.of(beneficiary));
        when(refFeignClient.getParCity(anyLong())).thenReturn(CityDTO.builder().name("name").region(RegionDTO.builder().id(1L).name("name").build()).build());
        Page<GetListDTO> getListDTOS= beneficiaryService.getAllCandidates(Optional.of("name"),Optional.of("hamza"),Optional.of("nachid"),null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,false,null,Optional.of(1));

        assertNotNull(getListDTOS);
    }
    private Pageable createPageable(Optional<Integer> page) {
        int pageNumber = page.orElse(0);
        int pageSize = 10;
        Sort.Direction sortDirection = Sort.Direction.DESC;
        String sortBy = "modifiedAt";
        return PageRequest.of(pageNumber, pageSize, Sort.by(sortDirection, sortBy));
    }


    @Test
    void BeneficiaryService_saveHandicaps_Sucess(){
        Person person= Person
                .builder()
                .firstName("firstName")
                .lastName("lastName")
                .cityId(1L)
                .identityCode("identityCode")
                .categoryBeneficiaryId(1L)
                .professionId(1L)
                .sex("male")
                .typeIdentityId(1L)
                .accommodationTypeId(1L)
                .accommodationNatureId(1L)
                .educated(false)
                .build();
        Beneficiary beneficiary=Beneficiary.builder()
                .epsResidents(List.of(EpsResident.builder().epsId(1L).build()))
                .addedYear("2002")
                .independent(true)
                .code("code")
                .scholarshipBeneficiaries(List.of())
                .diseaseTreatments(List.of(DiseaseTreatment.builder().comment("comment").build()))
                .beneficiaryServices(List.of())
                .handicapped(List.of(BeneficiaryHandicap.builder().handicapTypeId(1L).build()))
                .person(person)
                .takenInChargeBeneficiaries(List.of())
                .beneficiaryStatut(new BeneficiaryStatut(BeneficiaryStatus.BENEFICIAIRE_EN_ATTENTE.getId()))
                .build();

        BeneficiaryHandicap beneficiaryHandicap=BeneficiaryHandicap.builder().handicapTypeId(1L).build();
        beneficiaryHandicap.setId(1L);
        beneficiary.setId(1L);

        BeneficiarySanitary beneficiarySanitary=BeneficiarySanitary
                .builder()
                .handicapped(List.of(HandicapBeneficiaryAddDto.builder().handicapTypeId(1L).id(1L).build()))
                .build();

        List<BeneficiaryHandicap> beneficiaryHandicap1=List.of(
                beneficiaryHandicap
        );

        when(beneficiaryHandicapRepository.findByBeneficiaryId(anyLong())).thenReturn(beneficiaryHandicap1);
        when(beneficiaryHandicapRepository.save(any(BeneficiaryHandicap.class))).thenReturn(beneficiaryHandicap);
        doNothing().when(beneficiaryHandicapRepository).deleteById(anyLong());

        beneficiaryService.saveHandicaps(beneficiary,beneficiarySanitary);


    }
    @Test
    void BeneficiaryService_saveAllergies_Sucess(){
        Person person= Person
                .builder()
                .firstName("firstName")
                .lastName("lastName")
                .cityId(1L)
                .identityCode("identityCode")
                .categoryBeneficiaryId(1L)
                .professionId(1L)
                .sex("male")
                .typeIdentityId(1L)
                .accommodationTypeId(1L)
                .accommodationNatureId(1L)
                .educated(false)
                .build();
        Beneficiary beneficiary=Beneficiary.builder()
                .epsResidents(List.of(EpsResident.builder().epsId(1L).build()))
                .addedYear("2002")
                .independent(true)
                .code("code")
                .scholarshipBeneficiaries(List.of())
                .diseaseTreatments(List.of(DiseaseTreatment.builder().comment("comment").build()))
                .beneficiaryServices(List.of())
                .handicapped(List.of(BeneficiaryHandicap.builder().handicapTypeId(1L).build()))
                .person(person)
                .takenInChargeBeneficiaries(List.of())
                .beneficiaryStatut(new BeneficiaryStatut(BeneficiaryStatus.BENEFICIAIRE_EN_ATTENTE.getId()))
                .build();

        BeneficiaryAllergy beneficiaryAllergy=BeneficiaryAllergy.builder().allergyId(1L).build();
        beneficiaryAllergy.setId(1L);

        beneficiary.setId(1L);

        BeneficiarySanitary beneficiarySanitary=BeneficiarySanitary
                .builder()
                .beneficiaryAllergies(List.of(AllergyBeneficiaryAddDto.builder().allergyId(1L).id(1L).build()))
                .build();

        List<BeneficiaryAllergy> beneficiaryAllergies=List.of(
                beneficiaryAllergy
        );

        when(beneficiaryAllergyRepository.findByBeneficiaryId(anyLong())).thenReturn(beneficiaryAllergies);
        when(beneficiaryAllergyRepository.save(any(BeneficiaryAllergy.class))).thenReturn(beneficiaryAllergy);

        beneficiaryService.saveAllergies(beneficiary,beneficiarySanitary);


    }


    @Test
    void BeneficiaryService_getAllCandidates_SuccessWithoutFilter(){
        Pageable pageable = createPageable(Optional.of(1));
        Person person= Person
                .builder()
                .firstName("firstName")
                .lastName("lastName")
                .cityId(1L)
                .identityCode("identityCode")
                .categoryBeneficiaryId(1L)
                .professionId(1L)
                .sex("male")
                .typeIdentityId(1L)
                .accommodationTypeId(1L)
                .accommodationNatureId(1L)
                .educated(false)
                .build();
        Beneficiary beneficiary=Beneficiary.builder()
                .epsResidents(List.of(EpsResident.builder().epsId(1L).build()))
                .addedYear("2002")
                .independent(true)
                .code("code")
                .scholarshipBeneficiaries(List.of())
                .diseaseTreatments(List.of(DiseaseTreatment.builder().comment("comment").build()))
                .beneficiaryServices(List.of())
                .handicapped(List.of(BeneficiaryHandicap.builder().handicapTypeId(1L).build()))
                .person(person)
                .takenInChargeBeneficiaries(List.of())
                .beneficiaryStatut(new BeneficiaryStatut(BeneficiaryStatus.BENEFICIAIRE_EN_ATTENTE.getId()))
                .build();
        beneficiary.setId(1L);
        Page<Beneficiary> beneficiaryPage=new PageImpl<>
                (
                        List.of(

                                beneficiary
                        ),
                        pageable,
                        1);


        when(beneficiaryRepository.findBeneficiaryByArchivedIsFalse(pageable)).thenReturn(beneficiaryPage);
        when(beneficiaryRepository.findById(anyLong())).thenReturn(Optional.of(beneficiary));
        when(refFeignClient.getParCity(anyLong())).thenReturn(CityDTO.builder().name("name").region(RegionDTO.builder().id(1L).name("name").build()).build());
        Page<GetListDTO> getListDTOS= beneficiaryService.getAllCandidates(Optional.empty(),Optional.of("hamza"),Optional.of("nachid"),null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,false,null,Optional.of(1));

        assertNotNull(getListDTOS);
        assertEquals(1,getListDTOS.stream().count());
    }

    @Test
    void BeneficiaryService_getAllCandidates_SuccessWithFilter(){
        Pageable pageable = createPageable(Optional.of(1));
        Person person= Person
                .builder()
                .firstName("firstName")
                .lastName("lastName")
                .cityId(1L)
                .identityCode("identityCode")
                .categoryBeneficiaryId(1L)
                .professionId(1L)
                .sex("male")
                .typeIdentityId(1L)
                .accommodationTypeId(1L)
                .accommodationNatureId(1L)
                .educated(false)
                .build();
        Beneficiary beneficiary=Beneficiary.builder()
                .epsResidents(List.of(EpsResident.builder().epsId(1L).build()))
                .addedYear("2002")
                .independent(true)
                .code("code")
                .scholarshipBeneficiaries(List.of())
                .diseaseTreatments(List.of(DiseaseTreatment.builder().comment("comment").build()))
                .beneficiaryServices(List.of())
                .handicapped(List.of(BeneficiaryHandicap.builder().handicapTypeId(1L).build()))
                .person(person)
                .takenInChargeBeneficiaries(List.of())
                .beneficiaryStatut(new BeneficiaryStatut(BeneficiaryStatus.BENEFICIAIRE_EN_ATTENTE.getId()))
                .build();
        beneficiary.setId(1L);
        List<Beneficiary> beneficiaryPage=
                        List.of(
                                beneficiary
                        );
        when(beneficiaryRepository.findById(anyLong())).thenReturn(Optional.of(beneficiary));
        when(refFeignClient.getParCity(anyLong())).thenReturn(CityDTO.builder().name("name").region(RegionDTO.builder().id(1L).name("name").build()).build());

        when(entityManager.getCriteriaBuilder()).thenReturn(criteriaBuilder);
        when(criteriaBuilder.createQuery(Beneficiary.class)).thenReturn(criteriaQuery);
        when(criteriaQuery.from(Beneficiary.class)).thenReturn(root);
        when(entityManager.createQuery(criteriaQuery)).thenReturn(typedQuery);

        // Stub root.get() behavior
        when(root.get("archived")).thenReturn(mock(Path.class));
        when(criteriaBuilder.isFalse(any())).thenReturn(predicate1);

        // Mock joining behavior
        when(root.join("person")).then(invocationOnMock -> personJoin);

        Join<Beneficiary, BeneficiaryStatut> mockedStatutJoin = (Join<Beneficiary, BeneficiaryStatut>) mock(Join.class);
        when(mockedStatutJoin.get("id")).then(invocationOnMock -> statutIdPath);
        when(root.get("beneficiaryStatut")).then(invocationOnMock -> mockedStatutJoin); // 👈 Ensure it's not null
        when(typedQuery.getResultList()).thenReturn(beneficiaryPage);

        // Stub field access in joins
        when(personJoin.get("lastName")).thenReturn(mock(Path.class));

        // Stub predicates
        when(criteriaBuilder.and(any(Predicate[].class))).thenReturn(finalPredicate);
        Page<GetListDTO> getListDTOS= beneficiaryService.getAllCandidates(Optional.empty(),Optional.of("hamza"),Optional.of("nachid"),"hamza",null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,false,null,Optional.of(1));

        assertNotNull(getListDTOS);
        assertEquals(1,getListDTOS.stream().count());
    }
    // Test Add Beneficiary
    @Test
    void BeneficiaryService_addBeneficiary_SuccessWithoutAllImportantField() throws TechnicalException {
        BeneficiaryAddDTO beneficiaryAddDTO = BeneficiaryAddDTO
                .builder()
                .code("code")
                .independent(false)
                .zoneId(1L)
                .personId(1L)
                .firstName("firstName")
                .addedYear("2002")
                .lastName("lastName")
                .email("email")
                .address("address")
                .identityCode("identityCode")
                .typePriseEnChargeIds(Arrays.asList(1L, 2L, 3L))
                .oldBeneficiary(false)
                .build();

        Zone zone=Zone.builder().id(1L).name("zone").code("code").build();
        Person person= Person
                .builder()
                .firstName("firstName")
                .lastName("lastName")
                .pictureUrl("pictureUrl")
                .sex("male")
                .educated(false)
                .build();
        person.setId(1L);
        Beneficiary beneficiary=Beneficiary.builder().addedYear("2002").person(person).code("code").beneficiaryStatut(BeneficiaryStatut.builder().id(1L).nameStatut("name").build()).build();
        beneficiary.setId(1L);
        when(beneficiaryRepository.count()).thenReturn(10L);
        when(zoneRepository.findById(anyLong())).thenReturn(Optional.of(zone));
        when(personRepository.save(any(Person.class))).thenReturn(person);
        when(personRepository.findById(anyLong())).thenReturn(Optional.of(person));
        when(epsResidentRepository.findByBeneficiaryId(anyLong())).thenReturn(List.of());

        when(refFeignClient.getParTypePriseEnCharge(anyLong())).thenReturn(TypePriseEnChargeDTO.builder().id(1L).build());
        when(beneficiaryRepository.save(any(Beneficiary.class))).thenReturn(beneficiary);
        AddedBeneficiaryResponse addedBeneficiaryResponse=beneficiaryService.addBeneficiary(beneficiaryAddDTO);

        assertNotNull(addedBeneficiaryResponse);
        assertEquals(1L, addedBeneficiaryResponse.getId());
        assertEquals(beneficiary.getCode(), addedBeneficiaryResponse.getCode());
        assertEquals(1L, person.getId());

        verify(beneficiaryRepository, times(1)).count();
        verify(zoneRepository, times(1)).findById(anyLong());
        verify(personRepository, times(1)).save(any(Person.class));
        verify(personRepository, times(1)).findById(anyLong());
        verify(epsResidentRepository, times(1)).findByBeneficiaryId(anyLong());
        verify(refFeignClient, times(3)).getParTypePriseEnCharge(anyLong());
        verify(beneficiaryRepository, times(1)).save(any(Beneficiary.class));

    }
    @Test
    void BeneficiaryService_addBeneficiary_CandidateAlreadyExists() throws TechnicalException{
        BeneficiaryAddDTO beneficiaryAddDTO = BeneficiaryAddDTO
                .builder()
                .code("code")
                .independent(false)
                .zoneId(1L)
                .personId(1L)
                .firstName("firstName")
                .oldBeneficiary(true)
                .codeBeneficiary("codeBeneficiary")
                .accountingCode("accountingCode")
                .addedYear("2002")
                .lastName("lastName")
                .email("email")
                .address("address")
                .identityCode("identityCode")
                .typePriseEnChargeIds(Arrays.asList(1L, 2L, 3L))
                .build();

         Person person= Person
                .builder()
                .firstName("firstName")
                .lastName("lastName")
                .pictureUrl("pictureUrl")
                .sex("male")
                .educated(false)
                .build();
        person.setId(1L);
        Beneficiary beneficiary=Beneficiary.builder().addedYear("2002")
                .oldBeneficiary(true)
                .codeBeneficiary("codeBeneficiary")
                .accountingCode("accountingCode").person(person).code("code").beneficiaryStatut(BeneficiaryStatut.builder().id(1L).nameStatut("name").build()).build();
        Beneficiary beneficiary2=Beneficiary.builder().addedYear("2002")
                .oldBeneficiary(true)
                .codeBeneficiary("codeBeneficiary")
                .accountingCode("accountingCode").person(person).code("code").beneficiaryStatut(BeneficiaryStatut.builder().id(1L).nameStatut("name").build()).build();
        beneficiary.setId(1L);
        when(beneficiaryRepository.findByCodeBeneficiary(anyString())).thenReturn(Optional.of(beneficiary2));

        assertThrows(TechnicalException.class,()->{beneficiaryService.addBeneficiary(beneficiaryAddDTO);});

    }

    @Test
    void BeneficiaryService_addBeneficiary_SuccessWithAllImportantField() throws TechnicalException {
        BeneficiaryAddDTO beneficiaryAddDTO = BeneficiaryAddDTO
                .builder()
                .code("code")
                .independent(false)
                .zoneId(1L)
                .personId(1L)
                .firstName("firstName")
                .addedYear("2002")
                .professionId(1L)
                .accommodationTypeId(1L)
                .categoryBeneficiaryId(1L)
                .typeIdentityId(1L)
                .lastName("lastName")
                .email("email")
                .address("address")
                .identityCode("identityCode")
                .typePriseEnChargeIds(Arrays.asList(1L, 2L, 3L))
                .oldBeneficiary(false)
                .build();

        Zone zone=Zone.builder().id(1L).name("zone").code("code").build();
        Person person= Person
                .builder()
                .firstName("firstName")
                .professionId(1L)
                .accommodationTypeId(1L)
                .categoryBeneficiaryId(1L)
                .typeIdentityId(1L)
                .lastName("lastName")
                .pictureUrl("pictureUrl")
                .sex("male")
                .educated(false)
                .build();
        person.setId(1L);
        Beneficiary beneficiary=Beneficiary.builder().addedYear("2002").person(person).code("code").beneficiaryStatut(BeneficiaryStatut.builder().id(1L).nameStatut("name").build()).build();
        beneficiary.setId(1L);
        when(beneficiaryRepository.count()).thenReturn(10L);
        when(zoneRepository.findById(anyLong())).thenReturn(Optional.of(zone));
        when(personRepository.save(any(Person.class))).thenReturn(person);
        when(personRepository.findById(anyLong())).thenReturn(Optional.of(person));
        when(epsResidentRepository.findByBeneficiaryId(anyLong())).thenReturn(List.of());

        when(refFeignClient.getParTypePriseEnCharge(anyLong())).thenReturn(TypePriseEnChargeDTO.builder().id(1L).build());
        when(refFeignClient.getMetProfession(anyLong())).thenReturn(ProfessionDTO.builder().name("name").build());
        when(refFeignClient.getMetAccommodationType(anyLong())).thenReturn(AccommodationTypeDTO.builder().name("name").build());
        when(refFeignClient.getMetService(anyLong())).thenReturn(ServiceDTO.builder().name("name").build());
        when(refFeignClient.getParTypeIdentity(anyLong())).thenReturn(TypeIdentityDTO.builder().name("name").build());

        when(beneficiaryRepository.save(any(Beneficiary.class))).thenReturn(beneficiary);
        AddedBeneficiaryResponse addedBeneficiaryResponse=beneficiaryService.addBeneficiary(beneficiaryAddDTO);

        assertNotNull(addedBeneficiaryResponse);
        assertEquals(1L, addedBeneficiaryResponse.getId());
        assertEquals(beneficiary.getCode(), addedBeneficiaryResponse.getCode());
        assertEquals(1L, person.getId());

        verify(beneficiaryRepository, times(1)).count();
        verify(zoneRepository, times(1)).findById(anyLong());
        verify(personRepository, times(1)).save(any(Person.class));
        verify(personRepository, times(1)).findById(anyLong());
        verify(epsResidentRepository, times(1)).findByBeneficiaryId(anyLong());
        verify(refFeignClient, times(3)).getParTypePriseEnCharge(anyLong());
        verify(refFeignClient, times(1)).getMetProfession(anyLong());
        verify(refFeignClient, times(1)).getMetAccommodationType(anyLong());
        verify(refFeignClient, times(1)).getMetService(anyLong());
        verify(refFeignClient, times(1)).getParTypeIdentity(anyLong());
        verify(beneficiaryRepository, times(1)).save(any(Beneficiary.class));

    }

    // Test Update Beneficiary

    @Test
    void BeneficiaryService_addBeneficiary_SuccessUpdate() throws TechnicalException {
        BeneficiaryAddDTO beneficiaryAddDTO = BeneficiaryAddDTO
                .builder()
                .code("code")
                .independent(false)
                .zoneId(1L)
                .personId(1L)
                .id(1L)
                .firstName("firstName")
                .addedYear("2002")
                .lastName("lastName")
                .email("email")
                .address("address")
                .identityCode("identityCode")
                .typePriseEnChargeIds(Arrays.asList(1L, 2L, 3L))
                .oldBeneficiary(false)
                .build();

        Zone zone=Zone.builder().id(1L).name("zone").code("code").build();
        Person person= Person
                .builder()
                .firstName("firstName")
                .lastName("lastName")
                .pictureUrl("pictureUrl")
                .sex("male")
                .professionId(1L)
                .accommodationTypeId(1L)
                .categoryBeneficiaryId(1L)
                .typeIdentityId(1L)
                .educated(false)
                .build();
        person.setId(1L);
        Beneficiary beneficiary=Beneficiary.builder()
                .epsResidents(List.of(EpsResident.builder().epsId(1L).build()))
                .addedYear("2002")
                .person(person)
                .code("code")
                .beneficiaryServices(List.of())
                .beneficiaryStatut(BeneficiaryStatut.builder().id(1L).nameStatut("name").build())
                .build();
        beneficiary.setId(1L);
        when(beneficiaryRepository.findById(1L)).thenReturn(Optional.of(beneficiary));
        when(personRepository.findById(anyLong())).thenReturn(Optional.of(person));
        when(epsResidentRepository.findByBeneficiaryId(anyLong())).thenReturn(List.of());

        when(refFeignClient.getMetProfession(anyLong())).thenReturn(ProfessionDTO.builder().name("name").build());
        when(refFeignClient.getMetAccommodationType(anyLong())).thenReturn(AccommodationTypeDTO.builder().name("name").build());
        when(refFeignClient.getMetService(anyLong())).thenReturn(ServiceDTO.builder().name("name").build());
        when(refFeignClient.getParTypeIdentity(anyLong())).thenReturn(TypeIdentityDTO.builder().name("name").build());
        when(refFeignClient.getMetEps(anyLong())).thenReturn(EpsDTO.builder().id(1L).name("name").build());


        when(refFeignClient.getParTypePriseEnCharge(anyLong())).thenReturn(TypePriseEnChargeDTO.builder().id(1L).build());
        when(beneficiaryRepository.save(any(Beneficiary.class))).thenReturn(beneficiary);
        AddedBeneficiaryResponse addedBeneficiaryResponse=beneficiaryService.addBeneficiary(beneficiaryAddDTO);

        assertNotNull(addedBeneficiaryResponse);
        assertEquals(1L, addedBeneficiaryResponse.getId());
        assertEquals(beneficiary.getCode(), addedBeneficiaryResponse.getCode());
        assertEquals(1L, person.getId());

        verify(personRepository, times(1)).save(any(Person.class));
        verify(personRepository, times(2)).findById(anyLong());
        verify(epsResidentRepository, times(2)).findByBeneficiaryId(anyLong());
        verify(refFeignClient, times(3)).getParTypePriseEnCharge(anyLong());
        verify(beneficiaryRepository, times(1)).save(any(Beneficiary.class));

    }
    @Test
    void BeneficiaryService_addBeneficiary_CandidateAlreadyExsitsInUpdate() {
        BeneficiaryAddDTO beneficiaryAddDTO = BeneficiaryAddDTO
                .builder()
                .code("code")
                .independent(false)
                .zoneId(1L)
                .personId(1L)
                .oldBeneficiary(false)
                .id(1L)
                .firstName("firstName")
                .addedYear("2002")
                .lastName("lastName")
                .email("email")
                .address("address")
                .identityCode("identityCode")
                .typePriseEnChargeIds(Arrays.asList(1L, 2L, 3L))
                .build();


        when(beneficiaryRepository.findById(anyLong())).thenReturn(Optional.empty());

        assertThrows(TechnicalException.class,()->{beneficiaryService.addBeneficiary(beneficiaryAddDTO);});

    }


    @Test
    void BeneficiaryService_validateUpdateCandidate_Success() throws TechnicalException, FunctionalException {
        Long id = 1L;
        Beneficiary beneficiary=Beneficiary.builder()
                .epsResidents(List.of(EpsResident.builder().epsId(1L).build()))
                .addedYear("2002")
                .code("code")
                .beneficiaryServices(List.of())
                .beneficiaryStatut(new BeneficiaryStatut(BeneficiaryStatus.CANDIDAT_A_UPDATER.getId()))
                .build();

        when(beneficiaryRepository.findById(id)).thenReturn(Optional.of(beneficiary));
        when(beneficiaryRepository.save(any(Beneficiary.class))).thenReturn(null);

        Long idBeneficiary= beneficiaryService.validateUpdateCandidate(1L);
        assertNotNull(idBeneficiary);
    }



    @Test
    void BeneficiaryService_validateUpdateCandidate_NotFound() throws TechnicalException, FunctionalException {
        Long id = 1L;

        when(beneficiaryRepository.findById(id)).thenReturn(Optional.empty());

        assertThrows(RuntimeException.class,()->{beneficiaryService.validateUpdateCandidate(1L);});
    }
    @Test
    void BeneficiaryService_rejectBeneficiary_Success() throws TechnicalException, FunctionalException {
        Long id = 1L;
        Beneficiary beneficiary=Beneficiary.builder()
                .epsResidents(List.of(EpsResident.builder().epsId(1L).build()))
                .addedYear("2002")
                .code("code")
                .beneficiaryServices(List.of())
                .beneficiaryStatut(new BeneficiaryStatut(BeneficiaryStatus.CANDIDAT_VALIDER_ASSISTANCE.getId()))
                .build();

        when(beneficiaryRepository.findById(id)).thenReturn(Optional.of(beneficiary));
        when(beneficiaryRepository.save(any(Beneficiary.class))).thenReturn(null);

        Long idBeneficiary= beneficiaryService.rejectBeneficiary(1L,"reject");
        assertNotNull(idBeneficiary);
    }



    @Test
    void BeneficiaryService_rejectBeneficiary_NotFound() throws TechnicalException, FunctionalException {
        Long id = 1L;

        when(beneficiaryRepository.findById(id)).thenReturn(Optional.empty());

        assertThrows(RuntimeException.class,()->{beneficiaryService.rejectBeneficiary(1L,"reject");});
    }

    @Test
    void BeneficiaryService_rejectBeneficiary_PriseEnCharge() throws TechnicalException, FunctionalException {
        Long id = 1L;
        Beneficiary beneficiary=Beneficiary.builder()
                .epsResidents(List.of(EpsResident.builder().epsId(1L).build()))
                .addedYear("2002")
                .code("code")
                .takenInChargeBeneficiaries(List.of(TakenInChargeBeneficiary.builder().id(1L).build()))
                .beneficiaryServices(List.of())
                .beneficiaryStatut(new BeneficiaryStatut(BeneficiaryStatus.BENEFICIAIRE_ANCIEN.getId()))
                .build();

        when(beneficiaryRepository.findById(id)).thenReturn(Optional.of(beneficiary));

        assertThrows(TechnicalException.class,()->{beneficiaryService.rejectBeneficiary(1L,"reject");});
    }

    @Test
    void BeneficiaryService_deleteBeneficary_Success() throws TechnicalException, FunctionalException {
        Long id = 1L;
        Beneficiary beneficiary=Beneficiary.builder()
                .epsResidents(List.of(EpsResident.builder().epsId(1L).build()))
                .addedYear("2002")
                .code("code")
                .beneficiaryServices(List.of())
                .takenInChargeBeneficiaries(List.of())
                .beneficiaryStatut(new BeneficiaryStatut(BeneficiaryStatus.BENEFICIAIRE_EN_ATTENTE.getId()))
                .build();
        beneficiary.setId(id);
        beneficiary.setIndependent(true);
        beneficiary.setAddedYear("2002");
        beneficiary.setAccountingCode("code");
        Person person= Person
                .builder()
                .firstName("firstName")
                .lastName("lastName")
                .pictureUrl("pictureUrl")
                .sex("male")
                .educated(false)
                .build();
        person.setId(1L);
        beneficiary.setPerson(person);


        when(beneficiaryRepository.findById(id)).thenReturn(Optional.of(beneficiary));
        when(beneficiaryRepository.save(any(Beneficiary.class))).thenReturn(null);

        beneficiaryService.deleteBeneficiary(1L,"reject");

    }

    @Test
    void BeneficiaryService_deleteBeneficary_PriseEnCharge() throws TechnicalException, FunctionalException {
        Long id = 1L;
        Beneficiary beneficiary=Beneficiary.builder()
                .epsResidents(List.of(EpsResident.builder().epsId(1L).build()))
                .addedYear("2002")
                .code("code")
                .beneficiaryServices(List.of())
                .takenInChargeBeneficiaries(List.of(TakenInChargeBeneficiary.builder().id(1L).build()))
                .beneficiaryStatut(new BeneficiaryStatut(BeneficiaryStatus.BENEFICIAIRE_EN_ATTENTE.getId()))
                .build();
        beneficiary.setId(id);
        beneficiary.setIndependent(true);
        beneficiary.setAddedYear("2002");
        beneficiary.setAccountingCode("code");
        Person person= Person
                .builder()
                .firstName("firstName")
                .lastName("lastName")
                .pictureUrl("pictureUrl")
                .sex("male")
                .educated(false)
                .build();
        person.setId(1L);
        beneficiary.setPerson(person);


        when(beneficiaryRepository.findById(id)).thenReturn(Optional.of(beneficiary));

        assertThrows(TechnicalException.class,()->{beneficiaryService.deleteBeneficiary(1L,"reject");});


    }

    @Test
    void BeneficiaryService_getBeneficiaryById_Success() throws TechnicalException, FunctionalException {
        Long id = 1L;
        Person person= Person
                .builder()
                .firstName("firstName")
                .lastName("lastName")
                .cityId(1L)
                .categoryBeneficiaryId(1L)
                .professionId(1L)
                .sex("male")
                .typeIdentityId(1L)
                .accommodationTypeId(1L)
                .accommodationNatureId(1L)
                .educated(false)
                .build();
        Beneficiary beneficiary=Beneficiary.builder()
                .epsResidents(List.of(EpsResident.builder().epsId(1L).build()))
                .addedYear("2002")
                .independent(true)
                .code("code")
                .scholarshipBeneficiaries(List.of())
                .diseaseTreatments(List.of(DiseaseTreatment.builder().comment("comment").build()))
                .beneficiaryServices(List.of())
                .handicapped(List.of(BeneficiaryHandicap.builder().handicapTypeId(1L).build()))
                .person(person)
                .takenInChargeBeneficiaries(List.of())
                .beneficiaryStatut(new BeneficiaryStatut(BeneficiaryStatus.BENEFICIAIRE_EN_ATTENTE.getId()))
                .build();
        when(beneficiaryRepository.findById(id)).thenReturn(Optional.of(beneficiary));
        when(beneficiaryStatutRepository.findById(anyLong())).thenReturn(Optional.of(BeneficiaryStatut.builder().id(1L).nameStatut("name").build()));
        when(refFeignClient.getParCity(anyLong())).thenReturn(CityDTO.builder().code("code").build());
        when(refController.getCityWithRegionAndCountry(anyLong())).thenReturn(ResponseEntity.ok(CityWithRegionAndCountryDTO.builder().id(1L).name("name").build()));
        when(refFeignClient.getParTypeIdentity(anyLong())).thenReturn(TypeIdentityDTO.builder().id(1L).name("name").build());
        when(refFeignClient.getMetProfession(anyLong())).thenReturn(ProfessionDTO.builder().id(1L).name("name").build());
        when(refFeignClient.getMetAccommodationType(anyLong())).thenReturn(AccommodationTypeDTO.builder().id(1L).name("name").build());
        when(refFeignClient.getParAccommodationNature(anyLong())).thenReturn(AccommodationNatureDTO.builder().id(1L).name("name").build());
        when(refFeignClient.getCategoryBeneficiary(anyLong())).thenReturn(CategoryBeneficiaryDTO.builder().id(1L).name("name").build());
        when(refFeignClient.getMetService(anyLong())).thenReturn(ServiceDTO.builder().id(1L).name("name").build());
        when(refFeignClient.getMetHandicapType(anyLong())).thenReturn(HandicapTypeDTO.builder().id(1L).name("name").build());

        BeneficiaryDTO beneficiaryDTO=beneficiaryService.getBeneficiaryById(id);
        assertNotNull(beneficiaryDTO);
        assertEquals("2002",beneficiaryDTO.getAddedYear());
        assertTrue(beneficiaryDTO.getIndependent());
        assertNotNull(beneficiaryDTO.getEpsResidents());
        assertNotNull(beneficiaryDTO.getDiseaseTreatments());
        assertNotNull(beneficiaryDTO.getHandicapped());
        assertNotNull(beneficiaryDTO.getPerson());
        assertNotNull(beneficiaryDTO.getStatut());
    }


}
