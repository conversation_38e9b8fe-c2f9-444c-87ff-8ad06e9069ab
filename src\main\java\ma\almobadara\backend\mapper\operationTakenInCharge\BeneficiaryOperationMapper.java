package ma.almobadara.backend.mapper.operationTakenInCharge;

import ma.almobadara.backend.dto.takenInCharge.operationTakenInCharge.BeneficiaryOperationDTO;
import ma.almobadara.backend.model.beneficiary.Beneficiary;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface BeneficiaryOperationMapper {

    @Mapping(source = "person.firstName", target = "firstName")
    @Mapping(source = "person.lastName", target = "lastName")
    @Mapping(source = "person.firstNameAr", target = "firstNameAr")
    @Mapping(source = "person.lastNameAr", target = "lastNameAr")
    BeneficiaryOperationDTO beneficiaryToBeneficiaryDTO(Beneficiary beneficiary);


}
