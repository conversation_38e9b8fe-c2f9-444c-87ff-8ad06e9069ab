package ma.almobadara.backend.controller.administration;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.administration.RoleDTO;
import ma.almobadara.backend.service.administration.RoleService;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping(value = "/roles")
public class RoleController {
    private final RoleService roleService;
    @GetMapping
    public ResponseEntity<Page<RoleDTO>> getAllRoles(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "5") int size
    ) {
        log.info("Start resource getAllRoles page: {}, size: {}", page, size);

        Page<RoleDTO> roles = roleService.getAllRoles(page, size);

        log.info("End resource getAllRoles page: {}, size: {}", page, size);
        return ResponseEntity.ok(roles);
    }

}
