package ma.almobadara.backend.model.donation;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Objects;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ActionDonationId implements Serializable {

    private Long donation;
    private Long action;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ActionDonationId that = (ActionDonationId) o;
        return Objects.equals(donation, that.donation) && Objects.equals(action, that.action);
    }

    @Override
    public int hashCode() {
        return Objects.hash(donation, action);
    }

}
