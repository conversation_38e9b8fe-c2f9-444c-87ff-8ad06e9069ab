package ma.almobadara.backend.enumeration;

import lombok.Getter;

@Getter
public enum BeneficiaryStatus {
    CAND<PERSON>AT_INITIAL(1L),
    CANDIDAT_VALIDER_ASSISTANCE(2L),
    CANDIDAT_VALIDER_KAFALAT(3L),
    CANDIDAT_A_COMPLETER_PAR_ASSISTANCE(4L),
    CANDIDAT_REJETE(5L),
    BENEFICIAIRE_ACTIF(6L),
    BENEFICIAIRE_REJETE(7L),
    CANDIDAT_A_COMPLETER_PAR_KAFALAT(8L),
    BENEFICIAIRE_ANCIEN(9L),
    BENEFICIAIRE_EN_ATTENTE(10L),
    BENEFICIARY_AD_HOC_INDIVIDUAL(11L),
    BENEFICIARY_AD_HOC_GROUP(12L),
    CANDIDAT_A_UPDATER(13L)
    ;

    private final Long id;
    BeneficiaryStatus(Long id) {
        this.id = id;
    }

}
