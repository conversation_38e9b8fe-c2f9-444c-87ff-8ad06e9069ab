package ma.almobadara.backend.repository.donor;

import jakarta.transaction.Transactional;
import ma.almobadara.backend.model.donor.DonorContactCanalCommunication;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface DonorContactCanalCommunicationRepository extends JpaRepository<DonorContactCanalCommunication, Long> {

    @Transactional
    @Modifying
    @Query(value = "DELETE FROM donor_contact_canal_communication WHERE donor_contact_id = :donorContactId", nativeQuery = true)
    void deleteAllCanalByDonorContactId(Long donorContactId);
}
