info:
  component: AlMobadara PMGMT Backend

server:
  port: 8002


springdoc:
  packagesToScan: ma.almobadara.pmgmt
  api-docs:
    path: /api-docs

management:
  endpoints:
    web:
      exposure:
        include: "*"

azure-enterprise-app-properties:
  tenant-id: 3889043b-2822-485a-84c9-5f11e45d09d5
  client-id: 0fbce458-0ae1-413b-a383-00397a51ff95
  client-secret: ****************************************
  service-root-beta: https://graph.microsoft.com/v1.0/

spring:
  datasource:
    url: ********************************************************************
    username: postgres
    password: admin
    driver-class-name: org.postgresql.Driver
  jpa:
    database: POSTGRESQL
    show-sql: true
    hibernate:
      ddl-auto: validate
      properties:
        hibernate.jdbc.time_zone: UTC

  # Email configuration for sending emails via Gmail
  mail:
    host: smtp.gmail.com
    port: 587
    username: <EMAIL> # que pour test
    password: vjqukacjtwcasvxx # we can use the environment variable to store the password ${EMAIL-PASSWORD} just to  add  in the deployment environment
    #possible to use external plugins fir security like JASYPT
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true

  security:
    oauth2:
      resourceserver:
        jwt:
          jwk-set-uri: ${oauth2.resourceserver.jwk-set-uri}
          issuer-uri: ${oauth2.resourceserver.issuer-uri}

  servlet:
    multipart:
      enabled: true
      file-size-threshold: 2KB
      maxFileSize: 10MB
      maxRequestSize: 10MB
  jackson:
    time-zone: UTC
    serialization:
      FAIL_ON_EMPTY_BEANS: false
  cache:
    type: redis
  redis:
    host: localhost
    port: 6379

  flyway:
    enabled: true
    url: ********************************************************************
    user: postgres
    password: admin
    locations: classpath:db/migration
  sql:
    init:
      platform: POSTGRESQL

oauth2:
  resourceserver:
    tenant-id: ${azure-enterprise-app-properties.tenant-id}
    client-id: ${azure-enterprise-app-properties.client-id}
    jwk-set-uri: https://login.microsoftonline.com/${oauth2.resourceserver.tenant-id}/discovery/v2.0/keys
    issuer-uri: https://sts.windows.net/${oauth2.resourceserver.tenant-id}/


minio:
  access:
    name: minioadmin
    secret: minioadmin
  default:
    folder: /
  url: http://127.0.0.1:9000
  bucket: documents
  beneficiariesFolder: beneficiary/
  familiesFolder: families/
  campagnesFolder: campagnes/
  membersFolder: members/
  donorsFolder: doners/
  donationsFolder: donations/
  takenInChargesFolder: supports/
  assistantFolder: assistants/
  profilePicture:
    folder: profil_picture
    abv: pdp
  reportsFolder: reports
  picturesFolder: pictures
  picture:
    abv: IMG


ref:
  uri: http://*************:8080/api
  username: user
  password: user

