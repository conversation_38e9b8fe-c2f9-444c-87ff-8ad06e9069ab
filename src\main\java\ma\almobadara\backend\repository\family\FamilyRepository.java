package ma.almobadara.backend.repository.family;

import ma.almobadara.backend.model.beneficiary.Beneficiary;
import ma.almobadara.backend.model.family.Family;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface FamilyRepository extends JpaRepository<Family, Long> {


    Family getFamilyById(Long id);
    @Query(value = "select * from family where id in " +
            "(select family_id from family_member where person_id in " +
            "(select id from person where id not in " +
            "(select person_id from beneficiary)))"
            , nativeQuery = true)
    List<Family> findFamiliesWithoutBeneficiaryProfile();

    @Query(value = "select * from family where id in\n" +
            "(select family_id from family_member where person_id in\n" +
            "(select person_id from beneficiary where id = :id))", nativeQuery = true)
    Family getFamilyByBeneficiaryId(@Param("id") Long id);

    @Query("select f from Family f JOIN f.familyMembers fm WHERE (lower(fm.person.firstName) like lower(concat('%', :value1,'%'))" +
            "AND lower(fm.person.lastName) like lower(concat('%', :value2,'%')))")
    Page<Family> searchFamilyByTutorName(@Param("value1") String value1, @Param("value2") String value2, Pageable page);

    Page<Family> findByFamilyMembers_TutorTrueAndFamilyMembers_Person_LastNameContains(String lastName, Pageable pageable);

    Page<Family> findByFamilyMembers_ExternalIncomes_AmountBetween(Double amountStart, Double amountEnd, Pageable pageable);

    @Query("select f from Family f WHERE DATE(f.createdAt)= DATE(:value1)")
    Page<Family> searchFamilyByCreatedAt(@Param("value1") String value1, Pageable page);


    @Query("SELECT f FROM Family f JOIN f.familyMembers fm WHERE fm.person.id = :personId")
    Family findByPersonId(@Param("personId") Long personId);


    Page<Family> findFamilyByZoneId(Pageable pageable, Long zoneId);

    Page<Family> findFamilyByZoneIdIn(Pageable pageable, List<Long> zoneIds);

    @Query("SELECT f FROM Family f WHERE f.zone.id = :zoneId")
    List<Family> findFamilyByZoneId(@Param("zoneId") Long zoneId);

    @Query("SELECT f FROM Family f WHERE f.zone.id = :zoneId")
    Family findOneFamilyByZoneId(@Param("zoneId") Long zoneId);
}
