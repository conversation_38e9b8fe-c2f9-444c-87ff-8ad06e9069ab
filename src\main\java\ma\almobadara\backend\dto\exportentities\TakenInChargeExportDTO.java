package ma.almobadara.backend.dto.exportentities;

import lombok.*;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class TakenInChargeExportDTO {
    private String code;
    private LocalDateTime createdAt;
    private String donorName;
    private String beneficiaryName;
    private String serviceName;
    private String status;
    private Date startDate;
    private Date endDate;
    private int numberOfOperations;
    private double totalAmount;
    private int numberOfPlannedOperations;
    private int numberOfExecutedOperations;
    private int numberOfClosedOperations;

    public String getFormattedStartDate() {
        if (startDate != null) {
            DateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
            return dateFormat.format(startDate);
        }
        return null;
    }


    public String getFormattedEndDate() {
        if (endDate != null) {
            DateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
            return dateFormat.format(endDate);
        }
        return null;
    }


}
