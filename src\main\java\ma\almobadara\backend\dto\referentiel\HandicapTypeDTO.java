package ma.almobadara.backend.dto.referentiel;

import lombok.*;

import java.util.Objects;
import java.io.Serializable;

@Setter
@Getter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class HandicapTypeDTO implements Serializable{

	private static final long serialVersionUID = 1L;

	private Long id;
	private String code;
	private String name;
	private String nameAr;
	private String nameEn;

	@Override
	public boolean equals(Object o) {
		if (this == o) {
			return true;
		}
		if (!(o instanceof HandicapTypeDTO handicapTypeDTO)) {
			return false;
		}

        if (this.id == null) {
			return false;
		}
		return Objects.equals(this.id, handicapTypeDTO.id);
	}
	@Override
	public int hashCode() {
		return Objects.hash(this.id);
	}

	// prettier-ignore
	@Override
	public String toString() {
		return "MetHandicapTypeDTO{" +
				"id=" + getId() +
				", name='" + getName() + "'" +
				"}";
	}

}
