package ma.almobadara.backend.repository.family;

import ma.almobadara.backend.model.family.DocumentFamily;
import ma.almobadara.backend.model.family.Family;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface FamilyDocumentRepository extends JpaRepository<DocumentFamily, Long> {


    Iterable<DocumentFamily> findByFamily(Family family);

    Optional<DocumentFamily> findByFamilyIdAndDocumentId(Long familyId, Long documentId);
    List<DocumentFamily> findByFamilyId(Long  familyId);
    List<DocumentFamily> findByDocumentId(Long documentId);
    Optional<DocumentFamily> findDocumentFamiliesByDocumentId(Long documentId);

}
