CREATE TABLE beneficiary_statut (
                                    id BIGINT NOT NULL,
                                    name_statut VARCHAR(255) NOT NULL,
                                    PRIMARY KEY (id),
                                    UNIQUE (name_statut)
);


INSERT INTO beneficiary_statut (id, name_statut) VALUES (1, 'candidat_initial');
INSERT INTO beneficiary_statut (id, name_statut) VALUES (2, 'candidat_valider_assistance');
INSERT INTO beneficiary_statut (id, name_statut) VALUES (3, 'candidat_valider_kafalat');
INSERT INTO beneficiary_statut (id, name_statut) VALUES (4, 'candidat_a_completer');
INSERT INTO beneficiary_statut (id, name_statut) VALUES (5, 'candidat_rejete');
INSERT INTO beneficiary_statut (id, name_statut) VALUES (6, 'beneficiaire');
INSERT INTO beneficiary_statut (id, name_statut) VALUES (7, 'beneficiaire_rejete');

ALTER TABLE beneficiary
    ADD COLUMN beneficiary_statut_id BIGINT;

ALTER TABLE beneficiary
    ADD CONSTRAINT FK_beneficiary_statut_id
        FOREIGN KEY (beneficiary_statut_id)
            REFERENCES beneficiary_statut(id);
