package ma.almobadara.backend.service.mobile;

import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.administration.AssistantDTO;
import ma.almobadara.backend.dto.administration.LoginDTO;
import ma.almobadara.backend.dto.beneficiary.SmallZoneDTO;
import ma.almobadara.backend.dto.mobile.AssistantMobileDTO;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.mapper.AssistantMapper;
import ma.almobadara.backend.mapper.BankCardMapper;
import ma.almobadara.backend.mapper.ZoneMapper;
import ma.almobadara.backend.model.administration.Assistant;
import ma.almobadara.backend.model.beneficiary.Beneficiary;
import ma.almobadara.backend.model.family.Family;
import ma.almobadara.backend.repository.administration.AssistantRepository;
import ma.almobadara.backend.repository.beneficiary.BeneficiaryRepository;
import ma.almobadara.backend.repository.family.FamilyRepository;
import ma.almobadara.backend.service.administration.AssistantService;
import ma.almobadara.backend.service.donor.MinioService;
import org.springframework.stereotype.Service;

import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class AssistantMobileService {

    private final AssistantRepository assistantRepository;
    private final BeneficiaryRepository beneficiaryRepository;
    private final FamilyRepository familyRepository;
    private final AssistantService assistantService;
    private final AssistantMapper assistantMapper;
    private final BankCardMapper bankCardMapper;
    private final ZoneMapper zoneMapper;
    private final MinioService minioService;

    /**
     * Get an assistant by ID with counts of beneficiaries and families in their zone
     * @param id The ID of the assistant
     * @return AssistantMobileDTO with assistant details and counts
     * @throws TechnicalException if there's an error retrieving the assistant
     */
    public AssistantMobileDTO getAssistantById(Long id) throws TechnicalException {
        log.debug("Start service getAssistantById for ID: {}", id);

        // Get the assistant
        Assistant assistant = assistantRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Assistant not found with ID: " + id));

        // Create the mobile DTO
        AssistantMobileDTO mobileDTO = convertToMobileDTO(assistant);

        // If the assistant has a zone, count beneficiaries and families in that zone
        if (assistant.getZone() != null && assistant.getZone().getId() != null) {
            Long zoneId = assistant.getZone().getId();

            // Count beneficiaries in the zone, excluding archived and old beneficiaries
            List<Beneficiary> beneficiaries = beneficiaryRepository.findByZoneAndArchivedFalseOrArchivedNullAndOldBeneficiaryFalseOrOldBeneficiaryNull(assistant.getZone());
            mobileDTO.setBeneficiaryCount(beneficiaries.size());

            // Count families in the zone
            List<Family> families = familyRepository.findFamilyByZoneId(zoneId);
            mobileDTO.setFamilyCount(families.size());
        } else {
            // No zone assigned, so no beneficiaries or families
            mobileDTO.setBeneficiaryCount(0);
            mobileDTO.setFamilyCount(0);
        }

        log.debug("End service getAssistantById for ID: {}", id);
        return mobileDTO;
    }

    /**
     * Convert Assistant entity to AssistantMobileDTO
     */
    private AssistantMobileDTO convertToMobileDTO(Assistant assistant) {
        AssistantMobileDTO dto = new AssistantMobileDTO();

        // Set basic properties
        dto.setId(assistant.getId());
        dto.setCode(assistant.getCode());
        dto.setFirstName(assistant.getFirstName());
        dto.setLastName(assistant.getLastName());
        dto.setEmail(assistant.getEmail());
        dto.setPhone(assistant.getPhone());
        dto.setPictureUrl(assistant.getPictureUrl());
        dto.setDateAffectationToZone(assistant.getDateAffectationToZone());

        // Set zone
        if (assistant.getZone() != null) {
            SmallZoneDTO smallZoneDTO= new SmallZoneDTO(assistant.getZone().getId(),
                    assistant.getZone().getCode(),
                    assistant.getZone().getName(),assistant.getZone().getNameAr());
            dto.setZone(smallZoneDTO);
        }

        // Try to load picture if URL exists
        if (assistant.getPictureUrl() != null) {
            try {
                byte[] imageData = minioService.ReadFromMinIO(assistant.getPictureUrl(), null);
                String base64Image = Base64.getEncoder().encodeToString(imageData);
                dto.setPictureBase64(base64Image);
            } catch (Exception ex) {
                log.error("Error loading image for assistant {}: {}", assistant.getId(), ex.getMessage());
            }
        }

        return dto;
    }

    /**
     * Login an assistant with email and password
     * @param loginDTO The login credentials
     * @return AssistantDTO with full assistant details
     * @throws RuntimeException if the credentials are invalid
     */
    public AssistantDTO loginAssistant(LoginDTO loginDTO) {
        log.debug("Start service loginAssistant for email: {}", loginDTO.getEmail());

        // Find assistant by email and password
        Optional<Assistant> assistantOptional = assistantRepository.findByEmailAndPassword(loginDTO.getEmail(), loginDTO.getPassword());

        if (assistantOptional.isPresent()) {
            // Get the assistant details
            Assistant assistant = assistantOptional.get();

            try {
                // Get the full assistant details from the main service
                AssistantDTO assistantDTO = assistantService.getAssistantById(assistant.getId());

                // Add beneficiary and family counts to the DTO
                if (assistant.getZone() != null && assistant.getZone().getId() != null) {
                    Long zoneId = assistant.getZone().getId();

                    // Count beneficiaries in the zone, excluding archived and old beneficiaries
                    List<Beneficiary> beneficiaries = beneficiaryRepository.findByZoneAndArchivedFalseOrArchivedNullAndOldBeneficiaryFalseOrOldBeneficiaryNull(assistant.getZone());
                    assistantDTO.setHasBeneficiaries(!beneficiaries.isEmpty());

                    // Count families in the zone
                    List<Family> families = familyRepository.findFamilyByZoneId(zoneId);

                    // Add the counts as additional information
                    Map<String, Object> additionalInfo = new HashMap<>();
                    additionalInfo.put("beneficiaryCount", beneficiaries.size());
                    additionalInfo.put("familyCount", families.size());
                    assistantDTO.setAdditionalInfo(additionalInfo);
                }

                return assistantDTO;
            } catch (TechnicalException e) {
                throw new RuntimeException("Error retrieving assistant details: " + e.getMessage());
            }
        } else {
            throw new RuntimeException("Invalid email or password");
        }
    }
}
