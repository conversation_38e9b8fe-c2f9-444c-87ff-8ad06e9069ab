package ma.almobadara.backend.service.communs;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.exportentities.ExportFileDTO;
import ma.almobadara.backend.util.times.TimeWatch;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.tomcat.util.http.fileupload.ByteArrayOutputStream;
import org.springframework.stereotype.Service;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.Date;
import java.util.List;
import java.util.function.Function;

@Service
@RequiredArgsConstructor
@Slf4j
public class ExportService {

    public <T> ExportFileDTO exportEntities(String sheetName, String[] headers, List<T> entities, Function<T, Object[]> dataMapper) {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service Export File With Name {}", sheetName);

        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet(sheetName);

        // Set a reasonable default column width
        int defaultColumnWidth = 25; // Adjust based on your font and content
        for (int i = 0; i < headers.length; i++) {
            sheet.setColumnWidth(i, defaultColumnWidth * 256); // Convert width from characters to units
        }

        // Create and style the title row
        createTitleRow(sheet, sheetName, headers.length, workbook);

        // Create headers
        createHeaderRow(sheet, headers, workbook);

        // Populate data rows
        populateDataRows(sheet, entities, dataMapper);

        // Convert workbook to byte array
        byte[] bytes = convertWorkbookToByteArray(workbook);

        // Prepare export file DTO
        ExportFileDTO exportFileDTO = prepareExportFileDTO(sheetName, bytes);

        log.debug("End service Export File With Name, took {}", watch.toMS());
        return exportFileDTO;
    }

    private void createTitleRow(Sheet sheet, String sheetName, int numColumns, Workbook workbook) {
        Row titleRow = sheet.createRow(0);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue(sheetName);
        titleCell.setCellStyle(getTitleCellStyle(workbook, numColumns));

        // Merge cells for title row
        sheet.addMergedRegion(new CellRangeAddress(
                0, // From first row
                0, // To first row (merge vertically)
                0, // From first column
                numColumns - 1 // To last column
        ));
    }

    private void createHeaderRow(Sheet sheet, String[] headers, Workbook workbook) {
        Row headerRow = sheet.createRow(1);
        CellStyle headerCellStyle = getHeaderCellStyle(workbook);

        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerCellStyle);
        }
    }

    private <T> void populateDataRows(Sheet sheet, List<T> entities, Function<T, Object[]> dataMapper) {
        int rowNum = 2; // Start after title and header rows
        for (T entity : entities) {
            Row row = sheet.createRow(rowNum++);
            Object[] rowData = dataMapper.apply(entity);
            for (int i = 0; i < rowData.length; i++) {
                Cell cell = row.createCell(i);
                cell.setCellValue(String.valueOf(rowData[i]));
                // Optionally apply cell style here
            }
        }
    }

    private byte[] convertWorkbookToByteArray(Workbook workbook) {
        byte[] bytes = null;
        try (ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            workbook.write(bos);
            bytes = bos.toByteArray();
        } catch (Exception e) {
            log.error("Error while converting Excel workbook to byte array: {}", e.getMessage());
        } finally {
            try {
                workbook.close();
            } catch (Exception e) {
                log.error("Error while closing Excel workbook: {}", e.getMessage());
            }
        }
        return bytes;
    }

    private ExportFileDTO prepareExportFileDTO(String sheetName, byte[] bytes) {
        DateFormat dateFormatter = new SimpleDateFormat("yyyy-MM-dd_HH-mm-ss");
        String currentDateTime = dateFormatter.format(new Date());
        String filenamePrefix = sheetName + "_" + currentDateTime + ".xlsx"; // Excel file extension
        String base64 = Base64.getEncoder().encodeToString(bytes);

        ExportFileDTO exportFileDTO = new ExportFileDTO();
        exportFileDTO.setFile64(base64);
        exportFileDTO.setFileName(filenamePrefix);

        return exportFileDTO;
    }

    private CellStyle getTitleCellStyle(Workbook workbook, int numColumns) {
        CellStyle titleCellStyle = workbook.createCellStyle();
        titleCellStyle.setAlignment(HorizontalAlignment.LEFT); // Title aligned to the left
        titleCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        Font titleFont = workbook.createFont();
        titleFont.setFontHeightInPoints((short) 18); // Adjusted font size for title
        titleFont.setColor(IndexedColors.BLACK.getIndex());
        titleFont.setBold(true);
        titleCellStyle.setFont(titleFont);

        return titleCellStyle;
    }

    private CellStyle getHeaderCellStyle(Workbook workbook) {
        CellStyle headerCellStyle = workbook.createCellStyle();
        headerCellStyle.setFillForegroundColor(IndexedColors.DARK_BLUE.getIndex());
        headerCellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        Font headerFont = workbook.createFont();
        headerFont.setColor(IndexedColors.WHITE.getIndex()); // Set font color to white
        headerFont.setBold(true);
        headerFont.setFontHeightInPoints((short) 12);
        headerCellStyle.setFont(headerFont);
        headerCellStyle.setAlignment(HorizontalAlignment.CENTER);
        headerCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        return headerCellStyle;
    }
}
