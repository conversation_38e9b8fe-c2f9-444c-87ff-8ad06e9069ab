package ma.almobadara.backend.model.takenInCharge;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Objects;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class NoteTakenInChargeId implements Serializable {

    private Long takenInCharge;
    private Long note;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        NoteTakenInChargeId that = (NoteTakenInChargeId) o;
        return Objects.equals(takenInCharge, that.takenInCharge) && Objects.equals(note, that.note);
    }

    @Override
    public int hashCode() {
        return Objects.hash(takenInCharge, note);
    }

}
