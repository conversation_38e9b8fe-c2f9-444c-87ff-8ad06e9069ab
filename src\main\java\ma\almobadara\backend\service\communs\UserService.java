package ma.almobadara.backend.service.communs;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.communs.UserDTO;
import ma.almobadara.backend.mapper.UserMapper;
import ma.almobadara.backend.model.communs.User;
import ma.almobadara.backend.repository.communs.UserRepository;
import ma.almobadara.backend.util.times.TimeWatch;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

@Service
@RequiredArgsConstructor
@Slf4j
public class UserService {

    private final UserRepository userRepository;
    private final UserMapper userMapper;

    public UserDTO addUserDonor(UserDTO userDTO) {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service Add User ");

        User user = userMapper.userDtoModelToModel(userDTO);
        User newUser = userRepository.save(user);
        UserDTO newUserDTO = userMapper.userModelToDto(newUser);

        log.debug("End service Add User Donor, took {}", watch.toMS());
        return newUserDTO;
    }

    public List<UserDTO> getAllUsers() {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service getAllUsers{}", "");

        Iterable<User> users = userRepository.findAll();
        Iterable<UserDTO> userDTOS = userMapper.userListModelToDto(users);
        List<UserDTO> list = StreamSupport.stream(userDTOS.spliterator(), false).collect(Collectors.toList());

        log.debug("End service getAllUsers took {}", watch.toMS());
        return list;
    }

}
