package ma.almobadara.backend.repository.administration;

import ma.almobadara.backend.model.administration.Assistant;
import ma.almobadara.backend.model.administration.AssistantZone;
import ma.almobadara.backend.model.administration.Zone;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface AssistantZoneRepository extends JpaRepository<AssistantZone,Long> {
    Boolean existsByAssistant(Assistant assistant);
    Optional<AssistantZone>  findByZone(Zone zone);
}
