package ma.almobadara.backend.Cache;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.data.redis.core.RedisTemplate;


@Service
@Slf4j
public class CachingService {

    private final RedisTemplate<String, Object> redisTemplate;

    @Autowired
    public CachingService(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }


    public void evictAllCaches() {
        log.debug("Start service evictAllCaches {}");

        redisTemplate.getConnectionFactory().getConnection().flushDb();

        log.debug("End service evictAllCaches {}");

    }






}
