package ma.almobadara.backend.repository.beneficiary;


import ma.almobadara.backend.model.beneficiary.Beneficiary;
import ma.almobadara.backend.model.beneficiary.DocumentBeneficiary;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface DocumentBeneficiaryRepository extends JpaRepository<DocumentBeneficiary, Long> {
    Iterable<DocumentBeneficiary> findByBeneficiary(Beneficiary beneficiary);

    Optional<DocumentBeneficiary> findByBeneficiaryIdAndDocumentId(Long beneficiaryId, Long documentId);
    List<DocumentBeneficiary> findByBeneficiaryId(Long  beneficiaryId  );
    List<DocumentBeneficiary> findByDocumentId(Long documentId);

    //countByBeneficiaryIdAndTypeDocumentId
    @Query("SELECT count(d) FROM DocumentBeneficiary d WHERE d.beneficiary.id = ?1 AND d.document.typeDocumentId = ?2")
    Long countByBeneficiaryIdAndTypeDocumentId(Long beneficiaryId, Long typeDocumentId);


}
