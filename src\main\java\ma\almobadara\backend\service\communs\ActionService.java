package ma.almobadara.backend.service.communs;

import jakarta.persistence.EntityManager;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.Audit.AuditApplicationService;
import ma.almobadara.backend.clients.RefFeignClient;
import ma.almobadara.backend.config.Messages;
import ma.almobadara.backend.dto.communs.ActionAndEntityDto;
import ma.almobadara.backend.dto.communs.ActionAuditDTO;
import ma.almobadara.backend.dto.communs.ActionDTO;
import ma.almobadara.backend.dto.referentiel.ActionStatusDTO;
import ma.almobadara.backend.enumeration.ActifActionStatut;
import ma.almobadara.backend.enumeration.InactifActionStatut;
import ma.almobadara.backend.exceptions.FunctionalException;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.mapper.ActionMapper;
import ma.almobadara.backend.model.administration.CacheAdUser;
import ma.almobadara.backend.model.beneficiary.ActionBeneficiary;
import ma.almobadara.backend.model.beneficiary.Beneficiary;
import ma.almobadara.backend.model.communs.Action;
import ma.almobadara.backend.model.communs.CommentAction;
import ma.almobadara.backend.model.donation.ActionDonation;
import ma.almobadara.backend.model.donation.Donation;
import ma.almobadara.backend.model.donor.*;
import ma.almobadara.backend.model.family.ActionFamily;
import ma.almobadara.backend.model.family.Family;
import ma.almobadara.backend.model.takenInCharge.ActionTakenInCharge;
import ma.almobadara.backend.model.takenInCharge.TakenInCharge;
import ma.almobadara.backend.repository.administration.CacheAdUserRepository;
import ma.almobadara.backend.repository.beneficiary.ActionBeneficiaryRepository;
import ma.almobadara.backend.repository.beneficiary.BeneficiaryRepository;
import ma.almobadara.backend.repository.communs.ActionDonorRepository;
import ma.almobadara.backend.repository.communs.ActionRepository;
import ma.almobadara.backend.repository.donation.ActionDonationRepository;
import ma.almobadara.backend.repository.donation.DonationRepository;
import ma.almobadara.backend.repository.donor.DonorRepository;
import ma.almobadara.backend.repository.family.ActionFamilyRepository;
import ma.almobadara.backend.repository.family.FamilyRepository;
import ma.almobadara.backend.repository.takenInCharge.ActionTakenInChargeRepository;
import ma.almobadara.backend.repository.takenInCharge.TakenInChargeRepository;
import ma.almobadara.backend.service.notification.NotificationService;
import ma.almobadara.backend.util.times.TimeWatch;
import org.checkerframework.checker.units.qual.A;
import org.hibernate.Hibernate;
import org.hibernate.proxy.HibernateProxy;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

import static ma.almobadara.backend.Audit.ObjectConverter.convertObjectToJson;
import static ma.almobadara.backend.service.administration.CacheAdUserService.getUsernameFromJwt;
import static ma.almobadara.backend.util.constants.GlobalConstants.*;
import static ma.almobadara.backend.util.strings.HandleSpecialChars.escapeSpecialChars;

import java.text.SimpleDateFormat;
import java.util.stream.StreamSupport;


@Service
@RequiredArgsConstructor
@Slf4j
public class ActionService {

    private final ActionRepository actionRepository;
    private final ActionDonorRepository actionDonorRepository;
    private final ActionBeneficiaryRepository actionBeneficiaryRepository;
    private final ActionFamilyRepository actionFamilyRepository;
    private final ActionTakenInChargeRepository actionTakenInChargeRepository;
    private final ActionDonationRepository actionDonationRepository;
    private final DonorRepository donorRepository;
    private final DonationRepository donationRepository;
    private final BeneficiaryRepository beneficiaryRepository;
    private final FamilyRepository familyRepository;
    private final CacheAdUserRepository cacheAdUserRepository;
    private final TakenInChargeRepository takenInChargeRepository;
    private final DonationRepository donationsRepository;
    private final ActionMapper actionMapper;
    private final Messages messages;
    private final RefFeignClient refFeignClient;
    private final AuditApplicationService auditApplicationService;
    private final EntityManager entityManager;
    private final MailSenderService mailService;
    private final NotificationService notificationService;
    private final ExecutorService executorService = Executors.newCachedThreadPool();

    private String generateUpdateComment(Action oldAction, Action newAction) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        StringBuilder content = new StringBuilder();
        boolean hasChanges = false;

        if (oldAction != null) {
            if (oldAction.getDeadline() != null && newAction.getDeadline() != null) {
                String oldDeadline = dateFormat.format(oldAction.getDeadline());
                String newDeadline = dateFormat.format(newAction.getDeadline());
                if (!oldDeadline.equals(newDeadline)) {
                    content.append("Date limite: de ")
                            .append(oldDeadline)
                            .append(" à ")
                            .append(newDeadline)
                            .append("\n");
                    hasChanges = true;
                }
            } else if (oldAction.getDeadline() != newAction.getDeadline()) {
                content.append("Date limite: de ")
                        .append(oldAction.getDeadline())
                        .append(" à ")
                        .append(newAction.getDeadline())
                        .append("\n");
                hasChanges = true;
            }

            if (!Objects.equals(oldAction.getSubject(), newAction.getSubject())) {
                content.append("Objet: de ")
                        .append(oldAction.getSubject())
                        .append(" à ")
                        .append(newAction.getSubject())
                        .append("\n");
                hasChanges = true;
            }

            if (!Objects.equals(oldAction.getAffectedTo(), newAction.getAffectedTo())) {
                content.append("Affecter à: de ")
                        .append(oldAction.getAffectedTo().getMail())
                        .append(" à ")
                        .append(newAction.getAffectedTo().getMail())
                        .append("\n");
                hasChanges = true;
            }
            ActionStatusDTO actionStatusDTOOld = refFeignClient.getActionStatus(oldAction.getStatus());
            ActionStatusDTO actionStatusDTOONew = refFeignClient.getActionStatus(newAction.getStatus());
            if (!Objects.equals(actionStatusDTOOld.getName(), actionStatusDTOONew.getName())) {
                content.append("Statut : de ")
                        .append(actionStatusDTOOld.getName())
                        .append(" à ")
                        .append(actionStatusDTOONew.getName())
                        .append("\n");
                hasChanges = true;
            }
        }

        return hasChanges ? "Une modification a été effectuée:\n" + content : "";
    }

    public ActionDTO addActionAndAssignToEntity(ActionAndEntityDto actionAndEntityDto) throws TechnicalException {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service Create Action and Assign to Entity -> id: {}, type: {}, action: {}", actionAndEntityDto.getEntityId(), actionAndEntityDto.getEntityType(), actionAndEntityDto.getActionDTO().getSubject());

        if (actionAndEntityDto.getActionDTO().getCreatedBy().getId() == null) {
            throw new TechnicalException(messages.get(USER_ID_CREATED_BY_NOT_FOUND));
        }

        Optional<CacheAdUser> createdBy = cacheAdUserRepository.findById(actionAndEntityDto.getActionDTO().getCreatedBy().getId());

        if (createdBy.isEmpty()) {
            throw new TechnicalException(messages.get(USER_ID_CREATED_BY_NOT_FOUND));
        }

        if (actionAndEntityDto.getActionDTO().getAffectedTo().getId() == null) {
            throw new TechnicalException(messages.get(USER_ID_AFFECTED_BY_NOT_FOUND));
        }

        Optional<CacheAdUser> affectedTo = cacheAdUserRepository.findById(actionAndEntityDto.getActionDTO().getAffectedTo().getId());



        if (affectedTo.isEmpty()) {
            throw new TechnicalException(messages.get(USER_ID_AFFECTED_BY_NOT_FOUND));
        }

        notificationService.sendNotification(affectedTo.get().getId(),"Une nouvelle action est ajoutée",actionAndEntityDto.getActionDTO().getSubject());
        // Créer l'action
        Action action = actionMapper.actionDtoModelToModel(actionAndEntityDto.getActionDTO());

        Action oldExistingAction = null;
        Action existingAction = new Action();
        String emailSubject;
        if (action.getId() != null) {
            oldExistingAction = actionRepository.findById(action.getId()).orElseThrow();
            BeanUtils.copyProperties(oldExistingAction, existingAction);
            emailSubject = "Action mise à jour";
        } else {
            emailSubject = "Nouvelle action vous a été assignée";
        }

        action.setCreatedBy(createdBy.get());
        action.setAffectedTo(affectedTo.get());

        // Ajouter les commentaires
        List<CommentAction> comments = action.getComments();
        if (comments != null) {
            List<CommentAction> validComments = new ArrayList<>();
            for (CommentAction comment : comments) {
                if (comment.getAuthor().getId() != null) {
                    CacheAdUser commentAffectedTo = cacheAdUserRepository.findById(comment.getAuthor().getId())
                            .orElseThrow(() -> new TechnicalException(messages.get(USER_ID_AFFECTED_BY_NOT_FOUND)));
                    comment.setAuthor(commentAffectedTo);
                }
                if (action.getId() != null) {
                    if (comment.getLabelComment() == null) {
                        comment.setLabelComment(true);
                    }
                }else{
                    comment.setLabelComment(false);
                }


                comment.setAction(action);

                // Vérifier si le champ content est nul ou vide
                if (comment.getContent() != null && !comment.getContent().trim().isEmpty()) {
                    validComments.add(comment);
                }
            }
            action.setComments(validComments);
        }

        // Vérifier les changements et ajouter un commentaire si nécessaire
        if (oldExistingAction != null) {
            String updateCommentContent = generateUpdateComment(oldExistingAction, action);
            if (!updateCommentContent.trim().isEmpty()) {
                CommentAction updateComment = new CommentAction();
                updateComment.setDate(LocalDate.now());
                updateComment.setContent(updateCommentContent);
                updateComment.setLabelComment(true);
                updateComment.setAuthor(createdBy.get());
                updateComment.setAction(action);

                // Ajouter le commentaire à la liste des commentaires de l'action
                action.getComments().add(updateComment);
            }
        }

        Action newAction = actionRepository.save(action);

        Object entity = getEntityById(actionAndEntityDto.getEntityId(), actionAndEntityDto.getEntityType());
        saveActionEntityAssociation(newAction, entity, existingAction);


        // Verify if the action is saved successfully
        if (newAction.getId() != null) {
            String emailBody = mailService.generateEmailContentOne(newAction, false , actionAndEntityDto.getEntityType() , actionAndEntityDto.getEntityId());

            // Send email notification for the action creation/update
            executorService.submit(() -> {
                try {
                    // Send email to the new affectedTo user
                    mailService.sendNewMail(newAction.getAffectedTo().getMail(), emailSubject, emailBody);

                    // Check if the affectedTo value has changed
                    if (!existingAction.getAffectedTo().getMail().equals(action.getAffectedTo().getMail())) {
                        // Send email to the old affectedTo user
                        String emailBodyForOldAffectedTo = mailService.generateEmailContentOne(newAction, false , actionAndEntityDto.getEntityType() , actionAndEntityDto.getEntityId());
                        mailService.sendNewMail(existingAction.getAffectedTo().getMail(), "Action vous a été retirée", emailBodyForOldAffectedTo);
                    }

                    // Check if the action is marked as done
                    if (newAction.getStatus() != null && newAction.getStatus() == InactifActionStatut.STATUT_REALISEE.getValue()) {
                        // Send email to the creator to notify that the action is done
                        String emailBodyForCreator = mailService.generateEmailContentOne(newAction, false , actionAndEntityDto.getEntityType() , actionAndEntityDto.getEntityId());
                        mailService.sendNewMail(newAction.getCreatedBy().getMail(), "Action réalisée", emailBodyForCreator);
                    }
                } catch (Exception e) {
                    log.error("Error sending email notification: {}", e.getMessage());
                    // Handle email sending error, log or throw exception as needed
                }
            });
        }

        ActionDTO newActionDTO = actionMapper.actionModelToDto(newAction);

        log.debug("End service Create Action and Assign to Entity -> id: {}, type: {}, action: {}, took {}", actionAndEntityDto.getEntityId(), actionAndEntityDto.getEntityType(), actionAndEntityDto.getActionDTO().getSubject(), watch.toMS());
        return newActionDTO;
    }


    public void sendReminderEmailsForActions() {
        log.debug("Start service sendReminderEmailsForActions");
        List<Action> allActions = actionRepository.findAll();

        for (Action action : allActions) {
            if (isDeadlineTomorrow(action.getDeadline()) && (ActifActionStatut.STATUT_A_EFFECTUER.getValue() == action.getStatus() || ActifActionStatut.STATUT_EN_COURS.getValue() == action.getStatus())) {
                String emailBody = mailService.generateEmailContentOne(action , true , null , null);
                String emailSubject = "Rappel: Deadline pour action";
                try {
                    mailService.sendNewMail(action.getAffectedTo().getMail(), emailSubject, emailBody);
                } catch (Exception e) {
                    throw new RuntimeException("Erreur lors de l'envoi de l'email à " + action.getAffectedTo().getLastName() + " avec l'email " + action.getAffectedTo().getMail(), e);
                }
            }
        }

        log.debug("End service sendReminderEmailsForActions");
    }

    private boolean isDeadlineTomorrow(Date deadline) {
        LocalDate tomorrow = LocalDate.now().plusDays(1);
        LocalDate deadlineDate = deadline.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        return tomorrow.equals(deadlineDate);
    }

    private void saveActionEntityAssociation(Action newAction, Object entity, Action existingAction) throws TechnicalException {
        // Gérer différents types d'entités
        ActionStatusDTO newActionStatusDTO = refFeignClient.getActionStatus(newAction.getStatus());

        if (DonorMoral.class.equals(entity.getClass()) || DonorPhysical.class.equals(entity.getClass()) || DonorAnonyme.class.equals(entity.getClass())) {
            ActionDonor actionDonorEntity = ActionDonor.builder()
                    .donor((Donor) entity)
                    .action(newAction)
                    .build();
            actionDonorRepository.save(actionDonorEntity);
            Donor donor=(Donor) entity;
            if (existingAction != null && existingAction.getId() != null){
                ActionStatusDTO oldActionStatusDto = refFeignClient.getActionStatus(existingAction.getStatus());
                if(donor instanceof DonorPhysical){
                    DonorPhysical exsitingDonor = (DonorPhysical) donor;

                    StringBuilder columnToAppend = new StringBuilder();
                    columnToAppend.append("\"Type  Donateur\": \"" + "Physique" + "\",");
                    columnToAppend.append("\"Code  Donateur\": \"" +escapeSpecialChars( exsitingDonor.getCode()) + "\",");
                    columnToAppend.append("\"Nom  Donateur\": \"" +escapeSpecialChars( exsitingDonor.getLastName() +" "+exsitingDonor.getFirstName()) + "\"");
                    auditApplicationService.audit("Modification de l'action du donateur" + exsitingDonor.getCode(), getUsernameFromJwt(), "Modification d'action du donateur",
                            existingAction.getAudit(columnToAppend,oldActionStatusDto.getName()), newAction.getAudit(columnToAppend,newActionStatusDTO.getName()), DONATEUR, UPDATE);

                }
                else if(donor instanceof DonorAnonyme){
                    DonorAnonyme exsitingDonor = (DonorAnonyme) donor;
                    StringBuilder columnToAppend = new StringBuilder();
                    columnToAppend.append("\"Type  Donateur\": \"" + "Anonyme" + "\",");
                    columnToAppend.append("\"Code  Donateur\": \"" + escapeSpecialChars(exsitingDonor.getCode()) + "\",");
                    columnToAppend.append("\"Nom  Donateur\": \"" + escapeSpecialChars(exsitingDonor.getName()) + "\"");
                    auditApplicationService.audit("Modification de l'action du donateur " + exsitingDonor.getCode(), getUsernameFromJwt(), "Modification d'action du donateur :",
                            existingAction.getAudit(columnToAppend,oldActionStatusDto.getName()), newAction.getAudit(columnToAppend,newActionStatusDTO.getName()), DONATEUR, UPDATE);
                } else {
                    DonorMoral exsitingDonor = (DonorMoral) donor;
                    StringBuilder columnToAppend = new StringBuilder();
                    columnToAppend.append("\"Type  Donateur\": \"" + "Moral" + "\",");
                    columnToAppend.append("\"Code  Donateur\": \"" + escapeSpecialChars(exsitingDonor.getCode()) + "\",");
                    columnToAppend.append("\"Nom  Donateur\": \"" + escapeSpecialChars(exsitingDonor.getCompany()) + "\"");

                    auditApplicationService.audit("Modification de l'action du donateur" + exsitingDonor.getCode(), getUsernameFromJwt(), "Modification d'action du donateur :",
                            existingAction.getAudit(columnToAppend,oldActionStatusDto.getName()), newAction.getAudit(columnToAppend,newActionStatusDTO.getName()), DONATEUR, UPDATE);
                }
            }else {
                if(donor instanceof DonorPhysical){
                    DonorPhysical exsitingDonor = (DonorPhysical) donor;
                    StringBuilder columnToAppend = new StringBuilder();
                    columnToAppend.append("\"Type  Donateur\": \"" + "Physique" + "\",");
                    columnToAppend.append("\"Code  Donateur\": \"" + escapeSpecialChars(exsitingDonor.getCode()) + "\",");
                    columnToAppend.append("\"Nom  Donateur\": \"" + escapeSpecialChars(exsitingDonor.getFirstName()+" "+exsitingDonor.getLastName()) + "\"");
                    auditApplicationService.audit("Ajout d'une action du donateur :" + exsitingDonor.getCode(), getUsernameFromJwt(), "Ajout  note du donateur",
                            null,newAction.getAudit(columnToAppend,newActionStatusDTO.getName()), DONATEUR,CREATE);
                }
                else if(donor instanceof DonorAnonyme){
                    DonorAnonyme exsitingDonor = (DonorAnonyme) donor;
                    StringBuilder columnToAppend = new StringBuilder();
                    columnToAppend.append("\"Type  Donateur\": \"" + "Anonyme" + "\",");
                    columnToAppend.append("\"Code  Donateur\": \"" + escapeSpecialChars(exsitingDonor.getCode()) + "\",");
                    columnToAppend.append("\"Nom  Donateur\": \"" + escapeSpecialChars(exsitingDonor.getName()) + "\"");
                    auditApplicationService.audit("Ajout d'une action du donateur : " + exsitingDonor.getCode(), getUsernameFromJwt(), "Ajout d'action note du donateur : ",
                            null, newAction.getAudit(columnToAppend,newActionStatusDTO.getName()), DONATEUR, CREATE);
                } else {
                    DonorMoral exsitingDonor = (DonorMoral) donor;
                    StringBuilder columnToAppend = new StringBuilder();
                    columnToAppend.append("\"Type  Donateur\": \"" + "Moral" + "\",");
                    columnToAppend.append("\"Code  Donateur\": \"" + escapeSpecialChars(exsitingDonor.getCode()) + "\",");
                    columnToAppend.append("\"Nom  Donateur\": \"" + escapeSpecialChars(exsitingDonor.getCompany()) + "\"");
                    auditApplicationService.audit("Ajout d'une action du donateur :" + exsitingDonor.getCode(), getUsernameFromJwt(), "Ajout d'action note du donateur : ",
                            null, newAction.getAudit(columnToAppend,newActionStatusDTO.getName()), DONATEUR,CREATE);
                }
            }
        } else if (Beneficiary.class.equals(entity.getClass())) {
            ActionBeneficiary actionBeneficiaryEntity = ActionBeneficiary.builder()
                    .beneficiary((Beneficiary) entity)
                    .action(newAction)
                    .build();
            actionBeneficiaryRepository.save(actionBeneficiaryEntity);
            Beneficiary beneficiary= (Beneficiary) entity;

            StringBuilder columnToAppend = new StringBuilder();
            columnToAppend.append("\"Code  bénéficiaire\": \"" +escapeSpecialChars(beneficiary.getCode()) + "\",");
            columnToAppend.append("\"Nom  bénéficiaire\": \"" +escapeSpecialChars(beneficiary.getPerson().getFirstName()+" "+beneficiary.getPerson().getLastName()) + "\"");

            if (existingAction != null && existingAction.getId() != null){
                ActionStatusDTO oldActionStatusDto = refFeignClient.getActionStatus(existingAction.getStatus());

                auditApplicationService.audit("Modification de l'action du bénéficiaire : " + actionBeneficiaryEntity.getBeneficiary().getCode(), getUsernameFromJwt(), "Modification de l'action du beneficiaire",
                        existingAction.getAudit(columnToAppend,oldActionStatusDto.getName()), newAction.getAudit(columnToAppend,newActionStatusDTO.getName()), BENEFICIAIRE, UPDATE);
            }else {
                auditApplicationService.audit("Ajout d'une action pour le bénéficiaire : " + actionBeneficiaryEntity.getBeneficiary().getCode(), getUsernameFromJwt(), "Ajout action pour beneficiaire",
                        null, newAction.getAudit(columnToAppend,newActionStatusDTO.getName()), BENEFICIAIRE, CREATE);
            }
        }else if (Family.class.equals(entity.getClass())) {
            ActionFamily actionFamilyEntity = ActionFamily.builder()
                    .family((Family) entity)
                    .action(newAction)
                    .build();
            actionFamilyRepository.save(actionFamilyEntity);
            Family familyEntity = (Family) entity;
            StringBuilder columnToAppend = new StringBuilder();
            columnToAppend.append("\"Code  famille\": \"" +escapeSpecialChars(familyEntity.getCode())  + "\"");
             if (existingAction != null && existingAction.getId() != null){
                ActionStatusDTO oldActionStatusDto = refFeignClient.getActionStatus(existingAction.getStatus());
                auditApplicationService.audit("Modification de l'action  Famille : " +actionFamilyEntity.getFamily().getCode(), getUsernameFromJwt(), "Modification de l'action  Famille",
                        existingAction.getAudit(columnToAppend,oldActionStatusDto.getName()),newAction.getAudit(columnToAppend,newActionStatusDTO.getName()), FAMILLE, UPDATE);
            }else {
                auditApplicationService.audit("Ajout d'une action pour la famille :  " + actionFamilyEntity.getFamily().getCode(), getUsernameFromJwt(), "Ajout action pour Famille",
                        null,newAction.getAudit(columnToAppend,newActionStatusDTO.getName()), FAMILLE, CREATE);
            }
        }else if (TakenInCharge.class.equals(entity.getClass())) {
            ActionTakenInCharge actionTakenInCharge = ActionTakenInCharge.builder()
                    .takenInCharge((TakenInCharge) entity)
                    .action(newAction)
                    .build();
            actionTakenInChargeRepository.save(actionTakenInCharge);
            TakenInCharge takenInChargeEntity = (TakenInCharge) entity;

            StringBuilder columnToAppend = new StringBuilder();
            columnToAppend.append("\"Code\": \"" +escapeSpecialChars(takenInChargeEntity.getCode()) + "\",");
            columnToAppend.append("\"Service \": \"" +escapeSpecialChars(takenInChargeEntity.getService().getName())+ "\"");
            if (existingAction != null && existingAction.getId() != null){
                ActionStatusDTO oldActionStatusDto = refFeignClient.getActionStatus(existingAction.getStatus());

                auditApplicationService.audit("Modification de l'action  prise en charge : " +actionTakenInCharge.getTakenInCharge().getCode(), getUsernameFromJwt(), "Modification de l'action  Prise en Charge",
                        existingAction.getAudit(columnToAppend,oldActionStatusDto.getName()),  newAction.getAudit(columnToAppend,newActionStatusDTO.getName()), PRISEENCHARGE, UPDATE);
            }else {
                auditApplicationService.audit("Ajout d'une action pour la prise en charge: " + actionTakenInCharge.getTakenInCharge().getCode(), getUsernameFromJwt(), "Ajout action pour Prise en Charge",
                        null,  newAction.getAudit(columnToAppend,newActionStatusDTO.getName()), PRISEENCHARGE, CREATE);
            }
        }
        else if(Donation.class.equals(entity.getClass())){
            ActionDonation actionDonation = ActionDonation.builder()
                    .donation((Donation) entity)
                    .action(newAction)
                    .build();
            actionDonationRepository.save(actionDonation);
            Donation donationEntity = (Donation) entity;
            StringBuilder columnToAppend = new StringBuilder();
            columnToAppend.append("\"Code  Donation\": \"" +escapeSpecialChars(donationEntity.getCode()) + "\",");
            columnToAppend.append("\"Code  Donateur\": \"" +escapeSpecialChars(donationEntity.getDonor().getCode()) + "\",");
            columnToAppend.append("\"Type  Donation\": \"" +escapeSpecialChars(donationEntity.getType()) + "\"");
            if (existingAction != null && existingAction.getId() != null){
                ActionStatusDTO oldActionStatusDto = refFeignClient.getActionStatus(existingAction.getStatus());
                auditApplicationService.audit("Modification de l'action  donation : " + actionDonation.getDonation().getCode(), getUsernameFromJwt(), "Modification de l'action  Donation",
                        existingAction.getAudit(columnToAppend,oldActionStatusDto.getName()),newAction.getAudit(columnToAppend,newActionStatusDTO.getName()), DONATION2, UPDATE);
            }else {
                auditApplicationService.audit("Ajout d'une action pour la donation : " +actionDonation.getDonation().getCode(), getUsernameFromJwt(), "Ajout action pour Donation",
                        null, newAction.getAudit(columnToAppend,newActionStatusDTO.getName()), DONATION2, CREATE);
            }
        }
        // Ajouter d'autres cas selon les entités nécessaires
        else {
            throw new TechnicalException(messages.get(ENTITY_ID_NOT_FOUND));
        }
    }

    private Object getEntityById(Long entityId, String entityType) throws TechnicalException {
        return switch (entityType.toLowerCase()) {
            case DONOR -> donorRepository.findById(entityId).orElse(null);
            case BENEFICIARY -> beneficiaryRepository.findById(entityId).orElse(null);
            case FAMILY -> familyRepository.findById(entityId).orElse(null);
            case TAKENINCHARGE -> takenInChargeRepository.findById(entityId).orElse(null);
            case DONATION -> donationsRepository.findById(entityId).orElse(null);
            // Ajouter d'autres cas selon les entités nécessaires
            default -> throw new TechnicalException(messages.get(ENTITY_ID_NOT_FOUND));
        };
    }

    public List<ActionDTO> getAllActionsByEntity(String entityType, Long entityId) throws TechnicalException {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service Get All Actions By EntityId: {}",entityId);

        List<ActionDTO> actionDTOList = switch (entityType.toLowerCase()) {
            case DONOR -> getAllActionsByDonor(entityId);
            case BENEFICIARY -> getAllActionsByBeneficiary(entityId);
            case FAMILY -> getAllActionsByFamily(entityId);
            case TAKENINCHARGE -> getAllActionsByTakenInCharge(entityId);
            case DONATION -> getAllActionsByDonation(entityId);
            default -> throw new TechnicalException(messages.get(ENTITY_TYPE_NOT_FOUND));
        };
        log.debug("End service Get All Actions By Entity, took {}", watch.toMS());
        return actionDTOList;
    }

    private List<ActionDTO> getAllActionsByDonor(Long donorId) {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service Get All Actions By DonorId {}", donorId);
        List<ActionDonor> actionDonorList = actionDonorRepository.findByDonorId(donorId);
        List<ActionDTO> actionDTOList = new ArrayList<>();

        Donor donor = donorRepository.findById(donorId).get();
        // If donor is deleted (archived), return empty list
        if (donor.getDeletedAt() != null) {
            log.debug("Donor {} is archived (deletedAt not null), returning empty action list.", donorId);
            return actionDTOList;
        }

        for (ActionDonor actionDonor : actionDonorList) {
            Action actionEntity = actionDonor.getAction();

            ActionStatusDTO actionStatusDTO = refFeignClient.getActionStatus(actionDonor.getAction().getStatus());

            ActionDTO actionDTO = actionMapper.actionModelToDto(actionEntity);
            actionDTO.setActionStatus(actionStatusDTO);
            actionDTOList.add(actionDTO);
        }
        auditApplicationService.audit(" Consultation des actions du donateur :"+donor.getCode(), getUsernameFromJwt(), "Suppression  note du donateur",null
                ,null, DONATEUR,CONSULTATION);

        log.debug("End service Get All Actions By DonorId {}, took {}", donorId, watch.toMS());
        return actionDTOList;
    }

    private List<ActionDTO> getAllActionsByBeneficiary(Long beneficiaryId) {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service Get All Actions By BeneficiaryId {}", beneficiaryId);
        List<ActionBeneficiary> actionBeneficiaryList = actionBeneficiaryRepository.findByBeneficiaryId(beneficiaryId);
        List<ActionDTO> actionDTOList = new ArrayList<>();
        Beneficiary beneficiary = beneficiaryRepository.findById(beneficiaryId).get();
        // If beneficiary is archived, return empty list
        if (Boolean.TRUE.equals(beneficiary.getArchived())) {
            log.debug("Beneficiary {} is archived, returning empty action list.", beneficiaryId);
            return actionDTOList;
        }
        for (ActionBeneficiary actionBeneficiary : actionBeneficiaryList) {
            Action actionEntity = actionBeneficiary.getAction();

            ActionStatusDTO actionStatusDTO = refFeignClient.getActionStatus(actionBeneficiary.getAction().getStatus());

            ActionDTO actionDTO = actionMapper.actionModelToDto(actionEntity);
            actionDTO.setActionStatus(actionStatusDTO);
            actionDTOList.add(actionDTO);
        }
        auditApplicationService.audit("Consultation des actions du bénéficiaire :"+beneficiary.getCode(), getUsernameFromJwt(), "Suppression  note du donateur",null
                ,null, BENEFICIAIRE,CONSULTATION);
        log.debug("End service Get All Actions By beneficiaryId {}, took {}", beneficiaryId, watch.toMS());
        return actionDTOList;
    }

    private List<ActionDTO> getAllActionsByFamily(Long familyId) {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service Get All Actions By familyId {}", familyId);
        List<ActionFamily> actionFamilyList = actionFamilyRepository.findByFamilyId(familyId);
        List<ActionDTO> actionDTOList = new ArrayList<>();
        Family family=familyRepository.findById(familyId).get();
        for (ActionFamily actionFamily : actionFamilyList) {
            Action actionEntity = actionFamily.getAction();

            ActionStatusDTO actionStatusDTO = refFeignClient.getActionStatus(actionFamily.getAction().getStatus());

            ActionDTO actionDTO = actionMapper.actionModelToDto(actionEntity);
            actionDTO.setActionStatus(actionStatusDTO);
            actionDTOList.add(actionDTO);
        }
        auditApplicationService.audit("Consultation des actions  famille : "+family.getCode(), getUsernameFromJwt(), "Suppression  note du donateur",null
                ,null, FAMILLE,CONSULTATION);
        log.debug("End service Get All Actions By familyId {}, took {}", familyId, watch.toMS());
        return actionDTOList;
    }

    private List<ActionDTO> getAllActionsByTakenInCharge(Long takenInChargeId) {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service Get All Actions By takenInChargeId {}", takenInChargeId);
        List<ActionTakenInCharge> actionTakenInChargeList = actionTakenInChargeRepository.findByTakenInChargeId(takenInChargeId);
        List<ActionDTO> actionDTOList = new ArrayList<>();

        TakenInCharge takenInCharge=takenInChargeRepository.findById(takenInChargeId).get();
        for (ActionTakenInCharge actionTakenInCharge : actionTakenInChargeList) {
            Action actionEntity = actionTakenInCharge.getAction();

            ActionStatusDTO actionStatusDTO = refFeignClient.getActionStatus(actionTakenInCharge.getAction().getStatus());

            ActionDTO actionDTO = actionMapper.actionModelToDto(actionEntity);
            actionDTO.setActionStatus(actionStatusDTO);
            actionDTOList.add(actionDTO);
        }
        auditApplicationService.audit("Consultation des actions  prise en charge : "+takenInCharge.getCode(), getUsernameFromJwt(), "Suppression  note du donateur",null
                ,null, PRISEENCHARGE,CONSULTATION);
        log.debug("End service Get All Actions By takenInChargeId {}, took {}", takenInChargeId, watch.toMS());
        return actionDTOList;
    }

    private List<ActionDTO> getAllActionsByDonation(Long donationId) {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service Get All Actions By DonationId {}", donationId);
        List<ActionDonation> actionDonationList = actionDonationRepository.findByDonationId(donationId);
        List<ActionDTO> actionDTOList = new ArrayList<>();
        Donation donation = donationRepository.findById(donationId).get();
        // If donation is archived, return empty list
        if (Boolean.TRUE.equals(donation.getArchived())) {
            log.debug("Donation {} is archived, returning empty action list.", donationId);
            return actionDTOList;
        }
        for (ActionDonation actionDonation : actionDonationList) {
            Action actionEntity = actionDonation.getAction();

            ActionStatusDTO actionStatusDTO = refFeignClient.getActionStatus(actionDonation.getAction().getStatus());

            ActionDTO actionDTO = actionMapper.actionModelToDto(actionEntity);
            actionDTO.setActionStatus(actionStatusDTO);
            actionDTOList.add(actionDTO);
        }
        auditApplicationService.audit("Consultation des actions  donation :"+donation.getCode(), getUsernameFromJwt(), "Suppression  note du donateur",null
               ,null, DONATION2,CONSULTATION);
        log.debug("End service Get All Actions By donationId {}, took {}", donationId, watch.toMS());
        return actionDTOList;
    }

    private String getUserIDFromJwt() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        //Jwt jwtToken = (Jwt) authentication.getPrincipal();

        if (authentication.getPrincipal() instanceof Jwt jwtToken) {
            Map<String, Object> claims = jwtToken.getClaims();
            return (String) claims.get("oid");
        }
        else if (authentication.getPrincipal() instanceof CacheAdUser user) {
            return user.getAzureDirectoryId();
        }

        return null;
    }

    public List<ActionDTO> getAllActionsBy(Long userId) throws FunctionalException {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service Get All Actions for User with ID: {}", userId);

        Optional<CacheAdUser> user = cacheAdUserRepository.findById(userId);
        if (user.isEmpty()) {
            throw new FunctionalException(messages.get("error.user.not.found"));
        }

        List<Action> userActions = actionRepository.findByCreatedByOrAffectedTo(user.get(), user.get());
        List<ActionDTO> userActionDTOs = userActions.stream()
                .map(actionMapper::actionModelToDto)
                .collect(Collectors.toList());

        log.debug("End service Get All Actions for User with ID: {}, took {}", userId, watch.toMS());
        return userActionDTOs;
    }

    public void deleteAction(Long id, String entityType) throws TechnicalException {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service Delete Action id: {}", id);

        switch (entityType.toLowerCase()) {
            case DONOR:
                List<ActionDonor> actionDonorList = actionDonorRepository.findByActionId(id);
                actionDonorRepository.deleteAll(actionDonorList);
                Action deleteAction = actionRepository.findById(id).orElseThrow(() -> new TechnicalException("Action not found"));
                ActionStatusDTO actionStatusDto = refFeignClient.getActionStatus(deleteAction.getStatus());

                Optional<Donor> donorOptional =donorRepository.findById(actionDonorList.get(0).getDonor().getId());
                Donor donor = donorOptional.get();
                if (donor instanceof HibernateProxy) {
                    donor = (Donor) Hibernate.unproxy(donor);
                }
                if(donor instanceof DonorPhysical){
                    DonorPhysical exsitingDonor = (DonorPhysical) donor;
                    StringBuilder columnToAppend = new StringBuilder();
                    columnToAppend.append("\"Type  donateur\": \"" + "Physique" + "\",");
                    columnToAppend.append("\"Code  donateur\": \"" + escapeSpecialChars(exsitingDonor.getCode())  + "\",");
                    columnToAppend.append("\"Nom  donateur\": \"" + escapeSpecialChars(exsitingDonor.getFirstName())+" "+escapeSpecialChars(exsitingDonor.getLastName()) + "\"");
                    auditApplicationService.audit("Suppression d'une action du donateur : " + exsitingDonor.getCode(), getUsernameFromJwt(), "Suppression d'action du donateur : ",
                            deleteAction.getAudit(columnToAppend,actionStatusDto.getName()), null, DONATEUR,DELETE);
                }
                else if(donor instanceof DonorAnonyme){
                    DonorAnonyme exsitingDonor = (DonorAnonyme) donor;
                    StringBuilder columnToAppend = new StringBuilder();
                    columnToAppend.append("\"Type  donateur\": \"" + "Anonyme" + "\",");
                    columnToAppend.append("\"Code  donateur\": \"" + escapeSpecialChars(exsitingDonor.getCode()) + "\",");
                    columnToAppend.append("\"Nom  donateur\": \"" + escapeSpecialChars(exsitingDonor.getName()) + "\"");
                    auditApplicationService.audit("Suppression d'une action du donateur : " + exsitingDonor.getCode(), getUsernameFromJwt(), "Suppression d'action du donateur : ",
                            deleteAction.getAudit(columnToAppend,actionStatusDto.getName()), null, DONATEUR, DELETE);
                } else {
                    DonorMoral exsitingDonor = (DonorMoral) donor;
                    StringBuilder columnToAppend = new StringBuilder();
                    columnToAppend.append("\"Type  donateur\": \"" + "Moral" + "\",");
                    columnToAppend.append("\"Code  donateur\": \"" + escapeSpecialChars(exsitingDonor.getCode()) + "\",");
                    columnToAppend.append("\"Nom  donateur\": \"" + escapeSpecialChars(exsitingDonor.getCompany()) + "\"");
                    auditApplicationService.audit("Suppression d'une action du donateur :  " + exsitingDonor.getCode(), getUsernameFromJwt(), "Suppression  note du donateur",
                            deleteAction.getAudit(columnToAppend,actionStatusDto.getName()),null, DONATEUR,DELETE);
                }
                break;
            case BENEFICIARY:
                List<ActionBeneficiary> actionBeneficiaryList = actionBeneficiaryRepository.findByActionId(id);
                actionBeneficiaryRepository.deleteAll(actionBeneficiaryList);
                deleteAction = actionRepository.findById(id).orElseThrow(() -> new TechnicalException("Action not found"));
                Optional<Beneficiary> beneficiaryOptional =beneficiaryRepository.findById(actionBeneficiaryList.get(0).getBeneficiary().getId());
                Beneficiary beneficiary = beneficiaryOptional.get();
                actionStatusDto = refFeignClient.getActionStatus(deleteAction.getStatus());

                if (beneficiary instanceof HibernateProxy) {
                    beneficiary = (Beneficiary) Hibernate.unproxy(beneficiary);
                }

                StringBuilder columnToAppend = new StringBuilder();
                columnToAppend.append("\"Code\": \"" + escapeSpecialChars(beneficiary.getCode()) + "\",");
                columnToAppend.append("\"Nom  bénéficiaire \": \"" + escapeSpecialChars(beneficiary.getPerson().getLastName())+" "+escapeSpecialChars(beneficiary.getPerson().getFirstName())+ "\"");

                auditApplicationService.audit("Suppression d'une action pour Beneficiare : "+beneficiary.getCode() , getUsernameFromJwt(), "Suppression d'action pour Beneficiare",
                        deleteAction.getAudit(columnToAppend,actionStatusDto.getName()), null, BENEFICIAIRE, DELETE);
                break;
            case FAMILY:
                List<ActionFamily> actionFamilyList = actionFamilyRepository.findByActionId(id);
                actionFamilyRepository.deleteAll(actionFamilyList);

                deleteAction = actionRepository.findById(id).orElseThrow(() -> new TechnicalException("Action not found"));
                Optional<Family> familyOptional =familyRepository.findById(actionFamilyList.get(0).getFamily().getId());
                Family family = familyOptional.get();
                actionStatusDto = refFeignClient.getActionStatus(deleteAction.getStatus());

                if (family instanceof HibernateProxy) {
                    family = (Family) Hibernate.unproxy(family);
                }

                columnToAppend = new StringBuilder();
                columnToAppend.append("\"Code  famille\": \"" + escapeSpecialChars(family.getCode()) + "\"");
                auditApplicationService.audit("Suppression d'une action pour la famille : "+family.getCode() , getUsernameFromJwt(), "Delete Family Action",
                        deleteAction.getAudit(columnToAppend,actionStatusDto.getName()), null, FAMILLE, DELETE);
                break;
            case TAKENINCHARGE:
                List<ActionTakenInCharge> actionTakenInChargeList = actionTakenInChargeRepository.findByActionId(id);
                actionTakenInChargeRepository.deleteAll(actionTakenInChargeList);
                deleteAction = actionRepository.findById(id).orElseThrow(() -> new TechnicalException("Action not found"));
                Optional<TakenInCharge> takenInCharge =takenInChargeRepository.findById(actionTakenInChargeList.get(0).getTakenInCharge().getId());
                TakenInCharge takenInCharge1 = takenInCharge.get();
                if (takenInCharge1 instanceof HibernateProxy) {
                    takenInCharge1 = (TakenInCharge) Hibernate.unproxy(takenInCharge1);
                }
                actionStatusDto = refFeignClient.getActionStatus(deleteAction.getStatus());


                columnToAppend = new StringBuilder();
                columnToAppend.append("\"Code\": \"" + escapeSpecialChars(takenInCharge1.getCode()) + "\",");
                columnToAppend.append("\"Service\": \"" + escapeSpecialChars(takenInCharge1.getService().getName()) + "\"");
                auditApplicationService.audit("Suppression d'une action pour prise en charge : "+takenInCharge1.getCode() , getUsernameFromJwt(), "Delete TakenInCharge Action",
                        deleteAction.getAudit(columnToAppend,actionStatusDto.getName()), null, PRISEENCHARGE, DELETE);
                break;
                case DONATION:
                List<ActionDonation> actionDonationList = actionDonationRepository.findByActionId(id);
                actionDonationRepository.deleteAll(actionDonationList);
                deleteAction = actionRepository.findById(id).orElseThrow(() -> new TechnicalException("Action not found"));
                Optional<Donation> donation =donationRepository.findById(actionDonationList.get(0).getDonation().getId());
                Donation donation1 = donation.get();
                if (donation1 instanceof HibernateProxy) {
                    donation1 = (Donation) Hibernate.unproxy(donation1);
                }
                actionStatusDto = refFeignClient.getActionStatus(deleteAction.getStatus());

                columnToAppend = new StringBuilder();
                columnToAppend.append("\"Code  Donation\": \"" +escapeSpecialChars(donation1.getCode()) + "\",");
                columnToAppend.append("\"Code  Donateur\": \"" +escapeSpecialChars(donation1.getDonor().getCode()) + "\",");
                columnToAppend.append("\"Type  Donation\": \"" +escapeSpecialChars(donation1.getType()) + "\"");
                auditApplicationService.audit("Suppression d'une action pour Donation : "+donation1.getCode() , getUsernameFromJwt(), "Delete Donation Action",
                        deleteAction.getAudit(columnToAppend,actionStatusDto.getName()), null, DONATION2, DELETE);
                break;
            // Ajoutez d'autres cas pour chaque type d'entité si nécessaire
            default:
                throw new TechnicalException(messages.get(ENTITY_TYPE_NOT_FOUND));
        }
        log.debug("End service Delete Action, took {}", watch.toMS());
        actionRepository.deleteById(id);
    }

    public Page<ActionAndEntityDto> getAllActionsWithModules(int page, int size, String searchByValue, String searchByModule, Long searchByStatus, String searchByUser, Date minDate, Date maxDate) {
        Pageable pageable = PageRequest.of(page, size);

        Iterable<Action> actions = actionRepository.findAll();

        if (searchByValue != null || searchByModule != null || searchByStatus != null || searchByUser != null || minDate != null || maxDate != null) {
            actions = filterActions(searchByValue, searchByModule, searchByStatus, searchByUser, minDate, maxDate);
        }

        List<ActionDTO> actionDTOS = StreamSupport.stream(actions.spliterator(), false)
                .map(actionMapper::actionModelToDto)
                .toList();

        List<ActionAndEntityDto> actionAndEntityDtos = new ArrayList<>();

        for (ActionDTO action : actionDTOS) {
            ActionAndEntityDto actionAndEntityDto = getActionAndEntityDto(action);

            if (searchByModule != null && !searchByModule.isEmpty()) {
                if (actionAndEntityDto.getModuleName() != null && actionAndEntityDto.getModuleName().equalsIgnoreCase(searchByModule)) {
                    actionAndEntityDtos.add(actionAndEntityDto);
                }
            } else {
                actionAndEntityDtos.add(actionAndEntityDto);
            }
        }

        int start = Math.min((int) pageable.getOffset(), actionAndEntityDtos.size());
        int end = Math.min((start + pageable.getPageSize()), actionAndEntityDtos.size());
        List<ActionAndEntityDto> paginatedList = actionAndEntityDtos.subList(start, end);

        return new PageImpl<>(paginatedList, pageable, actionAndEntityDtos.size());
    }
    public List<ActionAndEntityDto> getAllActionsByUserConnected(int page, int size) {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service Get All Actions");

        String azureDirectoryId = getUserIDFromJwt();

        Pageable pageable = PageRequest.of(page, size);
        Pageable pageableTwo = PageRequest.of(page, 1000);
        List<Integer> statuses = Arrays.asList(3, 4);

        List<Action> actionsPage = actionRepository.findActionByStatus(statuses, pageableTwo);
        
        List<ActionDTO> actionDTOS = (List<ActionDTO>) actionMapper.actionListModelToDto(actionsPage);

        List<ActionAndEntityDto> actionAndEntityDtos = new ArrayList<>();

        for (ActionDTO action : actionDTOS) {
            ActionAndEntityDto actionAndEntityDto = getActionAndEntityDto(action);
            boolean skip = false;
            if (actionAndEntityDto.getEntityType() != null && actionAndEntityDto.getEntityId() != null) {
                switch (actionAndEntityDto.getEntityType()) {
                    case BENEFICIARY -> {
                        Beneficiary beneficiary = beneficiaryRepository.findById(actionAndEntityDto.getEntityId()).orElse(null);
                        if (beneficiary == null || Boolean.TRUE.equals(beneficiary.getArchived())) {
                            skip = true;
                        }
                    }
                    case DONATION -> {
                        Donation donation = donationRepository.findById(actionAndEntityDto.getEntityId()).orElse(null);
                        if (donation == null || Boolean.TRUE.equals(donation.getArchived())) {
                            skip = true;
                        }
                    }
                    case DONOR -> {
                        Donor donor = donorRepository.findById(actionAndEntityDto.getEntityId()).orElse(null);
                        if (donor == null || donor.getDeletedAt() != null) {
                            skip = true;
                        }
                    }
                }
            }
            if (!skip) {
                actionAndEntityDtos.add(actionAndEntityDto);
            }
        }

        log.debug("End service Get All Actions, took {}", watch.toMS());
        log.debug("Total number of actions for user: {}", actionAndEntityDtos.size());
        return actionAndEntityDtos;
    }

    private ActionAndEntityDto getActionAndEntityDto(ActionDTO action) {
        ActionAndEntityDto actionAndEntityDto = new ActionAndEntityDto();

        ActionStatusDTO actionStatusDTO = refFeignClient.getActionStatus(action.getActionStatus().getId());
        action.setActionStatus(actionStatusDTO);

        actionAndEntityDto.setActionDTO(action);

        Long entityId;
        String entityType;

        List<ActionBeneficiary> actionBeneficiaries = actionBeneficiaryRepository.findByActionId(action.getId());
        List<ActionDonation> actionDonations = actionDonationRepository.findByActionId(action.getId());
        List<ActionDonor> actionDonors = actionDonorRepository.findByActionId(action.getId());
        List<ActionFamily> actionFamilies = actionFamilyRepository.findByActionId(action.getId());
        List<ActionTakenInCharge> actionTakenInCharges = actionTakenInChargeRepository.findByActionId(action.getId());

        if (!actionBeneficiaries.isEmpty()) {
            ActionBeneficiary actionBeneficiary = actionBeneficiaryRepository.findByActionId(action.getId()).get(0);
            entityId = actionBeneficiary.getBeneficiary().getId();
            entityType = BENEFICIARY;
            actionAndEntityDto.setEntityId(entityId);
            actionAndEntityDto.setEntityType(entityType);
            actionAndEntityDto.setModuleName(BENEFICIAIRE);
            actionAndEntityDto.setModulepath("beneficiaries");
        } else if (!actionDonations.isEmpty()) {
            ActionDonation actionDonation = actionDonationRepository.findByActionId(action.getId()).get(0);
            entityId = actionDonation.getDonation().getId();
            entityType = DONATION;
            actionAndEntityDto.setEntityId(entityId);
            actionAndEntityDto.setEntityType(entityType);
            actionAndEntityDto.setModuleName(DONATION2);
            actionAndEntityDto.setModulepath("donations");
        } else if (!actionDonors.isEmpty()) {
            ActionDonor actionDonor = actionDonorRepository.findByActionId(action.getId()).get(0);
            entityId = actionDonor.getDonor().getId();
            entityType = DONOR;
            actionAndEntityDto.setEntityId(entityId);
            actionAndEntityDto.setEntityType(entityType);
            actionAndEntityDto.setModuleName(DONATEUR);
            actionAndEntityDto.setModulepath("donors");
        } else if (!actionFamilies.isEmpty()) {
            ActionFamily actionFamily = actionFamilyRepository.findByActionId(action.getId()).get(0);
            entityId = actionFamily.getFamily().getId();
            entityType = FAMILY;
            actionAndEntityDto.setEntityId(entityId);
            actionAndEntityDto.setEntityType(entityType);
            actionAndEntityDto.setModuleName(FAMILLE);
            actionAndEntityDto.setModulepath("families");
        } else if (!actionTakenInCharges.isEmpty()) {
            ActionTakenInCharge actionTakenInCharge = actionTakenInChargeRepository.findByActionId(action.getId()).get(0);
            entityId = actionTakenInCharge.getTakenInCharge().getId();
            entityType = TAKENINCHARGE;
            actionAndEntityDto.setEntityId(entityId);
            actionAndEntityDto.setEntityType(entityType);
            actionAndEntityDto.setModuleName(PRISEENCHARGE);
            actionAndEntityDto.setModulepath("takenInCharges");
        }

        return actionAndEntityDto;
    }

    public List<Action> filterActions(String searchByValue, String searchByModule, Long searchByStatus, String searchByUser, Date minDate, Date maxDate) {
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Action> criteriaQuery = criteriaBuilder.createQuery(Action.class);
        Root<Action> root = criteriaQuery.from(Action.class);

        Predicate predicates = criteriaBuilder.conjunction();

        if (searchByValue != null && !searchByValue.isEmpty()) {
            Predicate objetPredicate = criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("subject")),
                    "%" + searchByValue.toLowerCase() + "%"
            );
            predicates = criteriaBuilder.and(predicates, objetPredicate);
        }


//        if (searchByModule != null && !searchByModule.isEmpty()) {
//            predicates = criteriaBuilder.and(predicates, criteriaBuilder.equal(root.get("operation"), searchByOperation));
//        }

        if (searchByStatus != null) {
            predicates = criteriaBuilder.and(
                    predicates,
                    criteriaBuilder.equal(root.get("status"), searchByStatus)
            );
        }

        if (searchByUser != null && !searchByUser.isEmpty()) {
            String searchTerm = searchByUser.toLowerCase();
            Predicate createdByPredicate = criteriaBuilder.or(
                    criteriaBuilder.like(criteriaBuilder.lower(root.get("createdBy").get("firstName")), "%" + searchTerm + "%"),
                    criteriaBuilder.like(criteriaBuilder.lower(root.get("createdBy").get("lastName")), "%" + searchTerm + "%")
            );
            Predicate affectedToPredicate = criteriaBuilder.or(
                    criteriaBuilder.like(criteriaBuilder.lower(root.get("affectedTo").get("firstName")), "%" + searchTerm + "%"),
                    criteriaBuilder.like(criteriaBuilder.lower(root.get("affectedTo").get("lastName")), "%" + searchTerm + "%")
            );
            predicates = criteriaBuilder.and(predicates, criteriaBuilder.or(createdByPredicate, affectedToPredicate));
        }


        if (minDate != null) {
            predicates = criteriaBuilder.and(predicates, criteriaBuilder.greaterThanOrEqualTo(root.get("deadline"), minDate));
        }
        if (maxDate != null) {
            predicates = criteriaBuilder.and(predicates, criteriaBuilder.lessThanOrEqualTo(root.get("deadline"), maxDate));
        }

        // Ajouter tous les prédicats à la condition where
        criteriaQuery.where(predicates);

        // Exécuter la requête
        return entityManager.createQuery(criteriaQuery).getResultList();
    }

}
