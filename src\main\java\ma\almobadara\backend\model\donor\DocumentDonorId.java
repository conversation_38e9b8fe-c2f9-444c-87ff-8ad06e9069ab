package ma.almobadara.backend.model.donor;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Objects;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class DocumentDonorId implements Serializable {

    private Long donor;
    private Long document;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DocumentDonorId that = (DocumentDonorId) o;
        return Objects.equals(donor, that.donor) && Objects.equals(document, that.document);
    }

    @Override
    public int hashCode() {
        return Objects.hash(donor, document);
    }

}
