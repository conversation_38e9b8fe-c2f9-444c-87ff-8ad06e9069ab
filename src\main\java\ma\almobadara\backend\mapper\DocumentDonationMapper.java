package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.donation.DocumentDonationDTO;
import ma.almobadara.backend.model.donation.DocumentDonation;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")

public interface DocumentDonationMapper {

	DocumentDonationDTO documentDonationModelToDto(DocumentDonation documentDonation);

	Iterable<DocumentDonationDTO> documentDonationListModelToDto(Iterable<DocumentDonation> documentDonations);

	DocumentDonation documentDonationDtoModelToModel(DocumentDonationDTO documentDonation);

	Iterable<DocumentDonation> documentDonationListDtoToModal(Iterable<DocumentDonationDTO> documentDonations);

}
