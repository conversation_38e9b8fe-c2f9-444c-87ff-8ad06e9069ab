package ma.almobadara.backend.dto.beneficiary;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import org.springframework.web.multipart.MultipartFile;

import java.time.Instant;
import java.util.Date;
import java.util.List;


@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Builder
public class BeneficiaryAuditDTO {
    private String code;
    private Boolean independent;
    private String prenom;
    private String nom;
    private String prenomArabe;
    private String nomArabe;
    private Date dateDeNaissance;
    private String sexe;
    private String email;
    private String telephone;
    private String typeIdentite;
    private String numIdentite;
    private String adresse;
    private String adresseArabe;
    private String AnneeAjout;
    private String codeComptable;
    private String profession;
    private String TypeHebergement;
    private String Categorie;
    private List<String> centreEps;
    private String zone;
    private String Ville;
    private String TypeKafalat;

}
