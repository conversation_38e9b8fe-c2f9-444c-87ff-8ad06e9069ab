package ma.almobadara.backend.service.donor;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sun.jdi.InternalException;
import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityNotFoundException;
import jakarta.persistence.OptimisticLockException;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.Audit.AuditApplicationService;
import ma.almobadara.backend.clients.RefFeignClient;
import ma.almobadara.backend.config.Messages;
import ma.almobadara.backend.controller.referentiel.RefController;
import ma.almobadara.backend.dto.CityWithRegionAndCountryDTO;
import ma.almobadara.backend.dto.administration.TagDTO;
import ma.almobadara.backend.dto.aideComplemenatire.AideComplementaireBeneficiareDto;
import ma.almobadara.backend.dto.aideComplemenatire.AideComplementaireDonateurDTO;
import ma.almobadara.backend.dto.beneficiary.BeneficiaryReleveDto;
import ma.almobadara.backend.dto.communs.DocumentDTO;
import ma.almobadara.backend.dto.communs.NoteAndEntityDto;
import ma.almobadara.backend.dto.communs.NoteDTO;
import ma.almobadara.backend.dto.donor.*;
import ma.almobadara.backend.dto.exportentities.DonorExportDTO;
import ma.almobadara.backend.dto.exportentities.ExportFileDTO;
import ma.almobadara.backend.dto.referentiel.*;
import ma.almobadara.backend.dto.service.ServicesDTO;
import ma.almobadara.backend.enumeration.BudgetLineStatus;
import ma.almobadara.backend.enumeration.EntitiesToExport.DonorExportHeaders;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.mapper.*;
import ma.almobadara.backend.model.administration.CacheAdUser;
import ma.almobadara.backend.model.administration.ServiceCollectEps;
import ma.almobadara.backend.model.administration.Tag;
import ma.almobadara.backend.model.administration.Taggable;
import ma.almobadara.backend.model.aideComplemenatire.AideComplementaire;
import ma.almobadara.backend.model.aideComplemenatire.AideComplementaireDonorBeneficiary;
import ma.almobadara.backend.model.beneficiary.HistoryRapport;
import ma.almobadara.backend.model.donation.BudgetLine;
import ma.almobadara.backend.model.donation.Donation;
import ma.almobadara.backend.model.donor.*;
import ma.almobadara.backend.model.service.Services;
import ma.almobadara.backend.model.takenInCharge.TakenInCharge;
import ma.almobadara.backend.model.takenInCharge.TakenInChargeDonor;
import ma.almobadara.backend.model.takenInCharge.TakenInChargeOperation;
import ma.almobadara.backend.repository.DonorYearCountRepository;
import ma.almobadara.backend.repository.administration.TagRepository;
import ma.almobadara.backend.repository.administration.TaggableRepository;
import ma.almobadara.backend.repository.aideComplemenatire.AideComplementaireDonorBeneficiaryRepository;
import ma.almobadara.backend.repository.beneficiary.HistoryRapportRepository;
import ma.almobadara.backend.repository.beneficiary.RapportRepository;
import ma.almobadara.backend.repository.communs.ActionDonorRepository;
import ma.almobadara.backend.repository.communs.DocumentDonorRepository;
import ma.almobadara.backend.repository.communs.NoteDonorRepository;
import ma.almobadara.backend.repository.donation.BudgetLineRepository;
import ma.almobadara.backend.repository.donation.DonationHistoryRepository;
import ma.almobadara.backend.repository.donation.DonationRepository;
import ma.almobadara.backend.repository.donor.*;
import ma.almobadara.backend.repository.services.ServicesRepository;
import ma.almobadara.backend.repository.takenInCharge.TakenInChargeOperationRepository;
import ma.almobadara.backend.repository.takenInCharge.TakenInChargeRepository;
import ma.almobadara.backend.service.communs.ExportService;
import ma.almobadara.backend.service.communs.NoteService;
import ma.almobadara.backend.util.times.TimeWatch;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.ErrorResponseException;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import static ma.almobadara.backend.service.administration.CacheAdUserService.getUsernameFromJwt;
import static ma.almobadara.backend.util.constants.GlobalConstants.*;
import static ma.almobadara.backend.util.strings.HandleSpecialChars.escapeSpecialChars;

@Service
@RequiredArgsConstructor
@Slf4j
public class DonorService {
    private final TakenInChargeRepository takenInChargeRepository;

    private final DonorRepository donorRepository;
    private final TaggableRepository taggableRepository;
    private final TagRepository tagRepository;
    private final DonationRepository donationRepository;
    private final TakenInChargeDonorRepository takenInChargeDonorRepository;
    private final TakenInChargeOperationRepository takenInChargeOperationRepository;
    private final AideComplementaireDonorBeneficiaryRepository aideComplementaireDonorBeneficiaryRepository;
    private final DonorPhysicalMapper donorPhysicalMapper;
    private final DonorYearCountRepository donorYearCountRepository;
    private final DonorPhysicalLanguageCommunicationRepository donorPhysicalLanguageCommunicationRepository;
    private final DonorPhysicalRepository donorPhysicalRepository;
    private final DonorPhysicalCanalCommunicationRepository donorPhysicalCanalCommunicationRepository;
    private final DocumentDonorRepository documentDonorRepository;
    private final MinioService minioService;
    private final DonorMoralMapper donorMoralMapper;
    private final RefController refController;
    private final DonorContactMapper donorContactMapper;
    private final DonorMoralRepository donorMoralRepository;
    private final DonorContactRepository donorContactRepository;
    private final NoteDonorContactRepository noteDonorContactRepository;
    private final DonorContactCanalCommunicationRepository donorContactCanalCommunicationRepository;
    private final DonorContactLanguageCommunicationRepository donorContactLanguageCommunicationRepository;
    private final Messages messages;
    private final RefFeignClient refFeignClient;
    private final AuditApplicationService auditApplicationService;
    private final EntityManager entityManager;
    private final ExportService exportService;
    private final CorrespondenceRepository correspondenceRepository;
    private final NoteDonorRepository noteDonorRepository;
    private final ActionDonorRepository actionDonorRepository;
    private final DonorAnonymeRepository donorAnonymeRepository;
    private final DonorAnonymeMapper donorAnonymeMapper;
    private final ServicesRepository servicesRepository;
    private final BudgetLineRepository budgetLineRepository;
    private final ServicesMapper servicesMapper;
    private final DonationHistoryRepository donationHistoryRepository;
    private final RapportRepository rapportRepository;
    private final NoteService noteService;
    private final HistoryRapportRepository historyRapportRepository;
    private final BeneficiaryMapper beneficiaryMapper;

    @Value("${minio.donorsFolder}")
    private String donorsFolder;

    @Value("${minio.profilePicture.folder}")
    private String folderPathPicture;

    @Value("${minio.profilePicture.abv}")
    private String abv;

    public List<AideComplementaireDonateurDTO> getAideComplementaireForDonor(Long id) {
        List<BudgetLine> budgetLines = budgetLineRepository.findBudgetLineByDonor(id);

        if (budgetLines == null || budgetLines.isEmpty()) {
            return Collections.emptyList(); // ✅ Return empty list if no data found
        }

        return new ArrayList<>(budgetLines.stream()
                .filter(budgetLine ->
                        budgetLine.getAideComplementaire() != null &&
                                budgetLine.getAideComplementaire().getId() != null &&
                                budgetLine.getStatus() != BudgetLineStatus.DISPONIBLE &&
                                ("executer".equals(budgetLine.getAideComplementaire().getStatut()) ||
                                        "cloturer".equals(budgetLine.getAideComplementaire().getStatut())))
                .collect(Collectors.toMap(
                        budgetLine -> budgetLine.getAideComplementaire().getId(),
                        budgetLine -> mapToDto(budgetLine), // ✅ Extracted mapping logic
                        (existing, newDto) -> {
                            existing.setAmount(existing.getAmount() + newDto.getAmount());
                            return existing;
                        }
                )).values());
    }

    // ✅ Extracted mapping logic to avoid redundant null checks
    private AideComplementaireDonateurDTO mapToDto(BudgetLine budgetLine) {
        AideComplementaire aide = budgetLine.getAideComplementaire();
        if (aide == null) {
            return null; // Prevents NullPointerException
        }

        AideComplementaireDonateurDTO dto = new AideComplementaireDonateurDTO();
        dto.setName(aide.getName());
        dto.setAmount(budgetLine.getMontantReserve() != null ? budgetLine.getMontantReserve() : 0.0);
        dto.setId(aide.getId());
        dto.setTypeDonation(budgetLine.getNatureBudgetLine());
        dto.setExecuionDate(aide.getDateExecution());
        dto.setServices(aide.getService());
        dto.setStatut(aide.getStatut());
        return dto;
    }


    public DonorDTO convertAnonymeToPhysique(Long donorAnonymeId, DonorPhysicalDTO donorPhysicalDTO) throws TechnicalException {

        DonorDTO newDonorDTO = addDonorPhysique(donorPhysicalDTO);


        Optional<Donor> donorOptional = donorRepository.findById(donorAnonymeId);
        if (donorOptional.isEmpty() || !(donorOptional.get() instanceof DonorAnonyme)) {
            throw new TechnicalException("Donor Anonyme not found");
        }

        DonorAnonyme donorAnonyme = (DonorAnonyme) donorOptional.get();
        DonorPhysical newDonorPhysical = donorPhysicalRepository.findById(newDonorDTO.getId())
                .orElseThrow(() -> new TechnicalException("New DonorPhysical not found"));


        newDonorPhysical.setBalance(donorAnonyme.getBalance());
        newDonorPhysical.setStatus(donorAnonyme.getStatus());
        newDonorPhysical.setActionStatusId(donorAnonyme.getActionStatusId());
        newDonorPhysical.setCreatedAt(donorAnonyme.getCreatedAt());
        newDonorPhysical.setAnonymeId(donorAnonymeId);
        newDonorPhysical.setDeletedAt(donorAnonyme.getDeletedAt());


        donorPhysicalRepository.save(newDonorPhysical);


        updateDonorRelationships(donorAnonymeId, newDonorPhysical.getId());
        NoteDTO noteDto = NoteDTO.builder()
                .content("Ce donateur "+donorAnonyme.getCode()+" était auparavant anonyme . Il a été identifié en tant que Donateur Physique le " + newDonorPhysical.getCreatedAt().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")) + ".")
                .createdDate(new Date())
                .createdBy(donorPhysicalDTO.getCreatedBy())
                .objet("Identification du Donateur Anonyme")
                .build();
        NoteAndEntityDto noteAndEntityDto=NoteAndEntityDto.builder().entityId(newDonorPhysical.getId()).entityType("donor").noteDto(noteDto).build();
        noteService.addNoteAndAssignToEntity(noteAndEntityDto);

        donorAnonyme.setDeletedAt(LocalDateTime.now());
        donorRepository.save(donorAnonyme);

        return newDonorDTO;
    }
    public DonorDTO convertAnonymeToMoral(Long donorAnonymeId, DonorMoralDTO donorMoralDTO) throws TechnicalException {

        DonorDTO newDonorDTO = addDonorMoral(donorMoralDTO);


        Optional<Donor> donorOptional = donorRepository.findById(donorAnonymeId);
        if (donorOptional.isEmpty() || !(donorOptional.get() instanceof DonorAnonyme)) {
            throw new TechnicalException("Donor Anonyme not found");
        }

        DonorAnonyme donorAnonyme = (DonorAnonyme) donorOptional.get();
        DonorMoral newDonorMoral = donorMoralRepository.findById(newDonorDTO.getId())
                .orElseThrow(() -> new TechnicalException("New DonorMoral not found"));


        newDonorMoral.setBalance(donorAnonyme.getBalance());
        newDonorMoral.setStatus(donorAnonyme.getStatus());
        newDonorMoral.setActionStatusId(donorAnonyme.getActionStatusId());
        newDonorMoral.setCreatedAt(donorAnonyme.getCreatedAt());
        newDonorMoral.setAnonymeId(donorAnonymeId);
        newDonorMoral.setDeletedAt(donorAnonyme.getDeletedAt());


        donorMoralRepository.save(newDonorMoral);


        updateDonorRelationships(donorAnonymeId, newDonorMoral.getId());
        NoteDTO noteDto = NoteDTO.builder()
                .content("Ce donateur "+donorAnonyme.getCode()+" était auparavant anonyme . Il a été identifié en tant que Donateur Moral le " + newDonorMoral.getCreatedAt().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")) + ".")
                .createdDate(new Date())
                .createdBy(donorMoralDTO.getCreatedBy())
                .objet("Identification du Donateur Anonyme")
                .build();
        NoteAndEntityDto noteAndEntityDto=NoteAndEntityDto.builder().entityId(newDonorMoral.getId()).entityType("donor").noteDto(noteDto).build();
        noteService.addNoteAndAssignToEntity(noteAndEntityDto);

        donorAnonyme.setDeletedAt(LocalDateTime.now());
        donorRepository.save(donorAnonyme);

        return newDonorDTO;
    }
    @Transactional
    public void updateDonorRelationships(Long oldDonorId, Long newDonorId) throws TechnicalException {
        try {

            donationRepository.updateDonorId(oldDonorId, newDonorId);
            correspondenceRepository.updateDonorId(oldDonorId, newDonorId);
            aideComplementaireDonorBeneficiaryRepository.updateDonorId(oldDonorId, newDonorId);
            noteDonorRepository.updateDonorId(oldDonorId, newDonorId);
            documentDonorRepository.updateDonorId(oldDonorId, newDonorId);
            takenInChargeDonorRepository.updateDonorId(oldDonorId, newDonorId);
            donationHistoryRepository.updateDonorId(oldDonorId, newDonorId);
            rapportRepository.updateDonorId(oldDonorId, newDonorId);
            historyRapportRepository.updateDonorId(oldDonorId, newDonorId);
            actionDonorRepository.updateDonorId(oldDonorId, newDonorId);
        } catch (OptimisticLockException e) {

            throw new TechnicalException("The donor relationships were modified by another transaction. Please try again.");
        }
    }


    @Transactional
    public DonorDTO addDonorPhysique(DonorPhysicalDTO donorPhysicalDTO) throws TechnicalException {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service addDonorPhysique  {}", donorPhysicalDTO);
        if (donorPhysicalDTO == null) {
            throw new TechnicalException(messages.get(NULL_ENTITY));
        }
        String oldPhysical = null;
        if (donorPhysicalDTO.getId() != null) {

            Optional<DonorPhysical> donorPhysicalOptional = donorPhysicalRepository.findById(donorPhysicalDTO.getId());
            DonorPhysical existingDonorPhysical = donorPhysicalOptional.orElseThrow(() -> new TechnicalException("Donor Physical not found"));

            DonorPhysicalDTO dto=donorPhysicalMapper.donorPhysicalModelToDto(existingDonorPhysical);
            existingDonorPhysical = prepareDonor(existingDonorPhysical,dto);

            List<String> donorPhysicalCanalCommunication= getcanalCommynications(dto);
            List<String> donorPhysicalCanalLanguage= getLanguageCommunications(dto);

            oldPhysical= existingDonorPhysical.toDTOString(donorPhysicalCanalCommunication,donorPhysicalCanalLanguage);
        }


        Iterable<DonorPhysicalCanalCommunication> donorPhysicalCanalCommunicationIterable = donorPhysicalMapper.canalCommunicationDTOToDonorPhysicalCanalCommunication(donorPhysicalDTO.getCanalCommunications());
        Iterable<DonorPhysicalLanguageCommunication> donorPhysicalLanguageCommunicationIterable = donorPhysicalMapper.languageCommunicationDTOToDonorPhysicalLanguageCommunication(donorPhysicalDTO.getLanguageCommunications());
        DonorPhysical donorPhysical = donorPhysicalMapper.donorPhysicalDtoToModel(donorPhysicalDTO);

        String code = generateDonorCode(donorPhysical);
        donorPhysical.setCode(code);

        if (donorPhysicalDTO.getPicture() != null) {
            Instant instant = Instant.now();
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.BASIC_ISO_DATE;
            String donorPath = donorsFolder + donorPhysical.getLastName().toUpperCase() + "-" + donorPhysical.getFirstName().substring(0, 1).toUpperCase() + donorPhysical.getFirstName().substring(1) + "_" + donorPhysical.getCode();
            String fileName = donorPhysical.getLastName().toUpperCase() + "-" + donorPhysical.getFirstName().substring(0, 1).toUpperCase() + donorPhysical.getFirstName().substring(1) + "_" + abv + "_" + instant.atZone(ZoneId.systemDefault()).toLocalDate().format(dateTimeFormatter) + "." + FilenameUtils.getExtension(donorPhysicalDTO.getPicture().getOriginalFilename());
            donorPhysical.setPictureUrl(donorPath + "/" + folderPathPicture + "/" + fileName);
            minioService.WriteToMinIO(donorPhysicalDTO.getPicture(), donorPath + "/" + folderPathPicture + "/", fileName);
        }

        if (donorPhysicalDTO.getId() != null) {
            Optional<Donor> donor = donorRepository.findById(donorPhysicalDTO.getId());
            if (donor.isEmpty()) {
                throw new TechnicalException(messages.get(DONOR_NOT_FOUND));
            }
            donorPhysical.setCreatedAt(donor.get().getCreatedAt());
        }

        if (donorPhysical.getDonorPhysicalLanguageCommunications() != null && donorPhysical.getId() != null) {
            donorPhysicalLanguageCommunicationRepository.deleteAllLanguageByDonorId(donorPhysical.getId());
        }
        donorPhysical.setCodeComptabilite(donorPhysicalDTO.getCodeComptabilite());
        DonorPhysical newDonor2 = donorPhysicalRepository.save(donorPhysical);
        DonorPhysical finalSavedPhysique = newDonor2;
        taggableRepository.deleteAllByTaggableIdAndTaggableType(newDonor2.getId(),"donor");

        if(donorPhysicalDTO.getTags()!=null){
            donorPhysicalDTO.getTags().forEach(tagDTO -> {
                Taggable taggable = new Taggable();
                Optional<Tag> tag=tagRepository.findById(tagDTO.getId());
                if(tag.isPresent()){
                    Tag tag1=tag.get();
                    taggable.setTag(tag1);
                    taggable.setTaggableId(finalSavedPhysique.getId());
                    taggable.setTaggableType("donor");
                    taggableRepository.save(taggable);
                }
            });
        }

        newDonor2 = prepareDonor(newDonor2,donorPhysicalDTO);

        List<String> donorPhysicalCanalCommunication= getcanalCommynications(donorPhysicalDTO);
        List<String> donorPhysicalCanalLanguage= getLanguageCommunications(donorPhysicalDTO);
        String newDonorPhysical=newDonor2.toDTOString(donorPhysicalCanalCommunication,donorPhysicalCanalLanguage);

        saveLanguageCommunications(donorPhysicalLanguageCommunicationIterable, donorPhysical);
        if (donorPhysical.getDonorPhysicalCanalCommunication() != null && donorPhysical.getId() != null) {
            donorPhysicalCanalCommunicationRepository.deleteAllCanalByDonorId(donorPhysical.getId());
        }
        saveCanalCommunications(donorPhysicalCanalCommunicationIterable, donorPhysical);

        DonorPhysicalDTO donorDTO = donorPhysicalMapper.donorPhysicalModelToDto(newDonor2);

        if (donorPhysicalDTO.getId() != null) {
            auditApplicationService.audit("Modification des informations du donateur physique : "+donorPhysical.getCode(), getUsernameFromJwt(), "Update Physical Donor",
                    oldPhysical, newDonorPhysical, DONATEUR, UPDATE);
        } else {
            auditApplicationService.audit("Ajout d'un nouveau donateur physique : "+donorPhysical.getCode(), getUsernameFromJwt(), "Add Physical Donor",
                    null, newDonorPhysical, DONATEUR, CREATE);
        }

        log.debug("End service addDonorPhysique with code: {} , took {}", donorPhysical.getCode(), watch.toMS());
        return donorDTO;
    }

    public List<String> getcanalCommynications(DonorPhysicalDTO donorPhysicalDTO){
        List<String> canalCommunicationsIds = new ArrayList<>();

        var canalCommunicationDTOList = donorPhysicalDTO.getCanalCommunications();
        if (canalCommunicationDTOList != null && !canalCommunicationDTOList.isEmpty()) {

            for (CanalCommunicationDTO dto : canalCommunicationDTOList) {
                CanalCommunicationDTO canalCommunicationDTO = refFeignClient.getMetCanalCommunication(dto.getId());
                canalCommunicationsIds.add(canalCommunicationDTO.getName());
            }

        }
        return canalCommunicationsIds;
    }

    public List<String> getLanguageCommunications(DonorPhysicalDTO donorPhysicalDTO){

        List<String> LanguageCommunicationIds = new ArrayList<>();
        var LanguageCommunicationDTOList = donorPhysicalDTO.getLanguageCommunications();
        if (LanguageCommunicationDTOList != null && !LanguageCommunicationDTOList.isEmpty()) {
            for (LanguageCommunicationDTO dto : LanguageCommunicationDTOList) {
                LanguageCommunicationDTO languageCommunicationDTO = refFeignClient.getParLanguageCommunication(dto.getId());
                LanguageCommunicationIds.add(languageCommunicationDTO.getName());
            }
        }

        return LanguageCommunicationIds;
    }

    public DonorPhysical prepareDonor(DonorPhysical existingDonorPhysical,DonorPhysicalDTO donorPhysicalDTO) {
        if (donorPhysicalDTO.getTypeIdentity() != null && donorPhysicalDTO.getTypeIdentity().getId() != null) {
            TypeIdentityDTO typeIdentityDTO = refFeignClient.getParTypeIdentity(donorPhysicalDTO.getTypeIdentity().getId());
            existingDonorPhysical.setIdentity(typeIdentityDTO);
        }
        else{
            existingDonorPhysical.setIdentity(null);
        }
        if (donorPhysicalDTO.getProfession() != null && donorPhysicalDTO.getProfession().getId() != null) {
            ProfessionDTO professionDTO = refFeignClient.getMetProfession(donorPhysicalDTO.getProfession().getId());
            existingDonorPhysical.setProfession(professionDTO);
        }
        else {
            existingDonorPhysical.setProfession(null);
        }

        if (donorPhysicalDTO.getStatus() != null && donorPhysicalDTO.getStatus().getId() != null) {
            DonorStatusDTO donorStatusDTO = refFeignClient.getParDonorStatus(donorPhysicalDTO.getStatus().getId());
            existingDonorPhysical.setStatus(donorStatusDTO);
        }
        else {
            existingDonorPhysical.setStatus(null);
        }

        return existingDonorPhysical;

    }



    public DonorDTO addDonorAnonyme(DonorAnonymeDTO donorAnonymeDTO) {
        DonorAnonyme donorAnonyme;

        String donorAnonymeString = null;
        // Check if an ID is provided for updating
        if (donorAnonymeDTO.getId() != null) {
            // Find the existing donor
            donorAnonyme = donorAnonymeRepository.findById(donorAnonymeDTO.getId())
                    .orElseThrow(() -> new EntityNotFoundException("Donor not found"));

            donorAnonyme =prepareDonorAnonyme(donorAnonymeMapper.toDto(donorAnonyme),donorAnonyme);
            donorAnonymeString = donorAnonyme.toDtoString();
            // Preserve the existing code
            String existingCode = donorAnonyme.getCode();
            // i have to store the createdAt date to keep it
            LocalDateTime createdAt = donorAnonyme.getCreatedAt();

            // Update fields from DTO
            donorAnonymeMapper.updateModelFromDto(donorAnonymeDTO, donorAnonyme);

            // Restore the preserved code to ensure it's not overwritten
            donorAnonyme.setCode(existingCode);
            donorAnonyme.setCreatedAt(createdAt);
            donorAnonyme.setLabel(donorAnonymeDTO.getLabel());

            // Update donor status if provided
            if (donorAnonymeDTO.getStatus() != null && donorAnonymeDTO.getStatus().getId() != null) {
                DonorStatusDTO donorStatusDTO = refFeignClient.getParDonorStatus(donorAnonymeDTO.getStatus().getId());
                donorAnonyme.setStatus(donorStatusDTO);
            }
        } else {
            // Convert the DTO to a new model for adding
            donorAnonyme = donorAnonymeMapper.toModel(donorAnonymeDTO);

            donorAnonyme.setLabel(donorAnonymeDTO.getLabel());
            // Generate new donor code for the new donor
            String code = generateDonorCode(donorAnonyme);
            donorAnonyme.setCode(code);
        }

        // Save the entity in the database
        DonorAnonyme savedDonorAnonyme = donorAnonymeRepository.save(donorAnonyme);

        DonorAnonyme finalSavedDonorAnonyme = savedDonorAnonyme;
        taggableRepository.deleteAllByTaggableIdAndTaggableType(savedDonorAnonyme.getId(),"donor");

        if(donorAnonymeDTO.getTags()!=null){
            donorAnonymeDTO.getTags().forEach(tagDTO -> {
                Taggable taggable = new Taggable();
                Optional<Tag> tag=tagRepository.findById(tagDTO.getId());
                if(tag.isPresent()){
                    Tag tag1=tag.get();
                    taggable.setTag(tag1);
                    taggable.setTaggableId(finalSavedDonorAnonyme.getId());
                    taggable.setTaggableType("donor");
                    taggableRepository.save(taggable);
                }
            });
        }

        savedDonorAnonyme =prepareDonorAnonyme(donorAnonymeDTO,savedDonorAnonyme);
        String newDonorAnonymeString = savedDonorAnonyme.toDtoString();

        if(donorAnonymeDTO.getId()!=null){
            auditApplicationService.audit("Modification des informations du donateur Anonyme : "+donorAnonyme.getCode(), getUsernameFromJwt(), "Update moral Donor",
                    donorAnonymeString, newDonorAnonymeString, DONATEUR, UPDATE);
        }
        else{
            auditApplicationService.audit("Ajout d'un donateur Anonyme : "+donorAnonyme.getCode(), getUsernameFromJwt(), "add moral Donor",
                    null, newDonorAnonymeString, DONATEUR, CREATE);
        }

        return donorAnonymeMapper.toDto(savedDonorAnonyme);
    }


    DonorAnonyme prepareDonorAnonyme(DonorAnonymeDTO donorAnonymeDTO,DonorAnonyme donorAnonyme) {
        if (donorAnonymeDTO.getStatus() != null && donorAnonymeDTO.getStatus().getId() != null) {
            DonorStatusDTO donorStatusDTO = refFeignClient.getParDonorStatus(donorAnonymeDTO.getStatus().getId());
            donorAnonyme.setStatus(donorStatusDTO);
        }
        return donorAnonyme;
    }

    // getByIddonor anonyme

    public DonorAnonymeDTO getDonorAnonymeById(Long id) {
        // Récupérer l'entité à partir de l'identifiant
        Optional<DonorAnonyme> donorAnonymeOptional = donorAnonymeRepository.findById(id);

        // Vérifier si l'entité existe
        if (donorAnonymeOptional.isEmpty()) {
            // Retourner une exception si l'entité n'existe pas
            throw new IllegalArgumentException("Donor anonyme not found");
        }
        DonorAnonymeDTO donorAnonyme = donorAnonymeMapper.toDto(donorAnonymeOptional.get());
        if(donorAnonyme.getStatus().getId() != null){
            DonorStatusDTO donorStatusDTO = refFeignClient.getParDonorStatus(donorAnonyme.getStatus().getId());
            donorAnonyme.setStatus(donorStatusDTO);
        }
        return donorAnonyme;
    }


    private Pageable createPageable(Integer page, Integer size) {
        log.debug("Page number {}", page);
        Sort.Direction sortDirection = Sort.Direction.DESC;
        String sortBy = "createdAt";
        Sort sort = Sort.by(sortDirection, sortBy);
        return PageRequest.of(page, size, sort);
    }

    private String generateDonorCode(Donor donor) {
        if (donor.getCode() == null ) {
            return generateNewDonorCode(donor);
        }
        return donor.getCode();
    }


    private String generateNewDonorCode(Donor donor) {
        // actual date
        String date = LocalDateTime.now().toString();
        if(donor.getFirstDonationYear() !=null ){
            // set the current year as the first donation year
            date = donor.getFirstDonationYear();
        }
        Optional<DonorYearCount> donorYearCount = donorYearCountRepository.findByYear(date.substring(0, 4));
        Long count = donorYearCount.map(dyc -> dyc.getCount() + 1L).orElse(1L);

        // Generate base code using the year and incremented count
        String code = "D" + date.substring(0, 4) + String.format("%05d", count);

        // Update the donorYearCount repository with the new count
        if (donorYearCount.isPresent()) {
            donorYearCount.get().setCount(count);
            donorYearCountRepository.save(donorYearCount.get());
        } else {
            DonorYearCount newDonorYearCount = new DonorYearCount();
            newDonorYearCount.setCount(count);
            newDonorYearCount.setYear(donor.getFirstDonationYear().substring(0, 4));
            donorYearCountRepository.save(newDonorYearCount);
        }

        // Append appropriate suffix based on donor type (physical, moral, or anonymous)
        if (donor instanceof DonorPhysical) {
            return code + "PXX";  // Physical donor
        } else if (donor instanceof DonorMoral) {
            return code + "MXX";  // Moral donor
        } else if (donor instanceof DonorAnonyme) {
            return code + "AXX";  // Anonymous donor
        }

        return code;  // Fallback in case no type matches
    }


    public void saveLanguageCommunications(Iterable<DonorPhysicalLanguageCommunication> donorPhysicalLanguageCommunicationIterable, DonorPhysical savedDonorPhysical) {
        if (donorPhysicalLanguageCommunicationIterable != null) {
            Set<DonorPhysicalLanguageCommunication> donorPhysicalLanguageCommunications = StreamSupport.stream(donorPhysicalLanguageCommunicationIterable.spliterator(), false).collect(Collectors.toSet());

            if (!donorPhysicalLanguageCommunications.isEmpty()) {
                for (DonorPhysicalLanguageCommunication donorPhysicalLanguageCommunication : donorPhysicalLanguageCommunications) {
                    donorPhysicalLanguageCommunication.setDonorPhysical(savedDonorPhysical);
                    donorPhysicalLanguageCommunicationRepository.save(donorPhysicalLanguageCommunication);
                }
                savedDonorPhysical.setDonorPhysicalLanguageCommunications(donorPhysicalLanguageCommunications);
            }
        } else {
            donorPhysicalLanguageCommunicationRepository.deleteAllLanguageByDonorId(savedDonorPhysical.getId());
            savedDonorPhysical.setDonorPhysicalLanguageCommunications(Collections.emptySet());
        }
    }


    public void saveCanalCommunications(Iterable<DonorPhysicalCanalCommunication> donorPhysicalCanalCommunicationIterable, DonorPhysical savedDonorPhysical) throws InternalException, ErrorResponseException {
        if (donorPhysicalCanalCommunicationIterable == null) {
            donorPhysicalCanalCommunicationRepository.deleteAllCanalByDonorId(savedDonorPhysical.getId());
            savedDonorPhysical.setDonorPhysicalCanalCommunication(null);
        } else {
            Set<DonorPhysicalCanalCommunication> donorPhysicalCanalCommunications = StreamSupport.stream(donorPhysicalCanalCommunicationIterable.spliterator(), false).collect(Collectors.toSet());

            if (!donorPhysicalCanalCommunications.isEmpty()) {
                for (DonorPhysicalCanalCommunication donorPhysicalCanalCommunication : donorPhysicalCanalCommunications) {
                    donorPhysicalCanalCommunication.setDonorPhysical(savedDonorPhysical);
                    donorPhysicalCanalCommunicationRepository.save(donorPhysicalCanalCommunication);
                }
                savedDonorPhysical.setDonorPhysicalCanalCommunication(donorPhysicalCanalCommunications);
            }
        }
    }

    public DonorDTO getDonorById(Long idDonor) throws TechnicalException {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service getDonorById {}", idDonor);
        Optional<Donor> optionalDonor = donorRepository.findById(idDonor);
        if (optionalDonor.isEmpty()) {
            throw new TechnicalException(messages.get(DONOR_NOT_FOUND));
        }

        Donor donor = optionalDonor.get();
        if (donor instanceof DonorPhysical) {
            log.debug("End service getDonorById of type DonorPhysical {}, took '{}'", idDonor, watch.toMS());
            return processDonorPhysical((DonorPhysical) donor);

        } else if (donor instanceof DonorMoral) {
            log.debug("End service getDonorById of type DonorMoral {}, took '{}'", idDonor, watch.toMS());
            return processDonorMoral((DonorMoral) donor);

        } else if (donor instanceof DonorAnonyme donorAnonyme) {
            log.debug("End service getDonorById of type DonorAnonyme {}, took '{}'", idDonor, watch.toMS());
            DonorAnonymeDTO donorAnonymeDTO = donorAnonymeMapper.toDto(donorAnonyme);
            if (donorAnonymeDTO.getStatus() != null && donorAnonymeDTO.getStatus().getId() != null) {
                DonorStatusDTO donorStatusDTO = refFeignClient.getParDonorStatus(donorAnonymeDTO.getStatus().getId());
                donorAnonymeDTO.setStatus(donorStatusDTO);
            }

            donorAnonyme=prepareDonorAnonyme(donorAnonymeDTO,donorAnonyme);
             String donorAnonymeString=donorAnonyme.toDtoString("Anonyme",donorAnonyme.getName());

            auditApplicationService.audit("Consultation du donateur Anonyme : "+donorAnonyme.getCode(), getUsernameFromJwt(), "get donateur anonyme",
                    donorAnonymeString, null, DONATEUR, CONSULTATION);

            if(donorAnonymeDTO.getDonations()!=null && !donorAnonymeDTO.getDonations().isEmpty()){
                donorAnonymeDTO.getDonations().forEach(donationDTO -> {
                    if(donationDTO.getCanalDonation() !=null && donationDTO.getCanalDonation().getId() !=null){
                        CanalDonationDTO canalDonationDTO = refFeignClient.getMetCanalDonation(donationDTO.getCanalDonation().getId());
                        donationDTO.setCanalDonation(canalDonationDTO);
                    }
                });
            }
            if(donorAnonymeDTO.getTakenInChargeDonors()!=null){
                donorAnonymeDTO.getTakenInChargeDonors().forEach(takenInChargeDonorDTO -> {
                    if(takenInChargeDonorDTO.getTakenInCharge()!=null  && takenInChargeDonorDTO.getTakenInCharge().getId()!=null) {
                        Optional<TakenInCharge> takenInCharge = takenInChargeRepository.findById(takenInChargeDonorDTO.getTakenInCharge().getId());
                        if (takenInCharge.isPresent()) {
                            ServicesDTO servicesDTO = servicesMapper.toDto(takenInCharge.get().getService());
                            takenInChargeDonorDTO.getTakenInCharge().setServices(servicesDTO);
                        }
                    }
                });


            }
            updateFinancialsForDonor(donorAnonyme, donorAnonymeDTO);
            donorAnonymeDTO.setLabel(donorAnonyme.getLabel());
            List<Taggable> taggables=taggableRepository.findByTaggableIdAndTaggableType(donorAnonymeDTO.getId(),"donor");
            List<TagDTO> tags = new ArrayList<>();
            for (Taggable taggable : taggables) {
                TagDTO tagDTO = new TagDTO();
                tagDTO.setId(taggable.getTag().getId());
                tagDTO.setName(taggable.getTag().getName());
                tagDTO.setColor(taggable.getTag().getColor());
                tags.add(tagDTO);
            }
            donorAnonymeDTO.setTags(tags);
            return donorAnonymeDTO;

        } else {
            throw new TechnicalException(messages.get(TYPE_IDENTITY_NOT_FOUND));

        }

    }

    public DonorDTO processDonorPhysical(DonorPhysical donorPhysical) throws TechnicalException {
        DonorPhysicalDTO donorPhysicalDTO = donorPhysicalMapper.donorPhysicalModelToDto(donorPhysical);

        processCommonAttributes(donorPhysicalDTO);
        donorPhysicalDTO.setAnonymeId(donorPhysical.getAnonymeId());
        if (donorPhysicalDTO.getProfession().getId() != null) {
            donorPhysicalDTO.getProfession().setName(refFeignClient.getMetProfession(donorPhysicalDTO.getProfession().getId()).getName());
        }


        // Check if the picture URL is not null
        if (donorPhysical.getPictureUrl() != null) {

            try {
                // Retrieve the image from MinIO
                byte[] imageData = minioService.ReadFromMinIO(donorPhysical.getPictureUrl(),null);
                String base64Image = Base64.getEncoder().encodeToString(imageData);
                // Set the base64 string to the DonorPhysicalDTO object
                donorPhysicalDTO.setPicture64(base64Image);
            } catch (TechnicalException ex) {
                ex.printStackTrace();
            }
        }
        // we il boucle over each taken in charge to get the services
        if(donorPhysicalDTO.getTakenInChargeDonors()!=null){
            donorPhysicalDTO.getTakenInChargeDonors().forEach(takenInChargeDonorDTO -> {
                if(takenInChargeDonorDTO.getTakenInCharge()!=null  && takenInChargeDonorDTO.getTakenInCharge().getId()!=null) {
                    Optional<TakenInCharge> takenInCharge = takenInChargeRepository.findById(takenInChargeDonorDTO.getTakenInCharge().getId());
                    if (takenInCharge.isPresent()) {
                        ServicesDTO servicesDTO = servicesMapper.toDto(takenInCharge.get().getService());
                        takenInChargeDonorDTO.getTakenInCharge().setServices(servicesDTO);
                    }
                }
            });


                }

      if(donorPhysicalDTO.getDonations()!=null){
          donorPhysicalDTO.getDonations().forEach(donationDTO -> {
                  if(donationDTO.getCanalDonation() !=null && donationDTO.getCanalDonation().getId() !=null){
                      CanalDonationDTO canalDonationDTO = refFeignClient.getMetCanalDonation(donationDTO.getCanalDonation().getId());
                        donationDTO.setCanalDonation(canalDonationDTO);
                  }
            });
        }
        updateFinancialsForDonor(donorPhysical, donorPhysicalDTO);
        //Audit

        donorPhysical = prepareDonor(donorPhysical,donorPhysicalDTO);

        List<String> donorPhysicalCanalCommunication= getcanalCommynications(donorPhysicalDTO);
        List<String> donorPhysicalCanalLanguage= getLanguageCommunications(donorPhysicalDTO);

        String oldPhysical= donorPhysical.toDtoString("Physique",donorPhysical.getFirstName()+" "+donorPhysical.getLastName());


        auditApplicationService.audit("Consultation du donateur physique : "+donorPhysical.getCode(), getUsernameFromJwt(), "Add Physical Donor",
                oldPhysical, null, DONATEUR, CONSULTATION);
        List<Taggable> taggables=taggableRepository.findByTaggableIdAndTaggableType(donorPhysical.getId(),"donor");
        List<TagDTO> tags = new ArrayList<>();
        for (Taggable taggable : taggables) {
            TagDTO tagDTO = new TagDTO();
            tagDTO.setId(taggable.getTag().getId());
            tagDTO.setColor(taggable.getTag().getColor());
            tagDTO.setName(taggable.getTag().getName());
            tags.add(tagDTO);
        }
        donorPhysicalDTO.setTags(tags);
        return donorPhysicalDTO;
    }



    private DonorDTO processDonorMoral(DonorMoral donorMoral) throws TechnicalException {
        DonorMoralDTO donorMoralDTO = donorMoralMapper.donorMoralModelToDto(donorMoral);
        processCommonAttributes(donorMoralDTO);
        donorMoralDTO.setAnonymeId(donorMoral.getAnonymeId());

        if (donorMoral.getLogoUrl() != null) {

            try {
                // Retrieve the image from MinIO
                byte[] imageData = minioService.ReadFromMinIO(donorMoral.getLogoUrl(),null);
                String base64Image = Base64.getEncoder().encodeToString(imageData);
                // Set the base64 string to the DonorPhysicalDTO object
                donorMoralDTO.setLogo64(base64Image);
            } catch (TechnicalException ex) {
                ex.printStackTrace();
            }
        }
        if(donorMoralDTO.getDonations()!=null && !donorMoralDTO.getDonations().isEmpty()){
            donorMoralDTO.getDonations().forEach(donationDTO -> {
                if(donationDTO.getCanalDonation() !=null && donationDTO.getCanalDonation().getId() !=null){
                    CanalDonationDTO canalDonationDTO = refFeignClient.getMetCanalDonation(donationDTO.getCanalDonation().getId());
                    donationDTO.setCanalDonation(canalDonationDTO);
                }
            });
        }
        updateFinancialsForDonor(donorMoral, donorMoralDTO);

        donorMoralDTO.getDonorContacts().stream()
                .filter(donorContactDTO -> !donorContactDTO.getCanalCommunications().isEmpty())
                .flatMap(donorContactDTO -> donorContactDTO.getCanalCommunications().stream())
                .filter(canal -> canal.getId() != null)
                .forEach(canal -> {
                    CanalCommunicationDTO canalCommunicationDTO = refFeignClient.getMetCanalCommunication(canal.getId());
                    canal.setName(canalCommunicationDTO.getName());
                });

        donorMoralDTO.getDonorContacts().stream()
                .filter(donorContactDTO -> !donorContactDTO.getLanguageCommunications().isEmpty())
                .flatMap(donorContactDTO -> donorContactDTO.getLanguageCommunications().stream())
                .filter(langue -> langue.getId() != null)
                .forEach(langue -> {
                    LanguageCommunicationDTO languageCommunicationDTO = refFeignClient.getParLanguageCommunication(langue.getId());
                    langue.setName(languageCommunicationDTO.getName());
                });

        if (donorMoralDTO.getActivitySector() != null && donorMoralDTO.getActivitySector().getId() != null) {
            ActivitySectorDTO updatedActivitySector = refFeignClient.getMetActivitySector(donorMoralDTO.getActivitySector().getId());
            donorMoralDTO.getActivitySector().setName(updatedActivitySector.getName());
        }

        donorMoralDTO.getDonorContacts().stream()
                .filter(donorContactDTO -> donorContactDTO.getDonorContactFunction() != null)
                .forEach(donorContactDTO -> {
                    DonorContactFunctionDTO donorContactFunction = donorContactDTO.getDonorContactFunction();
                    if (donorContactFunction.getId() != null) {
                        DonorContactFunctionDTO contactFunctionDTO = refFeignClient.getMetDonorContactFunction(donorContactFunction.getId());
                        donorContactFunction.setName(contactFunctionDTO.getName());
                    }
                });
        if(donorMoralDTO.getTakenInChargeDonors()!=null){
            donorMoralDTO.getTakenInChargeDonors().forEach(takenInChargeDonorDTO -> {
                if(takenInChargeDonorDTO.getTakenInCharge()!=null  && takenInChargeDonorDTO.getTakenInCharge().getId()!=null) {
                    Optional<TakenInCharge> takenInCharge = takenInChargeRepository.findById(takenInChargeDonorDTO.getTakenInCharge().getId());
                    if (takenInCharge.isPresent()) {
                        ServicesDTO servicesDTO = servicesMapper.toDto(takenInCharge.get().getService());
                        takenInChargeDonorDTO.getTakenInCharge().setServices(servicesDTO);
                    }
                }
            });
        }

        DonorMoral donorMoralaudit=donorMoralMapper.donorMoralDtoToModel(donorMoralDTO);
        donorMoralaudit=prepareDonorMoral(donorMoralaudit,donorMoralDTO);
        String DonorMoralString = donorMoralaudit.toDtoString("Moral",donorMoral.getCompany());

        auditApplicationService.audit("Consultation du donateur moral : "+donorMoral.getCode(), getUsernameFromJwt(), "Liste des familles",
                DonorMoralString, null, DONATEUR, CONSULTATION);
        List<Taggable> taggables=taggableRepository.findByTaggableIdAndTaggableType(donorMoral.getId(),"donor");
        List<TagDTO> tags = new ArrayList<>();
        for (Taggable taggable : taggables) {
            TagDTO tagDTO = new TagDTO();
            tagDTO.setColor(taggable.getTag().getColor());
            tagDTO.setId(taggable.getTag().getId());
            tagDTO.setName(taggable.getTag().getName());
            tags.add(tagDTO);
        }
        donorMoralDTO.setTags(tags);
        return donorMoralDTO;
    }

    private void updateFinancialsForDonor(Donor donor, DonorDTO donorDTO) {
        Double availableBalanceInKafalat = donorRepository.findAvailableBalanceByDonorAndType(donor.getId(), "Kafalat");
        donorDTO.setAvailableBalanceInKafalat(availableBalanceInKafalat);

        Double availableBalanceInAideComplementaire = donorRepository.findAvailableBalanceByDonorAndType(donor.getId(), "Aides Complémentaires");
        donorDTO.setAvailableBalanceInAideComplementaire(availableBalanceInAideComplementaire);

        Double allAvailableBalance = donorRepository.findAvailableBalanceByDonor(donor.getId());
        donorDTO.setAvailableBalance(allAvailableBalance);

        Double totalDonated = donorRepository.findTotalDonatedByDonor(donor.getId());
        donorDTO.setTotalDonated(totalDonated);

        Double totalExecuted = donorRepository.findExecutedBalanceByDonor(donor.getId());
        donorDTO.setTotalExecuted(totalExecuted);

        Double totalReserved = donorRepository.findReservedBalanceByDonor(donor.getId());
        donorDTO.setTotalReserved(totalReserved);
    }

    private void processCommonAttributes(DonorDTO donorDTO) throws TechnicalException {
        List<DocumentDTO> documentDonorDTOs = donorDTO.getDocumentDonors();
        donorDTO.setDocumentDonors(documentDonorDTOs);

        DonorStatusDTO statusDTO = donorDTO.getStatus();
        DonorStatusDTO fullStatusDTO = refController.getParDonorStatus(statusDTO.getId()).getBody();
        donorDTO.setStatus(fullStatusDTO);

        CityDTO cityDTO = donorDTO.getCity();
        if (cityDTO.getId() == null) {
            throw new TechnicalException(messages.get(CITY_NOT_FOUND));
        }
        CityDTO fullCityDTO = refController.getParCity(cityDTO.getId()).getBody();
        donorDTO.setCity(fullCityDTO);

        CityWithRegionAndCountryDTO fullCountryDto = refController.getCityWithRegionAndCountry(cityDTO.getId()).getBody();
        donorDTO.setInfo(fullCountryDto);

        // Language and Canal communications processing can be extracted into separate methods
        processLanguageCommunications(donorDTO);
        processCanalCommunications(donorDTO);
    }

    private void processLanguageCommunications(DonorDTO donorDTO) {
        if (donorDTO instanceof DonorPhysicalDTO) {
            List<LanguageCommunicationDTO> languageCommunicationDTOs1 = ((DonorPhysicalDTO) donorDTO).getLanguageCommunications();
            List<LanguageCommunicationDTO> languageCommunicationDTOs2 = new ArrayList<>();
            if (languageCommunicationDTOs1 != null) {
                for (LanguageCommunicationDTO languageCommunicationDTO : languageCommunicationDTOs1) {
                    if (languageCommunicationDTO.getId() != null) {
                        LanguageCommunicationDTO fullLanguageCommunicationDTO = refController.getParLanguageCommunication(languageCommunicationDTO.getId()).getBody();
                        languageCommunicationDTOs2.add(fullLanguageCommunicationDTO);
                    }
                }
            }
            ((DonorPhysicalDTO) donorDTO).setLanguageCommunications(languageCommunicationDTOs2);
        }
    }

    private void processCanalCommunications(DonorDTO donorDTO) {
        if (donorDTO instanceof DonorPhysicalDTO) {
            List<CanalCommunicationDTO> canalCommunicationDTOs1 = ((DonorPhysicalDTO) donorDTO).getCanalCommunications();
            List<CanalCommunicationDTO> canalCommunicationDTOs2 = new ArrayList<>();
            if (canalCommunicationDTOs1 != null) {
                for (CanalCommunicationDTO canalCommunicationDTO : canalCommunicationDTOs1) {
                    if (canalCommunicationDTO.getId() != null) {
                        CanalCommunicationDTO fullCanalCommunicationDTO = refController.getMetCanalCommunication(canalCommunicationDTO.getId()).getBody();
                        canalCommunicationDTOs2.add(fullCanalCommunicationDTO);
                    }
                }
            }
            ((DonorPhysicalDTO) donorDTO).setCanalCommunications(canalCommunicationDTOs2);
        }
    }


    @Transactional
    public void deleteDonor(Long idDonor) throws TechnicalException {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service deleteDonor {}", idDonor);

        Donor donor = donorRepository.findById(idDonor)
                .orElseThrow(() -> new IllegalArgumentException("Donateur introuvable"));




        if (!donor.getTakenInChargeDonors().isEmpty()) {
            throw new IllegalStateException("Veuillez supprimer d'abord les prises en charge liées au donateur sélectionné.");
        }

        if (!donor.getDonations().isEmpty()) {
            throw new IllegalStateException("Veuillez d'abord supprimer les dons associés.");
        }

        if (donor.getDeletedAt() == null) {
            donor.setDeletedAt(LocalDateTime.now());
            donorRepository.save(donor);
        }

        List<DocumentDonor> documentDonors = (List<DocumentDonor>) documentDonorRepository.findByDonor(donor);
        for (DocumentDonor doc : documentDonors) {
            minioService.DeleteFromMinIo(doc.getDocument().getFileUrl());
        }

        if(donor instanceof DonorAnonyme  ){
            auditApplicationService.audit("Suppression du donateur anonyme : "+donor.getCode() , getUsernameFromJwt(), "Delete Donor",
                    donor.toDtoString("Anonyme",((DonorAnonyme) donor).getName()), null, DONATEUR, DELETE);
        }
        else if(donor instanceof DonorPhysical){
            auditApplicationService.audit("Suppression du donateur Physique : "+donor.getCode() , getUsernameFromJwt(), "Delete Donor",
                    donor.toDtoString("Physical",(((DonorPhysical) donor).getFirstName())+" "+((DonorPhysical) donor).getLastName()), null, DONATEUR, DELETE);
        }
        else if(donor instanceof DonorMoral){
            auditApplicationService.audit("Suppression du donateur Moral : "+donor.getCode() , getUsernameFromJwt(), "Delete Donor",
                    donor.toDtoString("Moral",((DonorMoral) donor).getCompany()), null, DONATEUR, DELETE);
        }


        log.debug("End service deleteDonor {}, took '{}'", idDonor, watch.toMS());
    }


    @Transactional
    public DonorDTO addDonorMoral(DonorMoralDTO donorMoralDTO) throws TechnicalException {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service addDonorMoral {}", donorMoralDTO);

        String oldDonorMoralString=null;
        String newcomapny=donorMoralDTO.getCompany();
        String oldcompany=null;
        List<DonorContactDTO> oldDonorContactsDTOList = List.of();
        if (donorMoralDTO.getId() != null) {
            Optional<DonorMoral> donorMoralOptional = donorMoralRepository.findById(donorMoralDTO.getId());
            DonorMoral existingDonorMoral = donorMoralOptional.orElseThrow(() -> new TechnicalException("Donor Moral not found"));

            DonorMoralDTO donorMoralDTO1=donorMoralMapper.donorMoralModelToDto(existingDonorMoral);
            existingDonorMoral=prepareDonorMoral(existingDonorMoral,donorMoralDTO1);
            oldDonorContactsDTOList = donorMoralDTO1.getDonorContacts();
            oldcompany=existingDonorMoral.getCompany();
            oldDonorMoralString = existingDonorMoral.toDtoString();


        }

        DonorMoral donorMoral = donorMoralMapper.donorMoralDtoToModel(donorMoralDTO);
        String code = generateDonorCode(donorMoral);
        donorMoral.setCode(code);

        if (donorMoralDTO.getLogo() != null) {
            Instant instant = Instant.now();
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.BASIC_ISO_DATE;
            String donorPath = donorsFolder + donorMoral.getCompany().toUpperCase() + "_" + donorMoral.getCode();
            String fileName = donorMoral.getCompany().toUpperCase() + "_" + abv + "_" + instant.atZone(ZoneId.systemDefault()).toLocalDate().format(dateTimeFormatter) + "." + FilenameUtils.getExtension(donorMoralDTO.getLogo().getOriginalFilename());
            donorMoral.setLogoUrl(donorPath + "/" + folderPathPicture + "/" + fileName);
            minioService.WriteToMinIO(donorMoralDTO.getLogo(), donorPath + "/" + folderPathPicture + "/", fileName);
        }
        if (donorMoralDTO.getId() != null) {
            Optional<Donor> donor = donorRepository.findById(donorMoralDTO.getId());
            if (donor.isEmpty()) {
                throw new TechnicalException(messages.get(DONOR_NOT_FOUND));
            }
            donorMoral.setCreatedAt(donor.get().getCreatedAt());
        }
        donorMoral.setCodeComptabilite(donorMoralDTO.getCodeComptabilite());

        DonorMoral newDonor = donorMoralRepository.save(donorMoral);
        DonorMoral finalNewDonor = newDonor;
        taggableRepository.deleteAllByTaggableIdAndTaggableType(newDonor.getId(),"donor");
        if(donorMoralDTO.getTags()!=null){
            donorMoralDTO.getTags().forEach(tagDTO -> {
                Taggable taggable = new Taggable();
                Optional<Tag> tag=tagRepository.findById(tagDTO.getId());
                if(tag.isPresent()){
                    Tag tag1=tag.get();
                    taggable.setTag(tag1);
                    taggable.setTaggableId(finalNewDonor.getId());
                    taggable.setTaggableType("donor");
                    taggableRepository.save(taggable);
                }
            });
        }

        DonorDTO donorDTO = donorMoralMapper.donorMoralModelToDto(newDonor);
        setDonorContactsToDonorMoral(donorMoralDTO, newDonor);
        newDonor=prepareDonorMoral(newDonor,donorMoralDTO);
        String newDonorString=newDonor.toDtoString();




        var newDonorContactsDTOList = donorMoralDTO.getDonorContacts();


        if (donorMoralDTO.getId() != null) {
            auditApplicationService.audit("Modification des informations du donateur moral : "+donorMoral.getCode(), getUsernameFromJwt(), "Update Moral Donor",
                    oldDonorMoralString, newDonorString, DONATEUR, UPDATE);



            Map<Long, DonorContactDTO> oldContactsMap = oldDonorContactsDTOList.stream()
                    .collect(Collectors.toMap(DonorContactDTO::getId, Function.identity()));
            Map<Long, DonorContactDTO> newContactsMap = newDonorContactsDTOList.stream()
                    .collect(Collectors.toMap(DonorContactDTO::getId, Function.identity()));


            for (var oldContactDto : oldDonorContactsDTOList) {
                DonorContact oldonorContact=donorContactMapper.donorContactDtoToModel(oldContactDto);
                List<String> olddonorContactCanalCommunication=prepareDonorContactCanalCommunication(oldContactDto);
                List<String> olddonorContactLangaugeCommunication=prepareDonorContactLanguageCommunication(oldContactDto);
                oldonorContact=processDonorContactFunction(oldContactDto,oldonorContact);


                DonorContactDTO newContact = newContactsMap.get(oldContactDto.getId());
                if (newContact != null) {
                    DonorContact newDonorContact=donorContactMapper.donorContactDtoToModel(newContact);
                    List<String> donorContactCanalCommunication=prepareDonorContactCanalCommunication(newContact);
                    List<String> donorContactLangaugeCommunication=prepareDonorContactLanguageCommunication(newContact);
                    newDonorContact=processDonorContactFunction(newContact,newDonorContact);

                    auditApplicationService.audit("Modification du contact pour le donateur moral : "+donorMoral.getCode() , getUsernameFromJwt(), "Update Moral Donor Contact",
                            oldonorContact.toDTOString(olddonorContactCanalCommunication,olddonorContactLangaugeCommunication,oldcompany), newDonorContact.toDTOString(donorContactCanalCommunication,donorContactLangaugeCommunication,newcomapny), DONATEUR, UPDATE);

                } else {
                    // Suppression
                    auditApplicationService.audit("Suppression du contact pour le donateur moral :"+donorMoral.getCode() , getUsernameFromJwt(), "Delete Moral Donor Contact",
                            oldonorContact.toDTOString(olddonorContactCanalCommunication,olddonorContactLangaugeCommunication,oldcompany), null, DONATEUR, DELETE);
                }
            }

            for (var newContact : newDonorContactsDTOList) {
                if (!oldContactsMap.containsKey(newContact.getId())) {

                    DonorContact newDonorContact=donorContactMapper.donorContactDtoToModel(newContact);
                    List<String> donorContactCanalCommunication=prepareDonorContactCanalCommunication(newContact);
                    List<String> donorContactLangaugeCommunication=prepareDonorContactLanguageCommunication(newContact);
                    newDonorContact=processDonorContactFunction(newContact,newDonorContact);

                    auditApplicationService.audit("Ajout du contact pour le donateur moral :"+donorMoral.getCode() , getUsernameFromJwt(), "Add Moral Donor Contact",
                            null,  newDonorContact.toDTOString(donorContactCanalCommunication,donorContactLangaugeCommunication,newcomapny), DONATEUR, CREATE);
                }
            }


        } else {
            auditApplicationService.audit("Ajout d'un nouveau donateur moral : "+donorMoral.getCode() , getUsernameFromJwt(), "Add Moral Donor",
                    null, newDonorString, DONATEUR, CREATE);

            var donorContactsDTOList = donorMoralDTO.getDonorContacts();
            if (donorContactsDTOList != null && !donorContactsDTOList.isEmpty()) {
                for (DonorContactDTO dto : donorContactsDTOList) {
                    DonorContact newDonorContact=donorContactMapper.donorContactDtoToModel(dto);
                    List<String> donorContactCanalCommunication=prepareDonorContactCanalCommunication(dto);
                    List<String> donorContactLangaugeCommunication=prepareDonorContactLanguageCommunication(dto);
                    newDonorContact=processDonorContactFunction(dto,newDonorContact);

                    auditApplicationService.audit("Ajout d'un nouveau contact pour le donateur moral : "+donorMoral.getCode() , getUsernameFromJwt(), "Add Moral Donor",
                            null,  newDonorContact.toDTOString(donorContactCanalCommunication,donorContactLangaugeCommunication,newcomapny), DONATEUR, CREATE);
                }
            }
        }

        log.debug("End service addDonorMoral with code: {} and id {}, took '{}'", donorMoral.getCode(), donorMoral.getId(), watch.toMS());
        return donorDTO;
    }

    List<String> prepareDonorContactCanalCommunication(DonorContactDTO contact){
        List<String> canalCommunications = new ArrayList<>();
        var canalCommunicationDTOList = contact.getCanalCommunications();

        if (canalCommunicationDTOList != null && !canalCommunicationDTOList.isEmpty()) {
            for (CanalCommunicationDTO dto : canalCommunicationDTOList) {
                CanalCommunicationDTO canalCommunicationDTO = refFeignClient.getMetCanalCommunication(dto.getId());
                canalCommunications.add(canalCommunicationDTO.getName());
            }
        }
        return canalCommunications;
    }

    List<String> prepareDonorContactLanguageCommunication(DonorContactDTO contact){
        List<String> languageCommunications = new ArrayList<>();
        var languageCommunicationDTOList = contact.getLanguageCommunications();

        if (languageCommunicationDTOList != null && !languageCommunicationDTOList.isEmpty()) {
            for (LanguageCommunicationDTO dto : languageCommunicationDTOList) {
                LanguageCommunicationDTO languageCommunicationDTO = refFeignClient.getParLanguageCommunication(dto.getId());
                languageCommunications.add(languageCommunicationDTO.getName());
            }
        }
        return languageCommunications;
    }

    private DonorContact processDonorContactFunction(DonorContactDTO contact, DonorContact donorContact ) {
        if (contact.getDonorContactFunction() != null && contact.getDonorContactFunction().getId() != null) {
            DonorContactFunctionDTO donorContactFunctionDTO = refFeignClient.getMetDonorContactFunction(contact.getDonorContactFunction().getId());
            donorContact.setDonorContactFunctionDTO(donorContactFunctionDTO);
        }
        return donorContact;
    }


    private DonorMoral prepareDonorMoral(DonorMoral donorMoral,DonorMoralDTO donorMoralDTO) {
        if (donorMoralDTO.getActivitySector() != null && donorMoralDTO.getActivitySector().getId() != null) {
            ActivitySectorDTO activitySectorDTO = refFeignClient.getMetActivitySector(donorMoralDTO.getActivitySector().getId());
            donorMoral.setActivitySectorDTO(activitySectorDTO );
        }
        else {
            donorMoral.setActivitySectorDTO(null);
        }
        if (donorMoralDTO.getTypeDonorMoral() != null && donorMoralDTO.getTypeDonorMoral().getId() != null) {
            TypeDonorMoralDTO typeDonorMoralDTO = refFeignClient.getMetTypeDonorMoral(donorMoralDTO.getTypeDonorMoral().getId());
            donorMoral.setTypeDonorMoralDTO(typeDonorMoralDTO);
        }
        else {
            donorMoral.setTypeDonorMoralDTO(null);
        }

        if (donorMoralDTO.getStatus() != null && donorMoralDTO.getStatus().getId() != null) {
            DonorStatusDTO donorStatusDTO = refFeignClient.getParDonorStatus(donorMoralDTO.getStatus().getId());
            donorMoral.setStatus(donorStatusDTO);
        }
        else {
            donorMoral.setStatus(null);
        }
        return donorMoral;
    }

    public void setDonorContactsToDonorMoral(DonorMoralDTO donorMoralDTO, DonorMoral newDonor) {
        if (donorMoralDTO.getDonorContacts() != null) {
            List<Long> existingIds = new ArrayList<>();
            List<Long> updatedIds = new ArrayList<>();

            if (donorMoralDTO.getId() != null) {
                List<DonorContact> donorContacts = (List<DonorContact>) donorContactRepository.findByDonor(newDonor);
                for (DonorContact donorContact : donorContacts) {
                    existingIds.add(donorContact.getId());
                }
            }

            Iterable<DonorContact> donorContacts = donorContactMapper.donorContactListDtoToModal(donorMoralDTO.getDonorContacts());
            for (DonorContact donorContact : donorContacts) {
                if (donorContact.getId() != null) {
                    updatedIds.add(donorContact.getId());
                }

                donorContact.setDonor(newDonor);
                DonorContact newDonorContact = donorContactRepository.save(donorContact);

                if (donorContact.getNoteDonorContacts() != null) {
                    for (NoteDonorContact noteDonorContact : donorContact.getNoteDonorContacts()) {
                        noteDonorContact.setDonorContact(newDonorContact);
                        noteDonorContactRepository.save(noteDonorContact);
                    }
                }

                // First delete existing relations before adding new ones
                donorContactCanalCommunicationRepository.deleteAllCanalByDonorContactId(donorContact.getId());
                donorContactLanguageCommunicationRepository.deleteAllLanguageByDonorContactId(donorContact.getId());

                // Add updated relationships
                setCanalCommunicationToDonorMoral(donorContact, newDonorContact);
                setLanguageCommunicationToDonorMoral(donorContact, newDonorContact);
            }

            // Remove donor contacts that are not updated
            if (donorMoralDTO.getId() != null) {
                for (Long existingId : existingIds) {
                    if (!updatedIds.contains(existingId)) {
                        donorContactCanalCommunicationRepository.deleteAllCanalByDonorContactId(existingId);
                        donorContactLanguageCommunicationRepository.deleteAllLanguageByDonorContactId(existingId);
                        donorContactRepository.deleteById(existingId);
                    }
                }
            }
        }
    }






    private void setLanguageCommunicationToDonorMoral(DonorContact donorContact, DonorContact newDonorContact) {

        if (Objects.nonNull(donorContact) && Objects.nonNull(newDonorContact)) {

            List<DonorContactLanguageCommunication> languageCommunications = donorContact.getDonorContactLanguageCommunications();
            if (Objects.nonNull(languageCommunications) && !languageCommunications.isEmpty()) {

                for (DonorContactLanguageCommunication languageCommunication : languageCommunications) {

                    DonorContactLanguageCommunication languageCommunicationSaved = new DonorContactLanguageCommunication();
                    languageCommunicationSaved.setLanguageCommunicationId(languageCommunication.getLanguageCommunicationId());
                    languageCommunicationSaved.setDonorContact(newDonorContact);
                    donorContactLanguageCommunicationRepository.save(languageCommunicationSaved);
                }
            }
        }
    }

    private void setCanalCommunicationToDonorMoral(DonorContact donorContact, DonorContact newDonorContact) {
        if (Objects.nonNull(donorContact) && Objects.nonNull(newDonorContact)) {
            List<DonorContactCanalCommunication> contactCanalCommunications = donorContact.getDonorContactCanalCommunications();
            if (Objects.nonNull(contactCanalCommunications) && !contactCanalCommunications.isEmpty()) {
                for (DonorContactCanalCommunication contactCanalCommunication : contactCanalCommunications) {
                    DonorContactCanalCommunication contactCanalCommunicationSaved = new DonorContactCanalCommunication();
                    contactCanalCommunicationSaved.setCanalCommunicationId(contactCanalCommunication.getCanalCommunicationId());
                    contactCanalCommunicationSaved.setDonorContact(newDonorContact);
                    donorContactCanalCommunicationRepository.save(contactCanalCommunicationSaved);
                }
            }
        }
    }

    private DonorDTO mapDonorToDTO(Donor donor) {
        DonorDTO donorDTO;
        if (donor instanceof DonorPhysical donorPhysical) {
            CityDTO cityDTO = refFeignClient.getParCity(donorPhysical.getCityId());
            donorPhysical.setCity(cityDTO);
            DonorStatusDTO statusDTO = refFeignClient.getParDonorStatus(donorPhysical.getDonorStatusId());
            donorPhysical.setStatus(statusDTO);
            donorDTO = donorPhysicalMapper.donorPhysicalModelToDtoForList(donorPhysical);

        } else if (donor instanceof DonorMoral donorMoral) {
            CityDTO cityDTO = refFeignClient.getParCity(donorMoral.getCityId());
            donorMoral.setCity(cityDTO);
            DonorStatusDTO statusDTO = refFeignClient.getParDonorStatus(donorMoral.getDonorStatusId());
            donorMoral.setStatus(statusDTO);
            donorDTO = donorMoralMapper.donorMoralModelToDtoForList(donorMoral);
        } else if (donor instanceof DonorAnonyme donorAnonyme) {
            DonorStatusDTO statusDTO = refFeignClient.getParDonorStatus(donorAnonyme.getDonorStatusId());
            donorAnonyme.setStatus(statusDTO);
            donorDTO = donorAnonymeMapper.donorAnonymeModelToDtoForList(donorAnonyme);
        } else {
            throw new IllegalArgumentException("Unsupported donor type: " + donor.getClass().getSimpleName());
        }
        Double allAvailableBalance = donorRepository.findAvailableBalanceByDonor(donor.getId());
        donorDTO.setAvailableBalance(allAvailableBalance);
        return donorDTO;
    }

    private String convertMapToJsonString(Map<String, String> map) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.writeValueAsString(map);
        } catch (Exception e) {
            // Gérer les exceptions de sérialisation JSON ici
            e.printStackTrace();
            return "{}"; // Retourne une chaîne JSON vide en cas d'erreur
        }
    }

    public Page<DonorDTO> getDonorsByCriteria(Integer page, Integer size, String type, String nameFirst, String nameLast, String company,String anonymeDonor, String searchByDonorType, String searchByNom, String lastNameAr, String searchByPrenom, String searchByPhoneNum, String searchByEmail, Integer searchByStatus, Long searchByTagId) throws TechnicalException {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service Get Donors page: {} size: {} type: {} nameFirst: {} nameLast: {} company: {}", page, size, type, nameFirst, nameLast, company);
        Pageable pageable = createPageable(page, size);

        Map<String, String> searchParams = new HashMap<>();
        if (searchByDonorType != null) {
            searchParams.put("Type de donateur", searchByDonorType);
        }
        if (searchByNom != null) {
            searchParams.put("Nom du donateur", searchByNom);
        }
        if (lastNameAr != null) {
            searchParams.put("Nom arabe du donateur", lastNameAr);
        }
        if (searchByPrenom != null) {
            searchParams.put("Prénom du donateur", searchByPrenom);
        }
        if (searchByPhoneNum != null) {
            searchParams.put("Numéro Téléphone", searchByPhoneNum);
        }
        if (searchByEmail != null) {
            searchParams.put("Email", searchByEmail);
        }
        if (searchByStatus != null) {
            DonorStatusDTO donorStatusDTO = refFeignClient.getParDonorStatus(Long.valueOf(searchByStatus));
            searchParams.put("Statut", String.valueOf(donorStatusDTO.getName()));
        }

        String jsonSearchParams = convertMapToJsonString(searchParams);

        Page<Donor> listDonors;
        boolean isFallbackCriteriaUsed = false; // Flag to check if fallback criteria was used
        if (searchByTagId != null) {
            searchParams.put("Tag", String.valueOf(searchByTagId));
        }

        if (!searchParams.isEmpty()) {
            listDonors = searchDonorByCriteria(searchByDonorType, searchByNom, lastNameAr, searchByPrenom, searchByPhoneNum, searchByEmail, searchByStatus, searchByTagId, pageable);
            auditApplicationService.audit("Recherche par filtre dans liste des donateurs", getUsernameFromJwt(), "Liste des donateurs", jsonSearchParams, null, DONATEUR, CONSULTATION);
        } else {
            auditApplicationService.audit("Consultation de la liste des donateurs globale", getUsernameFromJwt(), "Liste des donateurs", null, null, DONATEUR, CONSULTATION);

            if (StringUtils.isEmpty(type) && StringUtils.isEmpty(nameFirst) && StringUtils.isEmpty(nameLast) && StringUtils.isEmpty(company)) {
                return getAllDonors(page, size);
            }

            listDonors = searchDonorsByFallbackCriteria(type, nameFirst, nameLast, company, anonymeDonor, pageable);
            isFallbackCriteriaUsed = true;

        }
        List<DonorDTO> listDTO;

        if (isFallbackCriteriaUsed) {

            listDTO = listDonors.getContent().stream()
                    .map(this::mapDonorToDTOWithBase64Image)
                    .collect(Collectors.toList());
            addCityInfo(listDTO);
        } else {
            listDTO = listDonors.getContent().stream()
                    .map(this::mapDonorToDTO)
                    .collect(Collectors.toList());
        }

       listDTO= listDTO.stream().peek(dto->{
            List<Taggable> taggables = taggableRepository.findByTaggableIdAndTaggableType(dto.getId(), "donor");
            List<TagDTO> tagDTOs = new ArrayList<>();
            for (Taggable taggable : taggables) {
                Tag tag = taggable.getTag();
                TagDTO tagDTO = new TagDTO();
                tagDTO.setId(tag.getId());
                tagDTO.setName(tag.getName());
                tagDTO.setColor(tag.getColor());

                tagDTOs.add(tagDTO);
            }
            dto.setTags(tagDTOs);
       }).collect(Collectors.toList());

        log.debug("End service getDonorsByCriteria with number of donors: {}, took '{}'", listDonors.getTotalElements(), watch.toMS());
        return new PageImpl<>(listDTO, pageable, listDonors.getTotalElements());
    }

    private Page<Donor> searchDonorsByFallbackCriteria(String type, String nameFirst, String nameLast, String company, String anonymeDonor, Pageable pageable) throws TechnicalException {
        // Check for both first name and last name
        if (!StringUtils.isEmpty(nameFirst) && !StringUtils.isEmpty(nameLast)) {
            return donorRepository.searchDonorByFirstNameAndLastName(nameFirst, nameLast, pageable);
        }

        // Check for either first name or last name
        if (!StringUtils.isEmpty(nameFirst) || !StringUtils.isEmpty(nameLast)) {
            String name = !StringUtils.isEmpty(nameFirst) ? nameFirst : nameLast;
            return donorRepository.searchDonorByFirstNameOrLastName(name, pageable);
        }

        // Check for company name
        if (!StringUtils.isEmpty(company)) {
            return donorRepository.searchDonorByCompany(company, pageable);
        }

        // Check for anonymous donor
        if (!StringUtils.isEmpty(anonymeDonor)) {
            return donorRepository.searchDonorByAnonyme(anonymeDonor, pageable);
        }

        // Handle type-specific searches
        if (!StringUtils.isEmpty(type)) {
            return switch (type) {
                case TYPE_DONOR_PHYSIQUE -> donorRepository.findByDonorPhysical(pageable);
                case TYPE_DONOR_MORAL -> donorRepository.findByDonorMoral(pageable);
                case TYPE_DONOR_ANONYME -> donorRepository.findByDonorAnonyme(pageable);
                default -> throw new TechnicalException(messages.get(TYPE_IDENTITY_NOT_FOUND));
            };
        }

        // If no valid criteria provided, throw exception
        throw new TechnicalException("No valid criteria provided for donor search.");
    }


    public Page<Donor> searchDonorByCriteria(String searchByDonorType, String searchByNom, String lastNameAr, String searchByPrenom, String searchByPhoneNum, String searchByEmail, Integer searchByStatus, Long searchByTagId, Pageable pageable) {
        log.debug("Start service searchDonorByCriteria");
        TypedQuery<Donor> typedQuery = createCriteriaQuery(searchByDonorType, searchByNom, lastNameAr, searchByPrenom, searchByPhoneNum, searchByEmail, searchByStatus, searchByTagId);
        long totalCount = typedQuery.getResultList().size();
        typedQuery.setFirstResult((int) pageable.getOffset());
        typedQuery.setMaxResults(pageable.getPageSize());
        List<Donor> resultList = typedQuery.getResultList();

        log.debug("End service searchDonorByCriteria with {} donors found", totalCount);
        return new PageImpl<>(resultList, pageable, totalCount);
    }

    private Predicate addCaseInsensitiveLikePredicate(Predicate predicate, CriteriaBuilder criteriaBuilder, Expression<String> expression, String searchString) {
        return criteriaBuilder.and(predicate,
                criteriaBuilder.like(
                        criteriaBuilder.lower(expression),
                        "%" + searchString.toLowerCase() + "%"
                )
        );
    }

    private DonorDTO mapDonorToDTOWithBase64Image(Donor donor) {
        log.debug("Mapping donor of type: {}", donor.getClass().getSimpleName());  // Add this line for debugging

        DonorDTO donorDTO = mapDonorToDTO(donor);


        if (donor instanceof DonorPhysical) {
            DonorPhysicalDTO physicalDTO = (DonorPhysicalDTO) donorDTO;
            if (physicalDTO.getPictureUrl() != null) {
                try {
                    byte[] imageData = minioService.ReadFromMinIO(physicalDTO.getPictureUrl(),null);
                    String base64Image = Base64.getEncoder().encodeToString(imageData);
                    physicalDTO.setPicture64(base64Image);
                } catch (TechnicalException ex) {
                    ex.printStackTrace();
                }
            }
        } else if (donor instanceof DonorMoral) {
            DonorMoralDTO moralDTO = (DonorMoralDTO) donorDTO;
            if (moralDTO.getLogoUrl() != null) {
                try {
                    byte[] imageData = minioService.ReadFromMinIO(moralDTO.getLogoUrl(),null);
                    String base64Image = Base64.getEncoder().encodeToString(imageData);
                    moralDTO.setLogo64(base64Image);
                } catch (TechnicalException ex) {
                    ex.printStackTrace();
                }
            }
        } else if (donor instanceof DonorAnonyme) {
            DonorAnonymeDTO anonymeDTO = (DonorAnonymeDTO) donorDTO;

            anonymeDTO.setLabel(donor.getLabel());
            // No image handling for DonorAnonyme
            log.debug("Handled DonorAnonyme, skipping image processing.");
        } else {
            log.error("Unsupported donor type: {}", donor.getClass().getSimpleName());
            throw new IllegalArgumentException("Unsupported donor type: " + donor.getClass().getSimpleName());
        }
        Double allAvailableBalance = donorRepository.findAvailableBalanceByDonor(donor.getId());
        donorDTO.setAvailableBalance(allAvailableBalance);
        return donorDTO;
    }


    private Page<DonorDTO> getAllDonors(Integer page, Integer size) {
        Pageable pageable = createPageable(page, size);

        // Fetching the donors from the repository
        Page<Donor> listDonors = donorRepository.findByDeletedAtIsNull(pageable);
        log.debug("Fetched donors list with size: {}", listDonors.getTotalElements());

        // Stream processing with simplified error handling
        List<DonorDTO> listDTO = listDonors.getContent().stream()
                .map(donor -> {
                    try {
                        // Attempt to map donor to DTO with base64 image
                        return mapDonorToDTOWithBase64Image(donor);
                    } catch (Exception e) {
                        // Log error and donor details to pinpoint the issue
                        log.error("Error mapping donor with ID: {}, Type: {}", donor.getId(), donor.getClass().getSimpleName(), e);
                        return null;  // Return null for problematic donors
                    }
                })
                .filter(Objects::nonNull)  // Filter out any null values (failed mappings)
                .collect(Collectors.toList());
        listDTO= listDTO.stream().peek(dto->{
            List<Taggable> taggables = taggableRepository.findByTaggableIdAndTaggableType(dto.getId(), "donor");
            List<TagDTO> tagDTOs = new ArrayList<>();
            for (Taggable taggable : taggables) {
                Tag tag = taggable.getTag();
                TagDTO tagDTO = new TagDTO();
                tagDTO.setId(tag.getId());
                tagDTO.setName(tag.getName());
                tagDTO.setColor(tag.getColor());
                tagDTOs.add(tagDTO);
            }
            dto.setTags(tagDTOs);
        }).collect(Collectors.toList());
        return new PageImpl<>(listDTO, pageable, listDonors.getTotalElements());
    }
    // function of export to CSV
    public Predicate buildPredicate(String searchByDonorType, String searchByNom, String lastNameAr, String searchByPrenom, String searchByPhoneNum, String searchByEmail, Integer searchByStatus, Long searchByTagId, CriteriaBuilder criteriaBuilder, Root<Donor> root) {
        Predicate predicate = criteriaBuilder.conjunction();
        predicate = criteriaBuilder.and(predicate, criteriaBuilder.isNull(root.get("deletedAt")));
        // also we should remove the donor that have type = 3 (donor anonyme)
        //predicate = criteriaBuilder.and(predicate, criteriaBuilder.notEqual(root.type(), DonorAnonyme.class));

        if (searchByDonorType != null) {
            Class<? extends Donor> donorType = searchByDonorType.equals("Physique") ? DonorPhysical.class :
                    searchByDonorType.equals("Moral") ? DonorMoral.class :
                            DonorAnonyme.class; // Default to Anonyme if neither Physique nor Moral
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.type(), donorType));
        }

        // Filter by tag ID if provided
        if (searchByTagId != null) {
            // Create a subquery to find donors with the specified tag
            Subquery<Long> subquery = criteriaBuilder.createQuery().subquery(Long.class);
            Root<Taggable> taggableRoot = subquery.from(Taggable.class);
            subquery.select(taggableRoot.get("taggableId"))
                    .where(
                        criteriaBuilder.and(
                            criteriaBuilder.equal(taggableRoot.get("taggableType"), "donor"),
                            criteriaBuilder.equal(taggableRoot.get("tag").get("id"), searchByTagId)
                        )
                    );

            // Add the subquery condition to the main predicate
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.in(root.get("id")).value(subquery));
        }


        if (lastNameAr != null) {
            predicate = addCaseInsensitiveLikePredicate(predicate, criteriaBuilder, root.get("lastNameAr"), lastNameAr);
        }

        if (searchByNom != null) {
            predicate = criteriaBuilder.or(
                    addCaseInsensitiveLikePredicate(predicate, criteriaBuilder, root.get("lastName"), searchByNom),
                    addCaseInsensitiveLikePredicate(predicate, criteriaBuilder, root.get("name"), searchByNom),
                    addCaseInsensitiveLikePredicate(predicate, criteriaBuilder, root.join("donorContacts", JoinType.LEFT).join("donor", JoinType.LEFT).get("company"), searchByNom)
            );
        }

        if (searchByEmail != null) {
            Predicate physicalDonorPredicate = criteriaBuilder.like(root.get("email"), "%" + searchByEmail + "%");
            Join<DonorMoral, DonorContact> donorContactJoin = root.join("donorContacts", JoinType.LEFT);
            Predicate moralDonorPredicate = criteriaBuilder.like(donorContactJoin.get("email"), "%" + searchByEmail + "%");
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.or(physicalDonorPredicate, moralDonorPredicate));
        }

        if (searchByPhoneNum != null) {
            Predicate physicalDonorPredicate = criteriaBuilder.like(root.get("phoneNumber"), "%" + searchByPhoneNum + "%");
            Join<DonorMoral, DonorContact> donorContactJoin = root.join("donorContacts", JoinType.LEFT);
            Predicate moralDonorPredicate = criteriaBuilder.like(donorContactJoin.get("phoneNumber"), "%" + searchByPhoneNum + "%");
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.or(physicalDonorPredicate, moralDonorPredicate));
        }

        if (searchByStatus != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("donorStatusId"), searchByStatus));
        }

        return predicate;
    }

    public List<DonorDTO> filterDonorsToExport(String searchByDonorType, String searchByNom, String lastNameAr, String searchByPrenom, String searchByPhoneNum, String searchByEmail, Integer searchByStatus, Long searchByTagId) {
        TypedQuery<Donor> typedQuery = createCriteriaQuery(searchByDonorType, searchByNom, lastNameAr, searchByPrenom, searchByPhoneNum, searchByEmail, searchByStatus, searchByTagId);
        List<Donor> listDonors = typedQuery.getResultList();

        List<DonorDTO> listDTO = listDonors.stream()
                .map(this::mapDonorToDTO)
                .collect(Collectors.toList());

        // Add city part
        addCityInfo(listDTO);

        return listDTO;
    }

    public List<DonorExportDTO> getAllDonorsToExport(List<DonorDTO> listDonors) {
        return listDonors.stream()
                .map(DonorMapper::toDonorExportDTO)
                .collect(Collectors.toList());
    }

    public ExportFileDTO exportFileWithName(String searchByDonorType, String searchByNom, String lastNameAr, String searchByPrenom, String searchByPhoneNum, String searchByEmail, Integer searchByStatus, Long searchByTagId) {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service exportDonorFile with search criteria: searchByDonorType={}, searchByNom={}, lastNameAr={}, searchByPrenom={}, searchByPhoneNum={}, searchByEmail={}, searchByStatus={}, searchByTagId={}",
                searchByDonorType, searchByNom, lastNameAr, searchByPrenom, searchByPhoneNum, searchByEmail, searchByStatus, searchByTagId);

        // Filter Donor entities based on criteria
        List<DonorDTO> listDonors = filterDonorsToExport(searchByDonorType, searchByNom, lastNameAr, searchByPrenom, searchByPhoneNum, searchByEmail, searchByStatus, searchByTagId);

        // Convert entities to DTOs for export
        List<DonorExportDTO> listExportDTO = getAllDonorsToExport(listDonors);

        // Define export parameters
        String sheetName = "Rapport des Donateurs";
        String[] headers = Arrays.stream(DonorExportHeaders.values())
                .map(DonorExportHeaders::getHeaderName)
                .toArray(String[]::new);


        auditApplicationService.audit("Exportation du fichier CSV pour les donateurs", getUsernameFromJwt(), "Export file Csv",null
                    ,null  , DONATEUR, EXPORT);



        // Perform export
        ExportFileDTO exportFileDTO = exportService.exportEntities(sheetName, headers, listExportDTO, this::mapToExportRow);

        log.debug("End service exportDonorFile, took '{}'", watch.toMS());
        return exportFileDTO;
    }

    private Object[] mapToExportRow(DonorExportDTO dto) {
        return new Object[]{
                dto.getCode() != null ? dto.getCode() : "---",
                dto.getCreatedAt() != null ? dto.getCreatedAt().format(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss")) :"---",
                dto.getDonorType() != null ? dto.getDonorType() : "---",
                dto.getFirstName() != null ? dto.getFirstName() :"---",
                dto.getLastName() != null ? dto.getLastName() : "---",
                dto.getCompany() != null ? dto.getCompany() : "---",
                dto.getPhoneNumber() != null ? dto.getPhoneNumber() : "---",
                dto.getEmail() != null ? dto.getEmail() : "---",
                dto.getSex() != null ? dto.getSex() : "---",
                dto.getAddress() != null ? dto.getAddress() : "---",
                dto.getCity() != null ? dto.getCity() : "---",
                dto.getRegion() != null ? dto.getRegion() : "---",
                dto.getCountry() != null ? dto.getCountry() :"---",
                dto.getStatus() != null ? dto.getStatus() :"---"
        };
    }



    private TypedQuery<Donor> createCriteriaQuery(String searchByDonorType, String searchByNom, String lastNameAr, String searchByPrenom, String searchByPhoneNum, String searchByEmail, Integer searchByStatus, Long searchByTagId) {
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Donor> criteriaQuery = criteriaBuilder.createQuery(Donor.class);
        Root<Donor> root = criteriaQuery.from(Donor.class);
        Predicate predicate = buildPredicate(searchByDonorType, searchByNom, lastNameAr, searchByPrenom, searchByPhoneNum, searchByEmail, searchByStatus, searchByTagId, criteriaBuilder, root);

        criteriaQuery.where(predicate);
        criteriaQuery.orderBy(criteriaBuilder.desc(root.get("createdAt")));

        return entityManager.createQuery(criteriaQuery);
    }

    private void addCityInfo(List<DonorDTO> listDTO) {
        for (DonorDTO donorDTO : listDTO) {
            if (donorDTO instanceof DonorPhysicalDTO physicalDTO) {
                CityDTO fullcityDTO = refFeignClient.getParCity(physicalDTO.getCity().getId());
                physicalDTO.setCity(fullcityDTO);
                CityWithRegionAndCountryDTO fullCountryDto = refController.getCityWithRegionAndCountry(physicalDTO.getCity().getId()).getBody();
                physicalDTO.setInfo(fullCountryDto);
                if(physicalDTO.getTypeIdentity() != null && physicalDTO.getTypeIdentity().getId() != null){
                    TypeIdentityDTO typeIdentityDTO = refFeignClient.getParTypeIdentity(physicalDTO.getTypeIdentity().getId());
                    physicalDTO.setTypeIdentity(typeIdentityDTO);
                }
            } else if (donorDTO instanceof DonorMoralDTO moralDTO) {
                CityDTO fullcityDTO = refFeignClient.getParCity(moralDTO.getCity().getId());
                moralDTO.setCity(fullcityDTO);
                CityWithRegionAndCountryDTO fullCountryDto = refController.getCityWithRegionAndCountry(moralDTO.getCity().getId()).getBody();
                moralDTO.setInfo(fullCountryDto);
                //activity sector
                if(moralDTO.getActivitySector() != null && moralDTO.getActivitySector().getId() != null){
                    ActivitySectorDTO activitySectorDTO = refFeignClient.getMetActivitySector(moralDTO.getActivitySector().getId());
                    moralDTO.setActivitySector(activitySectorDTO);
                }
            }
        }
    }

    private LocalDateTime convertToLocalDateTime(Date date) {
        if (date == null) {
            return null;
        }
        return LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
    }



    public List<ReleveDonorDto> releveCompte(Long donorId) {

        Donor donor = donorRepository.findById(donorId)
                .orElseThrow(() -> new EntityNotFoundException("Donor not found"));

        List<ReleveDonorDto> releveList = new ArrayList<>();

        List<Donation> donations = donationRepository.findByDonor(donor);
        double totalDonation = donations.stream()
                .mapToDouble(Donation::getValue)
                .sum();

        for (Donation donation : donations) {
            for(BudgetLine budgetLine:donation.getBudgetLines()){
                ReleveDonorDto dto = ReleveDonorDto.builder()
                        .id(donation.getId())
                        .code(donation.getCode())
                        .name(null)
                        .montantEntree(budgetLine.getAmount())
                        .receptionDate(donation.getReceptionDate())
                        .type(donation.getType())
                        .dateExecution(null)
                        .canalDonation(donation.getCanalDonationId() != null ? refFeignClient.getMetCanalDonation(donation.getCanalDonationId()) : null)
                        .typeDonationKafalat("donation")
                        .services(servicesMapper.toDto(budgetLine.getService()))
                        .totalEntree(budgetLine.getAmount())
                        .beneficiaries(null)
                        .build();
                releveList.add(dto);

            }
        }

        List<TakenInChargeDonor> takenInChargeDonors = takenInChargeDonorRepository.findByDonor(donor);
        for (TakenInChargeDonor takenInChargeDonor : takenInChargeDonors) {
            List<TakenInChargeOperation> validOperations = takenInChargeOperationRepository
                    .findByTakenInChargeDonor(takenInChargeDonor)
                    .stream()
                    .filter(operation -> operation.getExecutionDate() != null)
                    .collect(Collectors.toList());

            double totalAmountOperations = validOperations.stream()
                    .mapToDouble(TakenInChargeOperation::getAmount)
                    .sum();

            for (TakenInChargeOperation operation : validOperations) {
                TakenInCharge takenInCharge = takenInChargeDonor.getTakenInCharge();
                Optional<Services> optionalService = servicesRepository.findById(takenInCharge.getService().getId());
                ServicesDTO servicesDTO = optionalService.map(servicesMapper::toDto).orElse(null);

                List<BeneficiaryReleveDto> beneficiaryDTOs = takenInCharge.getTakenInChargeBeneficiaries()
                        .stream()
                        .map(ticBeneficiary -> {
                            BeneficiaryReleveDto beneficiaryDto = beneficiaryMapper.toDto(ticBeneficiary.getBeneficiary());
                            if (beneficiaryDto != null) {
                                beneficiaryDto.setMontantAffecter(operation.getAmount());
                            }
                            return beneficiaryDto;
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

                ReleveDonorDto dto = ReleveDonorDto.builder()
                        .id(operation.getTakenInChargeDonor().getId())
                        .code(operation.getCode())
                        .name(null)
                        .receptionDate(operation.getPlanningDate())
                        .type(takenInCharge.getType())
                        .dateExecution(convertToLocalDateTime(operation.getExecutionDate()))
                        .montantSortie(operation.getAmount())
                        .typeDonationKafalat("takenInChargeOperation")
                        .services(servicesDTO)
                        .totalSortie(totalAmountOperations)
                        .beneficiaries(beneficiaryDTOs)
                        .build();

                releveList.add(dto);
            }

        }


        List<BudgetLine> budgetLines = budgetLineRepository.findBudgetLineByDonor(donorId)

                .stream()
                .filter(budgetLine -> budgetLine.getAideComplementaire()!= null &&  budgetLine.getStatus().equals(BudgetLineStatus.EXECUTED) )
                .toList();

        double totalMontantDonateur = 0;


        for (BudgetLine acdb : budgetLines) {
            AideComplementaire aideComplementaire = acdb.getAideComplementaire();
            Optional<Services> optionalService = servicesRepository.findById(aideComplementaire.getService().getId());
            ServicesDTO servicesDTO = optionalService.map(servicesMapper::toDto).orElse(null);
            if (acdb.getMontantReserve() != null) {
            if ("executer".equals(aideComplementaire.getStatut()) || "cloturer".equals(aideComplementaire.getStatut()) ) {



                totalMontantDonateur += acdb.getMontantReserve();

                List<BeneficiaryReleveDto> beneficiaryDTOs = aideComplementaire.getAideComplementaireDonorBeneficiaries()
                        .stream()
                        .filter(acdbItem -> acdbItem.getStatutValidation() != null && acdbItem.getStatutValidation())
                        .map(acdbItem -> {
                            BeneficiaryReleveDto beneficiaryDto = beneficiaryMapper.toDto(acdbItem.getBeneficiary());
                            if (beneficiaryDto != null) {
                                beneficiaryDto.setMontantAffecter(acdbItem.getMontantAffecter());
                            }
                            return beneficiaryDto;
                        })
                        .filter(Objects::nonNull) // Exclure les nulls
                        .collect(Collectors.toList());

                ReleveDonorDto dto = ReleveDonorDto.builder()
                        .id(aideComplementaire.getId())
                        .code(aideComplementaire.getCode())
                        .name(aideComplementaire.getName())
                        .type(aideComplementaire.getName())
                        .dateExecution(aideComplementaire.getDateExecution())
                        .montantSortie(acdb.getMontantReserve())
                        .typeDonationKafalat("aideComplementaire")
                        .totalSortie(totalMontantDonateur)
                        .services(servicesDTO)
                        .beneficiaries(beneficiaryDTOs)
                        .build();

                releveList.add(dto);
            }}
        }

        StringBuilder jsonFormat=prepareAuditForReleve(donor,"Compte Donateur");

        auditApplicationService.audit("Consultation du relevé pour le donateur "+donor.getCode(), getUsernameFromJwt(), "Export file Csv",jsonFormat.toString()
                ,null  , DONATEUR, CONSULTATION);

        return releveList;
    }

    public List<JournalOperationDto> getJournalOperation(Long donorId) {

        Donor donor = donorRepository.findById(donorId)
                .orElseThrow(() -> new EntityNotFoundException("Donor not found"));

        List<JournalOperationDto> releveList = new ArrayList<>();

        List<TakenInChargeDonor> takenInChargeDonors = takenInChargeDonorRepository.findByDonor(donor);
        double totalAmountOperations = 0;
        for (TakenInChargeDonor takenInChargeDonor : takenInChargeDonors) {
            List<TakenInChargeOperation> validOperations = takenInChargeOperationRepository
                    .findByTakenInChargeDonor(takenInChargeDonor)
                    .stream()
                    .filter(operation -> operation.getExecutionDate() != null)
                    .collect(Collectors.toList());

            totalAmountOperations += validOperations.stream()
                    .mapToDouble(TakenInChargeOperation::getAmount)
                    .sum();

            for (TakenInChargeOperation operation : validOperations) {
                TakenInCharge takenInCharge = takenInChargeDonor.getTakenInCharge();
                Optional<Services> optionalService = servicesRepository.findById(takenInCharge.getService().getId());

                ServicesDTO servicesDTO = optionalService.map(service -> {
                    ServicesDTO dto = servicesMapper.toDto(service);
                    CategoryDTO categoryDTO = refFeignClient.getMetCategory(service.getServiceCategoryId());
                    ServiceDTO serviceDTO = refFeignClient.getMetService(service.getServiceCategoryTypeId());
                    dto.setCategory(categoryDTO != null ? categoryDTO.getName() : null);
                    dto.setTypeCategory(serviceDTO != null ? serviceDTO.getName() : null);
                    return dto;
                }).orElse(null);

                List<BeneficiaryReleveDto> beneficiaryDTOs = takenInCharge.getTakenInChargeBeneficiaries()
                        .stream()
                        .map(ticBeneficiary -> {
                            BeneficiaryReleveDto beneficiaryDto = beneficiaryMapper.toDto(ticBeneficiary.getBeneficiary());
                            if (beneficiaryDto != null) {
                                beneficiaryDto.setMontantAffecter(operation.getAmount());
                            }
                            return beneficiaryDto;
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

                JournalOperationDto dto = JournalOperationDto.builder()
                        .id(operation.getTakenInChargeDonor().getId())
                        .code(operation.getCode())
                        .name(null)
                        .type(takenInCharge.getType())
                        .dateExecution(convertToLocalDateTime(operation.getExecutionDate()))
                        .montantSortie(operation.getAmount())
                        .typeDonationKafalat("takenInChargeOperation")
                        .services(servicesDTO)
                        .totalSortie(totalAmountOperations)
                        .beneficiaries(beneficiaryDTOs)
                        .build();

                releveList.add(dto);
            }
        }

        List<AideComplementaireDonorBeneficiary> aideComplementaireDonorBeneficiaries = aideComplementaireDonorBeneficiaryRepository
                .findByDonor(donor)
                .stream()
                .filter(acdb -> acdb.getDonor() != null && acdb.getBeneficiary() == null)
                .collect(Collectors.toList());

        double totalMontantDonateur = 0;

        for (AideComplementaireDonorBeneficiary acdb : aideComplementaireDonorBeneficiaries) {
            AideComplementaire aideComplementaire = acdb.getAideComplementaire();
            Optional<Services> optionalService = servicesRepository.findById(aideComplementaire.getService().getId());

            ServicesDTO servicesDTO = optionalService.map(service -> {
                ServicesDTO dto = servicesMapper.toDto(service);
                CategoryDTO categoryDTO = refFeignClient.getMetCategory(service.getServiceCategoryId());
                TypePriseEnChargeDTO serviceDTO = refFeignClient.getParTypePriseEnCharge(service.getServiceCategoryTypeId());
                dto.setCategory(categoryDTO != null ? categoryDTO.getName() : null);
                dto.setTypeCategory(serviceDTO != null ? serviceDTO.getName() : null);
                return dto;
            }).orElse(null);

            if ("executer".equals(aideComplementaire.getStatut()) || "cloturer".equals(aideComplementaire.getStatut())) {
                totalMontantDonateur += acdb.getMontantTotalDuDonateur();

                List<BeneficiaryReleveDto> beneficiaryDTOs = aideComplementaire.getAideComplementaireDonorBeneficiaries()
                        .stream()
                        .filter(acdbItem -> acdbItem.getStatutValidation() != null && acdbItem.getStatutValidation())
                        .map(acdbItem -> {
                            BeneficiaryReleveDto beneficiaryDto = beneficiaryMapper.toDto(acdbItem.getBeneficiary());
                            if (beneficiaryDto != null) {
                                beneficiaryDto.setMontantAffecter(acdbItem.getMontantAffecter());
                            }
                            return beneficiaryDto;
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

                JournalOperationDto dto = JournalOperationDto.builder()
                        .id(aideComplementaire.getId())
                        .code(aideComplementaire.getCode())
                        .name(aideComplementaire.getName())
                        .type(aideComplementaire.getName())
                        .dateExecution(aideComplementaire.getDateExecution())
                        .montantSortie(acdb.getMontantTotalDuDonateur())
                        .typeDonationKafalat("aideComplementaire")
                        .totalSortie(totalMontantDonateur)
                        .services(servicesDTO)
                        .beneficiaries(beneficiaryDTOs)
                        .total(totalAmountOperations + totalMontantDonateur)
                        .build();

                releveList.add(dto);
            }
        }
        StringBuilder jsonFormat=prepareAuditForReleve(donor,"Opérations exécutées");
        auditApplicationService.audit("Consultation du journal des opérations exécutées pour le donateur "+donor.getCode(), getUsernameFromJwt(), "Export file Csv",jsonFormat.toString(),null , DONATEUR, CONSULTATION);

        return releveList;
    }

    public List<JournalOperationReserveDto> getJournalOperationReserve(Long donorId) {

        Donor donor = donorRepository.findById(donorId)
                .orElseThrow(() -> new EntityNotFoundException("Donor not found"));

        List<JournalOperationReserveDto> releveList = new ArrayList<>();

        List<TakenInChargeDonor> takenInChargeDonors = takenInChargeDonorRepository.findByDonor(donor);
        double totalAmountOperations = 0;
        for (TakenInChargeDonor takenInChargeDonor : takenInChargeDonors) {
            List<TakenInChargeOperation> validOperations = takenInChargeOperationRepository
                    .findByTakenInChargeDonor(takenInChargeDonor)
                    .stream()
                    .filter(operation -> operation.getExecutionDate() != null)
                    .collect(Collectors.toList());

            totalAmountOperations += validOperations.stream()
                    .mapToDouble(TakenInChargeOperation::getAmount)
                    .sum();

            for (TakenInChargeOperation operation : validOperations) {
                TakenInCharge takenInCharge = takenInChargeDonor.getTakenInCharge();
                Optional<Services> optionalService = servicesRepository.findById(takenInCharge.getService().getId());

                ServicesDTO servicesDTO = optionalService.map(service -> {
                    ServicesDTO dto = servicesMapper.toDto(service);
                    CategoryDTO categoryDTO = refFeignClient.getMetCategory(service.getServiceCategoryId());
                    TypePriseEnChargeDTO serviceDTO = refFeignClient.getParTypePriseEnCharge(service.getServiceCategoryTypeId());
                    dto.setCategory(categoryDTO != null ? categoryDTO.getName() : null);
                    dto.setTypeCategory(serviceDTO != null ? serviceDTO.getName() : null);
                    return dto;
                }).orElse(null);

                List<BeneficiaryReleveDto> beneficiaryDTOs = takenInCharge.getTakenInChargeBeneficiaries()
                        .stream()
                        .map(ticBeneficiary -> {
                            BeneficiaryReleveDto beneficiaryDto = beneficiaryMapper.toDto(ticBeneficiary.getBeneficiary());
                            if (beneficiaryDto != null) {
                                beneficiaryDto.setMontantAffecter(operation.getAmount());
                            }
                            return beneficiaryDto;
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

                JournalOperationReserveDto dto = JournalOperationReserveDto.builder()
                        .id(operation.getTakenInChargeDonor().getId())
                        .code(operation.getCode())
                        .name(null)
                        .type(takenInCharge.getType())
                        .dateExecution(convertToLocalDateTime(operation.getExecutionDate()))
                        .montantSortie(operation.getAmount())
                        .typeDonationKafalat("takenInChargeOperation")
                        .services(servicesDTO)
                        .totalSortie(totalAmountOperations)
                        .beneficiaries(beneficiaryDTOs)
                        .build();

                releveList.add(dto);
            }
        }

        List<AideComplementaireDonorBeneficiary> aideComplementaireDonorBeneficiaries = aideComplementaireDonorBeneficiaryRepository
                .findByDonor(donor)
                .stream()
                .filter(acdb -> acdb.getDonor() != null && acdb.getBeneficiary() == null)
                .collect(Collectors.toList());

        double totalMontantDonateur = 0;

        for (AideComplementaireDonorBeneficiary acdb : aideComplementaireDonorBeneficiaries) {
            AideComplementaire aideComplementaire = acdb.getAideComplementaire();
            Optional<Services> optionalService = servicesRepository.findById(aideComplementaire.getService().getId());

            ServicesDTO servicesDTO = optionalService.map(service -> {
                ServicesDTO dto = servicesMapper.toDto(service);
                CategoryDTO categoryDTO = refFeignClient.getMetCategory(service.getServiceCategoryId());
                ServiceDTO serviceDTO = refFeignClient.getMetService(service.getServiceCategoryTypeId());
                dto.setCategory(categoryDTO != null ? categoryDTO.getName() : null);
                dto.setTypeCategory(serviceDTO != null ? serviceDTO.getName() : null);
                return dto;
            }).orElse(null);

            if ("planifier".equals(aideComplementaire.getStatut()) || "enCours".equals(aideComplementaire.getStatut()) || "enAttenteDexecution".equals(aideComplementaire.getStatut())) {
                totalMontantDonateur += acdb.getMontantTotalDuDonateur();

                List<BeneficiaryReleveDto> beneficiaryDTOs = aideComplementaire.getAideComplementaireDonorBeneficiaries()
                        .stream()
                        .filter(acdbItem -> acdbItem.getStatutValidation() != null && acdbItem.getStatutValidation())
                        .map(acdbItem -> {
                            BeneficiaryReleveDto beneficiaryDto = beneficiaryMapper.toDto(acdbItem.getBeneficiary());
                            if (beneficiaryDto != null) {
                                beneficiaryDto.setMontantAffecter(acdbItem.getMontantAffecter());
                            }
                            return beneficiaryDto;
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

                JournalOperationReserveDto dto = JournalOperationReserveDto.builder()
                        .id(aideComplementaire.getId())
                        .code(aideComplementaire.getCode())
                        .name(aideComplementaire.getName())
                        .type(aideComplementaire.getName())
                        .dateExecution(aideComplementaire.getDateExecution())
                        .montantSortie(acdb.getMontantTotalDuDonateur())
                        .typeDonationKafalat("aideComplementaire")
                        .totalSortie(totalMontantDonateur)
                        .services(servicesDTO)
                        .beneficiaries(beneficiaryDTOs)
                        .total(totalAmountOperations + totalMontantDonateur)
                        .build();

                releveList.add(dto);
            }
        }
        StringBuilder jsonFormat= prepareAuditForReleve(donor,"Opérations réservées");
        auditApplicationService.audit("Consultation du journal des opérations réservées pour le donateur : "+donor.getCode(), getUsernameFromJwt(), "Export file Csv",
                jsonFormat.toString(),null  , DONATEUR, CONSULTATION);
        return releveList;
    }

    public List<SoldeDto> getSolde(Long donorId) {

        Donor donor = donorRepository.findById(donorId)
                .orElseThrow(() -> new EntityNotFoundException("Donor not found"));

        List<SoldeDto> releveList = new ArrayList<>();

        List<TakenInChargeDonor> takenInChargeDonors = takenInChargeDonorRepository.findByDonor(donor);
        double totalAmountOperations = 0;

        for (TakenInChargeDonor takenInChargeDonor : takenInChargeDonors) {
            List<TakenInChargeOperation> validOperations = takenInChargeOperationRepository
                    .findByTakenInChargeDonor(takenInChargeDonor)
                    .stream()
                    .filter(operation -> operation.getExecutionDate() != null)
                    .collect(Collectors.toList());

            totalAmountOperations += validOperations.stream()
                    .mapToDouble(TakenInChargeOperation::getAmount)
                    .sum();

            for (TakenInChargeOperation operation : validOperations) {
                TakenInCharge takenInCharge = takenInChargeDonor.getTakenInCharge();
                Optional<Services> optionalService = servicesRepository.findById(takenInCharge.getService().getId());

                ServicesDTO servicesDTO = optionalService.map(service -> {
                    ServicesDTO dto = servicesMapper.toDto(service);
                    CategoryDTO categoryDTO = refFeignClient.getMetCategory(service.getServiceCategoryId());
                    ServiceDTO serviceDTO = refFeignClient.getMetService(service.getServiceCategoryTypeId());
                    dto.setCategory(categoryDTO != null ? categoryDTO.getName() : null);
                    dto.setTypeCategory(serviceDTO != null ? serviceDTO.getName() : null);
                    return dto;
                }).orElse(null);

                // Récupérer les montants `amountDisponible` et `amountReserve` pour le service
                Double amountDisponible = budgetLineRepository.findByServiceAndStatus(takenInCharge.getService(), BudgetLineStatus.DISPONIBLE)
                        .stream()
                        .mapToDouble(BudgetLine::getAmount)
                        .sum();

                Double amountReserve = budgetLineRepository.findByServiceAndStatus(takenInCharge.getService(), BudgetLineStatus.RESERVED)
                        .stream()
                        .mapToDouble(BudgetLine::getAmount)
                        .sum();

                List<BeneficiaryReleveDto> beneficiaryDTOs = takenInCharge.getTakenInChargeBeneficiaries()
                        .stream()
                        .map(ticBeneficiary -> {
                            BeneficiaryReleveDto beneficiaryDto = beneficiaryMapper.toDto(ticBeneficiary.getBeneficiary());
                            if (beneficiaryDto != null) {
                                beneficiaryDto.setMontantAffecter(operation.getAmount());
                            }
                            return beneficiaryDto;
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

                SoldeDto dto = SoldeDto.builder()
                        .id(operation.getTakenInChargeDonor().getId())
                        .code(operation.getCode())
                        .name(null)
                        .type(takenInCharge.getType())
                        .dateExecution(convertToLocalDateTime(operation.getExecutionDate()))
                        .montantSortie(operation.getAmount())
                        .typeDonationKafalat("takenInChargeOperation")
                        .services(servicesDTO)
                        .totalSortie(totalAmountOperations)
                        .beneficiaries(beneficiaryDTOs)
                        .amountDisponible(amountDisponible)
                        .amountReserve(amountReserve)
                        .build();

                releveList.add(dto);
            }
        }

        List<AideComplementaireDonorBeneficiary> aideComplementaireDonorBeneficiaries = aideComplementaireDonorBeneficiaryRepository
                .findByDonor(donor)
                .stream()
                .filter(acdb -> acdb.getDonor() != null && acdb.getBeneficiary() == null)
                .collect(Collectors.toList());

        double totalMontantDonateur = 0;

        for (AideComplementaireDonorBeneficiary acdb : aideComplementaireDonorBeneficiaries) {
            AideComplementaire aideComplementaire = acdb.getAideComplementaire();
            Optional<Services> optionalService = servicesRepository.findById(aideComplementaire.getService().getId());

            ServicesDTO servicesDTO = optionalService.map(service -> {
                ServicesDTO dto = servicesMapper.toDto(service);
                CategoryDTO categoryDTO = refFeignClient.getMetCategory(service.getServiceCategoryId());
                ServiceDTO serviceDTO = refFeignClient.getMetService(service.getServiceCategoryTypeId());
                dto.setCategory(categoryDTO != null ? categoryDTO.getName() : null);
                dto.setTypeCategory(serviceDTO != null ? serviceDTO.getName() : null);
                return dto;
            }).orElse(null);

            Double amountDisponible = budgetLineRepository.findByServiceAndStatus(aideComplementaire.getService(), BudgetLineStatus.DISPONIBLE)
                    .stream()
                    .mapToDouble(BudgetLine::getAmount)
                    .sum();

            Double amountReserve = budgetLineRepository.findByServiceAndStatus(aideComplementaire.getService(), BudgetLineStatus.RESERVED)
                    .stream()
                    .mapToDouble(BudgetLine::getAmount)
                    .sum();

            if ("executer".equals(aideComplementaire.getStatut()) || "cloturer".equals(aideComplementaire.getStatut())) {
                totalMontantDonateur += acdb.getMontantTotalDuDonateur();

                List<BeneficiaryReleveDto> beneficiaryDTOs = aideComplementaire.getAideComplementaireDonorBeneficiaries()
                        .stream()
                        .filter(acdbItem -> acdbItem.getStatutValidation() != null && acdbItem.getStatutValidation())
                        .map(acdbItem -> {
                            BeneficiaryReleveDto beneficiaryDto = beneficiaryMapper.toDto(acdbItem.getBeneficiary());
                            if (beneficiaryDto != null) {
                                beneficiaryDto.setMontantAffecter(acdbItem.getMontantAffecter());
                            }
                            return beneficiaryDto;
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

                SoldeDto dto = SoldeDto.builder()
                        .id(aideComplementaire.getId())
                        .code(aideComplementaire.getCode())
                        .name(aideComplementaire.getName())
                        .type(aideComplementaire.getName())
                        .dateExecution(aideComplementaire.getDateExecution())
                        .montantSortie(acdb.getMontantTotalDuDonateur())
                        .typeDonationKafalat("aideComplementaire")
                        .totalSortie(totalMontantDonateur)
                        .services(servicesDTO)
                        .beneficiaries(beneficiaryDTOs)
                        .amountDisponible(amountDisponible)
                        .amountReserve(amountReserve)
                        .build();

                releveList.add(dto);
            }
        }
        StringBuilder jsonFormat= prepareAuditForReleve(donor,"Solde");
        auditApplicationService.audit("Consultation du solde pour le donateur : "+donor.getCode(), getUsernameFromJwt(), "Export file Csv",jsonFormat.toString()
                ,null  , DONATEUR, CONSULTATION);

        return releveList;
    }

    private StringBuilder prepareAuditForReleve(Donor donor,String type){
        if(donor instanceof DonorPhysical){
            DonorPhysical exsitingDonor = (DonorPhysical) donor;

            StringBuilder columnToAppend = new StringBuilder();
            columnToAppend.append("{");
            columnToAppend.append("\"Type de Relevé\": \"" + type + "\",");
            columnToAppend.append("\"Type de la Donateur\": \"Physique\",");
            columnToAppend.append("\"Code de la Donateur\": \"" + escapeSpecialChars(exsitingDonor.getCode()) + "\",");
            columnToAppend.append("\"Nom de la Donateur\": \"" + escapeSpecialChars(exsitingDonor.getLastName() + " " + exsitingDonor.getFirstName()) + "\"");
            columnToAppend.append("}");
            return columnToAppend;

        }
        else if(donor instanceof DonorAnonyme){
            DonorAnonyme exsitingDonor = (DonorAnonyme) donor;
            StringBuilder columnToAppend = new StringBuilder();
            columnToAppend.append("{");
            columnToAppend.append("\"Type de Relevé\": \"" + type + "\",");
            columnToAppend.append("\"Type de la Donateur\": \"Anonyme\",");
            columnToAppend.append("\"Code de la Donateur\": \"" + escapeSpecialChars(exsitingDonor.getCode()) + "\",");
            columnToAppend.append("\"Nom de la Donateur\": \"" + escapeSpecialChars(exsitingDonor.getName()) + "\"");
            columnToAppend.append("}");
            return columnToAppend;
        } else {
            DonorMoral exsitingDonor = (DonorMoral) donor;
            StringBuilder columnToAppend = new StringBuilder();
            columnToAppend.append("{");
            columnToAppend.append("\"Type de Relevé\": \"" + type + "\",");
            columnToAppend.append("\"Type de la Donateur\": \"Moral\",");
            columnToAppend.append("\"Code de la Donateur\": \"" + escapeSpecialChars(exsitingDonor.getCode()) + "\",");
            columnToAppend.append("\"Nom de la Donateur\": \"" + escapeSpecialChars(exsitingDonor.getCompany()) + "\"");
            columnToAppend.append("}");
            return columnToAppend;
        }
    }

}
