package ma.almobadara.backend.repository.donor;

import jakarta.transaction.Transactional;
import ma.almobadara.backend.model.donor.DonorContactLanguageCommunication;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface DonorContactLanguageCommunicationRepository extends JpaRepository<DonorContactLanguageCommunication, Long> {

    @Transactional
    @Modifying
    @Query(value = "DELETE FROM donor_contact_language_communication WHERE donor_contact_id = :donorContactId", nativeQuery = true)
    void deleteAllLanguageByDonorContactId(Long donorContactId);

}
