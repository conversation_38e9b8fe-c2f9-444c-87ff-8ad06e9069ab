package ma.almobadara.backend.clients;

import ma.almobadara.backend.clients.config.RefFeignClientConfiguration;
import ma.almobadara.backend.dto.referentiel.*;
import ma.almobadara.backend.dto.takenInCharge.TakenInChargeDocumentTypeDTO;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

@FeignClient(name = "ref-service", url = "${ref.uri}", configuration = RefFeignClientConfiguration.class)
public interface RefFeignClient {

    @Cacheable("getAllCurrencies")
    @GetMapping(value = "/currencies",consumes = "application/json")
    List<CurrencyDTO> getAllCurrencies();

    @Cacheable("getParCurrency")
    @GetMapping(value = "/currencies/{id}",consumes = "application/json")
    CurrencyDTO getParCurrency(@PathVariable Long id);

    @Cacheable("getAllMetProfessions")
    @GetMapping(value = "/professions",consumes = "application/json")
    List<ProfessionDTO> getAllMetProfessions();

    @Cacheable("getAllMetDeathReasons")
    @GetMapping(value = "/death-reasons",consumes = "application/json")
    List<DeathReasonDTO> getAllMetDeathReasons();

    @Cacheable("getMetDeathReason")
    @GetMapping(value = "/death-reasons/{id}",consumes = "application/json")
    DeathReasonDTO getMetDeathReason(@PathVariable Long id);

    @Cacheable("getAllConsEducationSystemTypes")
    @GetMapping(value = "/education-system-types",consumes = "application/json")
    List<EducationSystemTypeDTO> getAllConsEducationSystemTypes();

    @Cacheable("getConsEducationSystemType")
    @GetMapping(value = "/education-system-types/{id}",consumes = "application/json")
    EducationSystemTypeDTO getConsEducationSystemType(@PathVariable Long id);






    @Cacheable("getMetProfession")
    @GetMapping(value = "/professions/{id}",consumes = "application/json")
    ProfessionDTO getMetProfession(@PathVariable Long id);

    @Cacheable("getAllAccommodationType")
    @GetMapping(value = "/accommodation_types",consumes = "application/json")
    List<AccommodationTypeDTO> getAllMetAccommodationType();

    @Cacheable("getMetAccommodationType")
    @GetMapping(value = "/accommodation_types/{id}",consumes = "application/json")
    AccommodationTypeDTO getMetAccommodationType(@PathVariable Long id);

    @Cacheable("getAllAccommodationNature")
    @GetMapping(value = "/accommodation_natures",consumes = "application/json")
    List<AccommodationNatureDTO> getAllParAccommodationNature();

    @Cacheable("getMetAccommodationNature")
    @GetMapping(value = "/accommodation_natures/{id}",consumes = "application/json")
    AccommodationNatureDTO getParAccommodationNature(@PathVariable Long id);

    @Cacheable("getAllParTypeKafalat")
    @GetMapping(value = "/type-kafalat",consumes = "application/json")
    List<TypeKafalatDTO> getAllParTypeKafalat();

    @Cacheable("getParTypeKafalat")
    @GetMapping(value = "/type-kafalat/{id}",consumes = "application/json")
    TypeKafalatDTO getParTypeKafalat(@PathVariable Long id);

    @Cacheable("getAllParTypePriseEnCharge")
    @GetMapping(value = "/type-prise-en-charge",consumes = "application/json")
    List<TypePriseEnChargeDTO> getAllParTypePriseEnCharge();

    @Cacheable("getParTypePriseEnCharge")
    @GetMapping(value = "/type-prise-en-charge/{id}",consumes = "application/json")
    TypePriseEnChargeDTO getParTypePriseEnCharge(@PathVariable Long id);

    @Cacheable("getAllCategoryBeneficiary")
    @GetMapping(value = "/category-beneficiaries", consumes = "application/json")
    List<CategoryBeneficiaryDTO> getAllCategoryBeneficiaries();

    //getAllConsClotureMotifTypes
    @Cacheable("getAllConsClotureMotifTypes")
    @GetMapping(value = "/cloture-motif-types", consumes = "application/json")
    List<ClotureMotifTypeDTO> getAllConsClotureMotifTypes();

    //getConsClotureMotifType
    @Cacheable("getConsClotureMotifType")
    @GetMapping(value = "/cloture-motif-types/{id}", consumes = "application/json")
    ClotureMotifTypeDTO getConsClotureMotifType(@PathVariable Long id);


    @Cacheable("getCategoryBeneficiary")
    @GetMapping(value = "/category-beneficiaries/{id}", consumes = "application/json")
    CategoryBeneficiaryDTO getCategoryBeneficiary(@PathVariable Long id);


    @Cacheable("getAllParStatuses")
    @GetMapping(value = "/statuses",consumes = "application/json")
    List<StatusDTO> getAllParStatuses();

    @Cacheable("getParStatus")
    @GetMapping(value = "/statuses/{id}",consumes = "application/json")
    StatusDTO getParStatus(@PathVariable Long id);



    @Cacheable("getAllParDonorStatuses")
    @GetMapping(value = "/donor_statuses",consumes = "application/json")
    List<DonorStatusDTO> getAllParDonorStatuses();

    @Cacheable("getParDonorStatus")
    @GetMapping(value = "/donor_statuses/{id}",consumes = "application/json")
    DonorStatusDTO getParDonorStatus(@PathVariable Long id);

    @Cacheable("getAllActionStatuses")
    @GetMapping(value = "/action_statuses",consumes = "application/json")
    List<ActionStatusDTO> getAllActionStatuses();

    @Cacheable("getActionStatus")
    @GetMapping(value = "/action_statuses/{id}",consumes = "application/json")
    ActionStatusDTO getActionStatus(@PathVariable Long id);

    @Cacheable("getAllParTypeIdentities")
    @GetMapping(value = "/type_identities",consumes = "application/json")
    List<TypeIdentityDTO> getAllParTypeIdentities();

    @Cacheable("getParTypeIdentity")
    @GetMapping(value = "/type_identities/{id}",consumes = "application/json")
    TypeIdentityDTO getParTypeIdentity(@PathVariable Long id);

    @Cacheable("getAllParCountries")
    @GetMapping(value = "/countries",consumes = "application/json")
    List<CountryDTO> getAllParCountries();

    @Cacheable("getParCountry")
    @GetMapping(value = "/countries/{id}",consumes = "application/json")
    CountryDTO getParCountry(@PathVariable Long id);

    @Cacheable("getAllParRegions")
    @GetMapping(value = "/regions",consumes = "application/json")
    List<RegionDTO> getAllParRegions();

    @Cacheable("getParRegion")
    @GetMapping(value = "/regions/{id}",consumes = "application/json")
    RegionDTO getParRegion(@PathVariable Long id);

    @Cacheable("cities")
    @GetMapping(value = "/cities",consumes = "application/json")
    List<CityDTO> getAllParCities();

    @Cacheable("getParCity")
    @GetMapping(value = "/cities/{id}",consumes = "application/json")
    CityDTO getParCity(@PathVariable Long id);

    @Cacheable("getAllConsBeneficiaryDocumentTypes")
    @GetMapping(value = "/beneficiary_document_types", consumes = "application/json")
    List<BeneficiaryDocumentTypeDTO> getAllConsBeneficiaryDocumentTypes();

    @Cacheable("getConsBeneficiaryDocumentType")
    @GetMapping(value = "/beneficiary_document_types/{id}",consumes = "application/json")
    BeneficiaryDocumentTypeDTO getConsBeneficiaryDocumentType(@PathVariable Long id);

    @Cacheable("getAllConsTakeInChargeDocumentTypes")
    @GetMapping(value = "/take_in_charge_document_types", consumes = "application/json")
    List<TakenInChargeDocumentTypeDTO> getAllConsTakeInChargeDocumentTypes();

    @Cacheable("getConsTakeInChargeDocumentType")
    @GetMapping(value = "/take_in_charge_document_types/{id}",consumes = "application/json")
    TakenInChargeDocumentTypeDTO getConsTakeInChargeDocumentType(@PathVariable Long id);

    @Cacheable("getAllMetCategories")
    @GetMapping(value = "/categories",consumes = "application/json")
    List<CategoryDTO> getAllMetCategories();

    @Cacheable("getMetCategory")
    @GetMapping(value = "/categories/{id}",consumes = "application/json")
    CategoryDTO getMetCategory(@PathVariable Long id);

    @Cacheable("getAllMetServices")
    @GetMapping(value = "/services",consumes = "application/json")
    List<ServiceDTO> getAllMetServices();

    @Cacheable("getMetService")
    @GetMapping(value = "/services/{id}",consumes = "application/json")
    ServiceDTO getMetService(@PathVariable Long id);

    @Cacheable("getAllMetCanalCommunications")
    @GetMapping(value = "/canal_communications",consumes = "application/json")
    List<CanalCommunicationDTO> getAllMetCanalCommunications();

    @Cacheable("getMetCanalCommunication")
    @GetMapping(value = "/canal_communications/{id}",consumes = "application/json")
    CanalCommunicationDTO getMetCanalCommunication(@PathVariable Long id);

    @Cacheable("getAllParLanguageCommunications")
    @GetMapping(value = "/language_communications",consumes = "application/json")
    List<LanguageCommunicationDTO> getAllParLanguageCommunications();

    @Cacheable("getParLanguageCommunication")
    @GetMapping(value = "/language_communications/{id}",consumes = "application/json")
    LanguageCommunicationDTO getParLanguageCommunication(@PathVariable Long id);

    @Cacheable("getAllConsTypeDonorDocuments")
    @GetMapping(value = "/type_donor_documents",consumes = "application/json")
   List<TypeDocumentDonorDTO> getAllConsTypeDonorDocuments();

    @Cacheable("getConsTypeDonorDocument")
    @GetMapping(value = "/type_donor_documents/{id}",consumes = "application/json")
    TypeDocumentDonorDTO getConsTypeDonorDocument( @PathVariable Long id);

    @Cacheable("getAllMetActivitySectors")
    @GetMapping(value = "/activity_sectors",consumes = "application/json")
    List<ActivitySectorDTO> getAllMetActivitySectors();

    @Cacheable("getMetActivitySector")
    @GetMapping(value = "/activity_sectors/{id}",consumes = "application/json")
    ActivitySectorDTO getMetActivitySector(@PathVariable Long id);

    @Cacheable("getAllMetTypeDonorMorals")
    @GetMapping(value = "/type_donor_morals",consumes = "application/json")
    List<TypeDonorMoralDTO> getAllMetTypeDonorMorals();

    @Cacheable("getMetTypeDonorMoral")
    @GetMapping(value = "/type_donor_morals/{id}",consumes = "application/json")
    TypeDonorMoralDTO getMetTypeDonorMoral(@PathVariable Long id);

    @Cacheable("getAllMetDonorContactFunctions")
    @GetMapping(value = "/donor_contact_functions",consumes = "application/json")
    List<DonorContactFunctionDTO> getAllMetDonorContactFunctions();

    @Cacheable("getMetDonorContactFunction")
    @GetMapping(value = "/donor_contact_functions/{id}",consumes = "application/json")
    DonorContactFunctionDTO getMetDonorContactFunction(@PathVariable Long id);

    @Cacheable("getAllParSchoolLevels")
    @GetMapping(value = "/school_levels",consumes = "application/json")
    List<SchoolLevelDTO> getAllParSchoolLevels();

    @Cacheable("getParSchoolLevel")
    @GetMapping(value = "/school_levels/{id}",consumes = "application/json")
    SchoolLevelDTO getParSchoolLevel(@PathVariable Long id);

    @Cacheable("getAllMetFamilyRelationships")
    @GetMapping(value = "/family_relationships",consumes = "application/json")
    List<FamilyRelationshipDTO> getAllMetFamilyRelationships();

    @Cacheable("getMetFamilyRelationship")
    @GetMapping(value = "family_relationships/{id}",consumes = "application/json")
    FamilyRelationshipDTO getMetFamilyRelationship(@PathVariable Long id);

    @Cacheable("getAllConsFamilyDocumentTypes")
    @GetMapping(value = "/family_document_types",consumes = "application/json")
    List<FamilyDocumentTypeDTO> getAllConsFamilyDocumentTypes();

    @Cacheable("consFamilyDocumentTypeDTO")
    @GetMapping(value = "/family_document_types/{id}",consumes = "application/json")
    FamilyDocumentTypeDTO consFamilyDocumentTypeDTO(@PathVariable Long id);

    @Cacheable("getAllConsCardTypes")
    @GetMapping(value = "/card_types",consumes = "application/json")
    List<CardTypeDTO> getAllConsCardTypes();

    @Cacheable("getConsCardType")
    @GetMapping(value = "/card_types/{id}",consumes = "application/json")
    CardTypeDTO getConsCardType(@PathVariable Long id);


    @Cacheable("getAllMetIncomeSources")
    @GetMapping(value = "/income_sources",consumes = "application/json")
    List<IncomeSourceDTO> getAllMetIncomeSources();

    @Cacheable("getMetIncomeSource")
    @GetMapping(value = "/income_sources/{id}",consumes = "application/json")
    IncomeSourceDTO getMetIncomeSource(@PathVariable Long id);

    @Cacheable("isAuthenticated")
    @GetMapping(value = "/authenticate", consumes = "application/json")
    AuthenticationResponseDTO isAuthenticated(AuthenticationDTO build);

    @Cacheable("getAllMetEps")
    @GetMapping(value = "/eps",consumes = "application/json")
    List<EpsDTO> getAllMetEps();

    @Cacheable("getMetEps")
    @GetMapping(value = "/eps/{id}",consumes = "application/json")
    EpsDTO getMetEps(@PathVariable Long id);

    @Cacheable("getAllMetDiseases")
    @GetMapping(value = "/diseases",consumes = "application/json")
    List<DiseasesDTO> getAllMetDiseases();

    @Cacheable("getMetDiseases")
    @GetMapping(value = "/diseases/{id}",consumes = "application/json")
    DiseasesDTO getMetDiseases(@PathVariable Long id);

    @Cacheable("getAllMetAllergies")
    @GetMapping(value = "/allergies",consumes = "application/json")
    List<AllergiesDTO> getAllMetAllergies();

    @Cacheable("getMetAllergies")
    @GetMapping(value = "/allergies/{id}",consumes = "application/json")
    AllergiesDTO getMetAllergies(@PathVariable Long id);

    @GetMapping(value = "/handicap_types",consumes = "application/json")
    List<HandicapTypeDTO> getAllMetHandicapTypes();
    @Cacheable("getMetHandicapType")
    @GetMapping(value = "/handicap_types/{id}",consumes = "application/json")
    HandicapTypeDTO getMetHandicapType(@PathVariable Long id);

    @Cacheable("getAllMetDiseaseTreatmentTypes")
    @GetMapping(value = "/disease_treatment_types",consumes = "application/json")
    List<DiseaseTreatmentTypeDTO> getAllMetDiseaseTreatmentTypes();

    @Cacheable("getMetDiseaseTreatmentType")
    @GetMapping(value = "/disease_treatment_types/{id}",consumes = "application/json")
    DiseaseTreatmentTypeDTO getMetDiseaseTreatmentType(@PathVariable Long id);

    @Cacheable("getAllParHonors")
    @GetMapping(value = "/honors",consumes = "application/json")
    List<HonorDTO> getAllParHonors();

    @Cacheable("getParHonor")
    @GetMapping(value = "/honors/{id}",consumes = "application/json")
    HonorDTO getParHonor(@PathVariable Long id);

    @Cacheable("getAllParSchoolYears")
    @GetMapping(value = "/school_years",consumes = "application/json")
    List<SchoolYearDTO> getAllParSchoolYears();

    @Cacheable("getParSchoolYear")
    @GetMapping(value = "/school_years/{id}",consumes = "application/json")
    SchoolYearDTO getParSchoolYear(@PathVariable Long id);

    @Cacheable("getAllParMajors")
    @GetMapping(value = "/majors",consumes = "application/json")
    List<MajorDTO> getAllParMajors();

    @Cacheable("getParMajor")
    @GetMapping(value = "/majors/{id}",consumes = "application/json")
    MajorDTO getParMajor(@PathVariable Long id);

    @Cacheable("getAllMetScholarships")
    @GetMapping(value = "/scholarships",consumes = "application/json")
    List<ScholarshipDTO> getAllMetScholarships();

    @Cacheable("getMetScholarship")
    @GetMapping(value = "/scholarships/{id}",consumes = "application/json")
    ScholarshipDTO getMetScholarship(@PathVariable Long id);

    @Cacheable("getAllMetCanalDonations")
    @GetMapping(value = "/canal_donations",consumes = "application/json")
    List<CanalDonationDTO> getAllMetCanalDonations();

    @Cacheable("getMetCanalDonation")
    @GetMapping(value = "/canal_donations/{id}",consumes = "application/json")
    CanalDonationDTO getMetCanalDonation(@PathVariable Long id);

    @Cacheable("getAllConsTypeDonationDocuments")
    @GetMapping(value = "/type_donation_documents",consumes = "application/json")
    List<TypeDonationDocumentDTO> getAllConsTypeDonationDocuments();

    @Cacheable("getConsTypeDonationDocument")
    @GetMapping(value = "/type_donation_documents/{id}",consumes = "application/json")
    TypeDonationDocumentDTO getConsTypeDonationDocument(@PathVariable Long id);

    @Cacheable("getAllConsTypeProductNatures")
    @GetMapping(value = "/type_product_natures",consumes = "application/json")
    List<TypeProductNatureDTO> getAllConsTypeProductNatures();

    @Cacheable("getConsTypeProductNature")
    @GetMapping(value = "/type_product_natures/{id}",consumes = "application/json")
    TypeProductNatureDTO getConsTypeProductNature(@PathVariable Long id);

    @Cacheable("getAllParProductUnits")
    @GetMapping(value = "/product_units",consumes = "application/json")
    List<ProductUnitDTO> getAllParProductUnits();

    @Cacheable("getParProductUnit")
    @GetMapping(value = "/product_units/{id}",consumes = "application/json")
    ProductUnitDTO getParProductUnit(@PathVariable Long id);

    @Cacheable("getAllMetProductNatures")
    @GetMapping(value = "/product_natures",consumes = "application/json")
    List<ProductNatureDTO> getAllMetProductNatures();

    @Cacheable("getMetProductNature")
    @GetMapping(value = "/product_natures/{id}",consumes = "application/json")
    ProductNatureDTO getMetProductNature(@PathVariable Long id);


    @Cacheable("getAllSourceBeneficiary")
    @GetMapping(value = "/source_beneficiary",consumes = "application/json")
    List<SourceBeneficiaryDTO> getAllSourceBeneficiary();

    @Cacheable("getSourceBeneficiary")
    @GetMapping(value = "/source_beneficiary/{id}",consumes = "application/json")
    SourceBeneficiaryDTO getSourceBeneficiary(@PathVariable Long id);


}
