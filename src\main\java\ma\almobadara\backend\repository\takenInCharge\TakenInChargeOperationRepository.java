package ma.almobadara.backend.repository.takenInCharge;

import ma.almobadara.backend.model.takenInCharge.TakenInChargeDonor;
import ma.almobadara.backend.model.takenInCharge.TakenInChargeOperation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TakenInChargeOperationRepository extends JpaRepository<TakenInChargeOperation, Long>, JpaSpecificationExecutor<TakenInChargeOperation> {

    List<TakenInChargeOperation> findByTakenInChargeDonor(TakenInChargeDonor takenInChargeDonor);

    List<TakenInChargeOperation> findTakenInChargeOperationsByTakenInChargeDonor(TakenInChargeDonor takenInChargeDonor);

    //countByTakenInChargeDonorId
    Long countByTakenInChargeDonorId(Long takenInChargeDonorId);


}
