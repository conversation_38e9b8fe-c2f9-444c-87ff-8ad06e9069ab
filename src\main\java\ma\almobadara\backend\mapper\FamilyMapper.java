package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.*;
import ma.almobadara.backend.dto.beneficiary.*;
import ma.almobadara.backend.dto.donation.DonationDTO;
import ma.almobadara.backend.dto.donor.DonorDTO;
import ma.almobadara.backend.dto.exportentities.FamilyExportDTO;
import ma.almobadara.backend.dto.family.*;
import ma.almobadara.backend.dto.takenInCharge.TakenInChargeBeneficiaryDTO;
import ma.almobadara.backend.dto.takenInCharge.TakenInChargeDTO;
import ma.almobadara.backend.dto.takenInCharge.TakenInChargeDonorDTO;
import ma.almobadara.backend.dto.takenInCharge.TakenInChargeOperationDTO;
import ma.almobadara.backend.model.beneficiary.*;
import ma.almobadara.backend.model.donation.Donation;
import ma.almobadara.backend.model.donor.Donor;
import ma.almobadara.backend.model.takenInCharge.TakenInChargeDonor;
import ma.almobadara.backend.model.family.*;
import ma.almobadara.backend.model.takenInCharge.TakenInCharge;
import ma.almobadara.backend.model.takenInCharge.TakenInChargeBeneficiary;
import ma.almobadara.backend.model.takenInCharge.TakenInChargeOperation;
import org.mapstruct.IterableMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

@Mapper(componentModel = "spring")
public interface FamilyMapper {

	FamilyDTO familyToFamilyDTO(Family family);

	Family familyDTOToFamily(FamilyDTO familyDTO);

	Iterable<Family> familyDTOToFamily(Iterable<FamilyDTO> familyDTOS);

	@Mapping(target = "family", ignore = true)
	@Mapping(source = "familyRelationshipId", target = "familyRelationship.id")
	FamilyMemberDTO familyMemberToFamilyMemberDTO(FamilyMember familyMember);

	@Mapping(target = "family", ignore = true)
	@Mapping(source = "familyRelationship.id", target = "familyRelationshipId")
	FamilyMember familyMemberDTOToFamilyMember(FamilyMemberDTO familyMemberDTO);

	NoteFamilyDTO familyNoteToFamilyNoteDTO(NoteFamily familyNote);

	DocumentFamilyDTO familyDocumentToFamilyDocumentDTO(DocumentFamily familyDocument);

	@Mapping(target = "family", ignore = true)
	DocumentFamily familyDocumentDTOToFamilyDocument(DocumentFamilyDTO familyDocumentDTO);

	DocumentFamilyMemberDTO familyMemberDocumentToFamilyMemberDocumentDTO(DocumentFamilyMember familyMemberDocument);

	@Mapping(target = "familyMember", ignore = true)
	DocumentFamilyMember familyMemberDocumentDTOToFamilyMemberDocument(DocumentFamilyMemberDTO familyMemberDocumentDTO);

	@Mapping(target = "familyMember", qualifiedByName = "mapFamilyMemberForExternalIncome")
	@Mapping(source = "incomeSourceId", target = "incomeSource.id")
	ExternalIncomeDTO externalIncomeToExternalIncomeDTO(ExternalIncome externalIncome);

	@Mapping(target = "familyMember", qualifiedByName = "mapFamilyMemberForExternalIncomeDTO")
	@Mapping(source = "incomeSource.id", target = "incomeSourceId")
	ExternalIncome externalIncomeDTOToExternalIncome(ExternalIncomeDTO externalIncomeDTO);

	@Named("mapFamilyMemberForExternalIncome")
	@Mapping(target = "family", ignore = true)
	@Mapping(target = "externalIncomes", ignore = true)
	@Mapping(target = "person", ignore = true)
	FamilyMemberDTO familyMemberToFamilyMemberDTOForExternalIncome(FamilyMember familyMember);

	@Named("mapFamilyMemberForExternalIncomeDTO")
	@Mapping(target = "family", ignore = true)
	@Mapping(target = "externalIncomes", ignore = true)
	@Mapping(target = "person", ignore = true)
	FamilyMember familyMemberDTOToFamilyMemberForExternalIncome(FamilyMemberDTO familyMemberDTO);


	//--------------- family without notes & documents

	@Mapping(target = "familyMembers", qualifiedByName = "mapFamilyMemberForBeneficiaryFiche")
	FamilyDTO familyToFamilyDTOWithoutNotesDocuments(Family family);

	@Named("mapFamilyMemberForBeneficiaryFiche")
	@Mapping(target = "externalIncomes", ignore = true)
	@Mapping(target = "family", ignore = true)
	@Mapping(source = "familyRelationshipId", target = "familyRelationship.id")
	FamilyMemberDTO familyMemberToFamilyMemberDTOForBeneficiaryFiche(FamilyMember familyMember);


	//------------------------ For GetFamilyList
	@Named("mapFamilyForList")
	@Mapping(target = "familyMembers", qualifiedByName = "mapFamilyMembersForList")
	FamilyDTO familyToFamilyDTOForList(Family family);

	@Named("mapFamilyMembersForList")
	@Mapping(target = "family", ignore = true)
	@Mapping(target = "externalIncomes", ignore = true)
	@Mapping(source = "familyRelationshipId", target = "familyRelationship.id")
	@Mapping(target = "person", qualifiedByName = "mapPersonForList")
	FamilyMemberDTO familyMemberToFamilyMemberDTOForList(FamilyMember familyMember);

	@Named("mapPersonForList")
	@Mapping(target = "bankCards", ignore = true)
	@Mapping(target = "picture", ignore = true)
	@Mapping(target = "pictureBase64", ignore = true)
	@Mapping(target = "familyMember", ignore = true)
	@Mapping(source = "typeIdentityId", target = "typeIdentity.id")
	@Mapping(source = "cityId", target = "city.id")
	@Mapping(source = "schoolLevelId", target = "schoolLevel.id")
	@Mapping(source = "professionId", target = "profession.id")
	@Mapping(source = "deathReasonId", target = "deathReasonSelected.id")
	PersonDTO personToPersonDTOLForList(Person person);

	@IterableMapping(qualifiedByName = "mapFamilyForList")
	Iterable<FamilyDTO> familyToFamilyDTO(Iterable<Family> families);


	//------------------------ For GetFamilyListForBeneficiary
	@Named("mapFamilyForBeneficiary")
	@Mapping(target = "familyMembers", qualifiedByName = "mapFamilyMembersForBeneficiary")
	FamilyDTO familyToFamilyDTOForBeneficiary(Family family);

	@Named("mapFamilyMembersForBeneficiary")
	@Mapping(target = "family", ignore = true)
	@Mapping(target = "externalIncomes", ignore = true)
	@Mapping(source = "familyRelationshipId", target = "familyRelationship.id")
	@Mapping(target = "person", qualifiedByName = "mapPersonForBeneficiary")
	FamilyMemberDTO familyMemberToFamilyMemberDTOForBeneficiary(FamilyMember familyMember);

	@Named("mapPersonForBeneficiary")
	@Mapping(target = "bankCards", ignore = true)
	@Mapping(target = "beneficiary", ignore = true)
	@Mapping(target = "familyMember", ignore = true)
	@Mapping(source = "typeIdentityId", target = "typeIdentity.id")
	@Mapping(source = "cityId", target = "city.id")
	@Mapping(source = "schoolLevelId", target = "schoolLevel.id")
	@Mapping(source = "professionId", target = "profession.id")
	@Mapping(source = "deathReasonId", target = "deathReasonSelected.id")
	PersonDTO personToPersonDTOLForBeneficiary(Person person);

	@IterableMapping(qualifiedByName = "mapFamilyForBeneficiary")
	Iterable<FamilyDTO> familyToFamilyDTOForBeneficiary(Iterable<Family> family);


	//--External
	@Mapping(target = "person", ignore = true)
	@Mapping(target = "epsResidents", ignore = true)
	@Mapping(target = "diseaseTreatments", ignore = true)
	@Mapping(target = "allergies", ignore = true)
	@Mapping(target = "diseases", ignore = true)
	@Mapping(target = "notes", ignore = true)
	@Mapping(target = "documents", ignore = true)
	@Mapping(target = "educations", ignore = true)
	@Mapping(target = "scholarshipBeneficiaries", ignore = true)
	BeneficiaryDTO beneficiaryToBeneficiaryDTO(Beneficiary beneficiary);

	@Mapping(target = "beneficiary", ignore = true)
    DocumentBeneficiary beneficiaryDocumentDTOToBeneficiaryDocument(DocumentBeneficiaryDTO documentDTO);

	@Mapping(target = "beneficiary", ignore = true)
	@Mapping(source = "city.id", target = "cityId")
	@Mapping(source = "schoolYear.id", target = "schoolYearId")
	@Mapping(source = "schoolLevel.id", target = "schoolLevelId")
	@Mapping(source = "honor.id", target = "honorId")
	@Mapping(source = "major.id", target = "majorId")
	Education educationDTOToEducation(EducationDTO educationDTO);

	@Mapping(target = "beneficiary", ignore = true)
	@Mapping(source = "serviceId", target = "service.id")
	@Mapping(source = "statusId", target = "status.id")
	BeneficiaryServiceDTO beneficiaryServiceToBeneficiaryServiceDTO(BeneficiaryService beneficiaryService);

	@Mapping(target = "beneficiary", ignore = true)
	@Mapping(source = "service.id", target = "serviceId")
	@Mapping(source = "status.id", target = "statusId")
	BeneficiaryService beneficiaryServiceDTOToBeneficiaryService(BeneficiaryServiceDTO beneficiaryServiceDTO);

	@Mapping(target = "beneficiary", ignore = true)
	@Mapping(source = "eps.id", target = "epsId")
	EpsResident epsResidentDTOToEpsResident(EpsResidentDTO epsResidentDTO);

	@Mapping(target = "beneficiary", ignore = true)
	@Mapping(source = "type.id", target = "typeId")
	DiseaseTreatment diseaseTreatmentDTOToDiseaseTreatment(DiseaseTreatmentDTO diseaseTreatmentDTO);

	@Mapping(target = "beneficiary", ignore = true)
	@Mapping(source = "scholarship.id", target = "scholarshipId")
	ScholarshipBeneficiary scholarshipBeneficiaryDTOToScholarshipBeneficiary(ScholarshipBeneficiaryDTO scholarshipBeneficiaryDTO);

	@Mapping(target = "beneficiary", ignore = true)
	@Mapping(source = "handicapTypeId", target = "handicapType.id")
	BeneficiaryHandicapDto beneficiaryHandicapToBeneficiaryHandicapDto(BeneficiaryHandicap beneficiaryHandicap);

	@Mapping(target = "beneficiary", ignore = true)
	@Mapping(source = "handicapType.id", target = "handicapTypeId")
	BeneficiaryHandicap beneficiaryHandicapDtoToBeneficiaryHandicap(BeneficiaryHandicapDto beneficiaryHandicapDto);

	@Mapping(target = "beneficiary", ignore = true)
	@Mapping(target = "familyMember", ignore = true)
	@Mapping(source = "typeIdentityId", target = "typeIdentity.id")
	@Mapping(source = "cityId", target = "city.id")
	@Mapping(source = "schoolLevelId", target = "schoolLevel.id")
	@Mapping(source = "professionId", target = "profession.id")
	@Mapping(source = "deathReasonId", target = "deathReasonSelected.id")
    PersonDTO personToPersonDTO(Person person);

	@Mapping(target = "beneficiary", ignore = true)
	@Mapping(target = "familyMember", ignore = true)
	@Mapping(source = "city.id", target = "cityId")
	@Mapping(source = "typeIdentity.id", target = "typeIdentityId")
	@Mapping(source = "schoolLevel.id", target = "schoolLevelId")
	@Mapping(source = "profession.id", target = "professionId")
	@Mapping(source = "deathReasonSelected.id", target = "deathReasonId")
	Person personDTOToPerson(PersonDTO personDTO);

	@Mapping(target = "person", ignore = true)
	@Mapping(source = "cardTypeId", target = "cardType.id")
	BankCardDTO bankCardToBankCardDTO(BankCard bankCard);

	@Mapping(target = "person", ignore = true)
	@Mapping(source = "cardType.id", target = "cardTypeId")
	BankCard bankCardDTOToBankCard(BankCardDTO bankCardDTO);

	@Mapping(target = "beneficiary", ignore = true)
	TakenInChargeBeneficiary takenInChargeBeneficiaryToTakenInChargeBeneficiaryDTO(TakenInChargeBeneficiaryDTO takenInChargeBeneficiaryDTO);

	@Mapping(target = "beneficiary", ignore = true)
	TakenInChargeBeneficiaryDTO takenInChargeBeneficiaryDTOToTakenInChargeBeneficiary(TakenInChargeBeneficiary takenInChargeBeneficiary);

	@Mapping(target = "takenInCharge", ignore = true)
	TakenInChargeDonor takenInChargeDonorTotTakenInChargeDonorDTO(TakenInChargeDonorDTO takenInChargeDonorDTO);

	@Mapping(target = "takenInCharge", ignore = true)
	TakenInChargeDonorDTO takenInChargeDonorDTOToTakenInChargeDonor(TakenInChargeDonor takenInChargeDonor);

	@Mapping(target = "takenInChargeDonor", ignore = true)
	TakenInChargeOperation takenInChargeOperationDTOToTakenInChargeOperation(TakenInChargeOperationDTO takenInChargeOperationDTO);

	@Mapping(target = "takenInChargeDonor", ignore = true)
	TakenInChargeOperationDTO takenInChargeOperationToTakenInChargeOperationDTO(TakenInChargeOperation takenInChargeOperation);

	@Mapping(target = "takenInChargeDonors", ignore = true)
	@Mapping(target = "documentsDonors", ignore = true)
	Donor donorDTOToDonor(DonorDTO donorDTO);

	@Mapping(target = "takenInChargeDonors", ignore = true)
	@Mapping(source = "documentsDonors", target = "documentDonors")
	DonorDTO donorToDonorDTO(Donor donor);

	@Mapping(target = "donor", ignore = true)
	@Mapping(target = "documentDonations", ignore = true)
	@Mapping(target = "donationProductNatures", ignore = true)
	Donation donationDTOToDonation(DonationDTO donationDTO);

	@Mapping(target = "donor", ignore = true)
	@Mapping(target = "documentDonations", ignore = true)
	@Mapping(target = "donationProductNatures", ignore = true)
	DonationDTO donationToDonationDTO(Donation donation);

	@Mapping(target = "takenInChargeBeneficiaries", ignore = true)
	TakenInChargeDTO takenInChargeToTakenInChargeDTO(TakenInCharge takenInCharge);

	@Mapping(target = "takenInChargeBeneficiaries", ignore = true)
	TakenInCharge takenInChargeDTOToTakenInCharge(TakenInChargeDTO takenInChargeDTO);



	@Mapping(source = "code", target = "Code")
	FamilyAuditDto familyDtoToFamilyAuditDto(FamilyDTO FamilyDTO);
	@Mapping(target = "code", source = "family.code")
	@Mapping(target = "createdAt", source = "family.createdAt")
	@Mapping(target = "numberOfFamilyMembers", expression = "java(family.getFamilyMembers().size())")
	@Mapping(target = "familyName", expression = "java(getFamilyName(family))")
	@Mapping(target = "numberOfBeneficiaries", expression = "java(getNumberOfBeneficiaries(family))")
	FamilyExportDTO familyToFamilyExportDTO(Family family);

	default String getFamilyName(Family family) {
		return family.getFamilyMembers().stream()
				.filter(fm -> fm.getFamilyRelationshipId() == 1)
				.findFirst()
				.map(p -> p.getPerson().getLastName())
				.orElse(null);
	}

	default long getNumberOfBeneficiaries(Family family) {
		return family.getFamilyMembers().stream()
				.filter(fm -> fm.getPerson().getBeneficiary() != null)
				.count();
	}
}


