package ma.almobadara.backend.repository.beneficiary;

import ma.almobadara.backend.model.beneficiary.AgendaRapport;
import ma.almobadara.backend.model.beneficiary.Rapport;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Repository
public interface AgendaRapportRepository extends JpaRepository<AgendaRapport, Long> {

    boolean existsByBeneficiaryIdAndYear(Long beneficiaryId, String year);

    List<AgendaRapport> findByBeneficiaryIdAndYear(Long beneficiaryId, String year);

    Optional<AgendaRapport> findByRapportId(Long rapportId);

    Page<AgendaRapport> findByBeneficiary_IdOrderByModifiedAtDesc(Long beneficiaryId, Pageable pageable);

//    @Query("SELECT ar FROM AgendaRapport ar WHERE ar.beneficiary.zone.assistant.id = :assistantId")
//    Page<AgendaRapport> findByAssistantId(@Param("assistantId") Long assistantId, Pageable pageable);


    @Query("SELECT ar FROM AgendaRapport ar " +
            "JOIN AssistantZone az ON az.zone.id = ar.beneficiary.zone.id " +
            "WHERE az.assistant.id = :assistantId")
    List<AgendaRapport> findAllByAssistantId(@Param("assistantId") Long assistantId);

    Optional<AgendaRapport> findByRapport(Rapport rapport);

    @Query(
            value = """
        SELECT ar.*
        FROM agenda_rapport ar
        JOIN beneficiary b ON b.id = ar.beneficiary_id
        JOIN assistant_zone az ON b.zone_id = az.zone_id
        WHERE az.assistant_id = :assistantId
        ORDER BY ar.modified_at DESC
        """,
            nativeQuery = true
    )
    Page<AgendaRapport> findAllByAssistantId(
            @Param("assistantId") Long assistantId,
            Pageable pageable
    );


    List<AgendaRapport> findByDatePlanned(Date datePlanned);


    @Query("SELECT MAX(a.numberRapport) FROM AgendaRapport a " +
            "WHERE a.beneficiary.id = :beneficiaryId " +
            "AND YEAR(a.datePlanned) = :year")
    Long findLastNumberRapportByBeneficiaryAndYear(@Param("beneficiaryId") Long beneficiaryId,
                                                   @Param("year") int year);

    Optional<AgendaRapport> findByBeneficiaryIdAndRapportId(Long beneficiaryId, Long rapportId);
}
