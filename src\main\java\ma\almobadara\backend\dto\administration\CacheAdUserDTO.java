package ma.almobadara.backend.dto.administration;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor

public class CacheAdUserDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    private Long id;
    private Long assistantId;
    private Long zoneId;
    private String azureDirectoryId;
    private String firstName;
    private String lastName;
    private String mail;
    private LocalDateTime creationDate;
    private LocalDateTime updateDate;
    private ProfileDTO profile;
    private RoleDTO role;
    private LocalDateTime lastLoginInDate;
    private List<Long> zoneIds;
}
