package ma.almobadara.backend.controller.administration;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.config.HasAccessToModule;
import ma.almobadara.backend.dto.administration.ProfileDTO;
import ma.almobadara.backend.enumeration.Functionality;
import ma.almobadara.backend.enumeration.Module;
import ma.almobadara.backend.exceptions.DuplicateFunctionalitiesException;
import ma.almobadara.backend.exceptions.DuplicateProfileException;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.service.administration.ProfileService;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping(value ="/profiles")
public class UserProfileController {
    private final ProfileService profileService;
    @PostMapping(value= "/create", produces = MediaType.APPLICATION_JSON_VALUE)
    @HasAccessToModule(modules = {Module.USER}, functionalities = {Functionality.UPDATE})
    public ResponseEntity<Object> createProfile(@RequestBody ProfileDTO profileDTO) {
        log.info("Start resource createProfile with name: {}, functionalities: {}", profileDTO.getNameProfile() , profileDTO.getModuleFunctionalities());
        try {
            ProfileDTO createdProfile = profileService.createProfile(profileDTO);
            log.info("Profile created: {}", createdProfile);
            return ResponseEntity.status(HttpStatus.CREATED).body(createdProfile);
        } catch (DuplicateProfileException e) {
            log.error("Failed to create profile", e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("A profile with similar name already exists.");
        } catch (DuplicateFunctionalitiesException e) {
            log.error("Failed to create profile", e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("A profile with similar functionalities already exists.");
        } catch (Exception e) {
            log.error("Failed to create profile", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Failed to create profile.");
        } finally {
            log.info("End resource createProfile with id: {}", profileDTO.getId());
        }
    }

    @PutMapping("/{profileId}")
    @HasAccessToModule(modules = {Module.USER}, functionalities = {Functionality.UPDATE})
    public ResponseEntity<Object> updateProfile(@PathVariable Long profileId, @RequestBody ProfileDTO profileDTO) throws TechnicalException {
        log.info("Start resource updateProfile with name: {}, functionalities: {}", profileDTO.getNameProfile() , profileDTO.getModuleFunctionalities());
        try {
            ProfileDTO updatedProfile = profileService.updateProfile(profileId ,profileDTO);
            log.info("Profile updated: {}", updatedProfile);
            return ResponseEntity.status(HttpStatus.CREATED).body(updatedProfile);
        } catch (DuplicateProfileException e) {
            log.error("Failed to update profile", e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("A profile with similar name already exists.");
        } catch (UnsupportedOperationException e) {
            log.error("Failed to update profile", e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("The master admin profile cannot be updated.");
        } catch (DuplicateFunctionalitiesException e) {
            log.error("Failed to update profile", e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("A profile with similar functionalities already exists.");
        } catch (Exception e) {
            log.error("Failed to update profile", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Failed to update profile.");
        } finally {
            log.info("End resource updateProfile with id: {}", profileDTO.getId());
        }
    }

    @GetMapping(produces = MediaType.APPLICATION_JSON_VALUE)
    @HasAccessToModule(modules = {Module.USER}, functionalities = {Functionality.UPDATE})
    public ResponseEntity<Page<ProfileDTO>> getAllProfiles(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "5") int size,
            @RequestParam(required = false) String searchByName,
            @RequestParam(required = false) String[] modules
    ) {
        log.info("Start resource getAllProfiles page: {}, size: {}, searchByName: {}, modules: {}", page, size, searchByName, modules);
        Page<ProfileDTO> profiles = profileService.getAllProfiles(page, size, searchByName, modules);
        log.info("End resource getAllProfiles with Profiles size fetched: {}", profiles.getTotalElements());
        return ResponseEntity.ok(profiles);
    }





    @GetMapping(value = "/{profileId}", produces = MediaType.APPLICATION_JSON_VALUE)
    @HasAccessToModule(modules = {Module.USER}, functionalities = {Functionality.UPDATE})
    public ResponseEntity<ProfileDTO> getProfileById(@PathVariable Long profileId) {
        log.info("Start resource getProfileById: {}", profileId);
        Optional<ProfileDTO> profileDTO = profileService.getProfileById(profileId);
        if (profileDTO.isPresent()) {
            log.info("End resource getProfileById Profile found: {}", profileDTO.get());
            return ResponseEntity.ok(profileDTO.get());
        } else {
            log.info("End resource getProfileById Profile not found with ID: {}", profileId);
            return ResponseEntity.notFound().build();
        }
    }

    @DeleteMapping("/{profileId}")
    @HasAccessToModule(modules = {Module.USER}, functionalities = {Functionality.UPDATE})
    public ResponseEntity<Object> deleteProfile(@PathVariable Long profileId) {
        log.info("Start resource deleteProfile: {}", profileId);
        try {
            profileService.deleteProfile(profileId);
            log.info("End resource deleteProfile Profile deleted with ID: {}", profileId);
            return ResponseEntity.ok().build();
        } catch (TechnicalException e) {
            log.error("Error deleting profile with ID: {}", profileId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Error deleting profile: " + e.getMessage());
        } finally {
            log.info("End resource deleteProfile: {}", profileId);
        }
    }
}