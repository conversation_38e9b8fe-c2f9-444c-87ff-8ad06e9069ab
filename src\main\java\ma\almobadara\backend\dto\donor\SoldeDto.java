package ma.almobadara.backend.dto.donor;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;
import ma.almobadara.backend.dto.beneficiary.BeneficiaryReleveDto;
import ma.almobadara.backend.dto.referentiel.CategoryDTO;
import ma.almobadara.backend.dto.service.ServicesDTO;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
@JsonIgnoreProperties(value = { "executionDate" }, allowGetters = true)
public class SoldeDto {

    private Long id;
    private String code;
    private String name;
    private String type;
    private String typeDonationKafalat;
    private LocalDateTime dateExecution;
    private Double montantSortie;
    private ServicesDTO services;
    private Double totalSortie;
    private List<BeneficiaryReleveDto> beneficiaries;
    private CategoryDTO categoryDTO;
    private double total;
    private Double amountDisponible;
    private Double amountReserve;
    private Double solde;


}
