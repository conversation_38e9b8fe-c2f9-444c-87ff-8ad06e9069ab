CREATE TABLE role_privilege (
                                id BIGINT GE<PERSON>RATED BY DEFAULT AS IDENTITY PRIMARY KEY,
                                role_id BIGINT NOT NULL,
                                privilege_id BIGINT NOT NULL,
                                feature_id BIGINT NOT NULL,
                                creation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                update_date TIM<PERSON><PERSON><PERSON> DEFAULT CURRENT_TIMESTAMP NOT NULL,

                                CONSTRAINT fk_role FOREIGN KEY (role_id) REFERENCES role (id) ON DELETE CASCADE,
                                CONSTRAINT fk_privilege FOREIGN KEY (privilege_id) REFERENCES privilege (id) ON DELETE CASCADE,
                                CONSTRAINT fk_feature FOREIGN KEY (feature_id) REFERENCES feature (id) ON DELETE CASCADE
);

CREATE INDEX idx_role_privilege_role_id ON role_privilege(role_id);
CREATE INDEX idx_role_privilege_privilege_id ON role_privilege(privilege_id);
CREATE INDEX idx_role_privilege_feature_id ON role_privilege(feature_id);
