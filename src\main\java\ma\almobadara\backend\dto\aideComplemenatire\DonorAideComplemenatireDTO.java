package ma.almobadara.backend.dto.aideComplemenatire;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.util.List;


@Getter
@Setter
@SuperBuilder
@AllArgsConstructor
public class DonorAideComplemenatireDTO {
    private Long id;
    private String code;
    private String firstName;
    private String lastName;
    private String fullNameDonor;
    private String typeDonor;
    private Boolean isNature;
    private String status;
    private Long NumberOfBeneficiaries;
    private Double montantTotalDuDonateur;
    private Double montantPoserDuDonateur;
    private Double montantRestantDuDonateur;
    private Double montantReserverDuDonateur;
    private Boolean isRelatedToBeneficiary;
    private List<BeneficiaryAideComplemenatireDTO> beneficiaryAideComplemenatireDTOList;

    public DonorAideComplemenatireDTO() {
    }
}
