package ma.almobadara.backend.dto.referentiel;

import lombok.*;

import java.io.Serializable;
import java.util.Objects;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class TypeDonationDocumentDTO implements Serializable {

	private Long id;
	private String code;
	private String name;
	private String nameAr;
	private String nameEn;
	private String folderName;
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getFolderName() {
		return folderName;
	}
	public void setFolderName(String folderName) {
		this.folderName = folderName;
	}
	@Override
	public boolean equals(Object o) {
		if (this == o) {
			return true;
		}
		if (!(o instanceof TypeDonationDocumentDTO typeDonationDocumentDTO)) {
			return false;
		}

        if (this.id == null) {
			return false;
		}
		return Objects.equals(this.id, typeDonationDocumentDTO.id);
	}

	@Override
	public int hashCode() {
		return Objects.hash(this.id);
	}

	// prettier-ignore
	@Override
	public String toString() {
		return "ConsTypeDonationDocumentDTO{" +
				"id=" + getId() +
				", name='" + getName() + "'" +
				", folderName='" + getFolderName() + "'" +
				"}";
	}

}
