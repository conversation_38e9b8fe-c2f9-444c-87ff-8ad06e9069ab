package ma.almobadara.backend.dto.referentiel;

import lombok.*;
import org.springframework.hateoas.RepresentationModel;

import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class TypeIdentityDTO extends RepresentationModel<TypeIdentityDTO> implements Serializable {

	private static final long serialVersionUID = 5324290086598679830L;

	private Long id;

	private String code;
	private String name;
	private String nameAr;
	private String nameEn;

}
