CREATE TABLE agenda_rapport (
                                id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
                                status VARCHAR(255),
                                date_rapport TIMESTAMP,
                                date_validate TIMESTAMP,
                                date_planned TIMESTAMP,
                                beneficiary_id BIGINT,
                                donor_id BIGINT,
                                rapport_id BIGINT,

                                created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                                modified_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,

                                CONSTRAINT fk_agenda_rapport_beneficiary FOREIGN KEY (beneficiary_id)
                                    REFERENCES beneficiary(id) ON DELETE SET NULL,
                                CONSTRAINT fk_agenda_rapport_donor FOREIGN KEY (donor_id)
                                    REFERENCES donor(id) ON DELETE SET NULL,
                                CONSTRAINT fk_agenda_rapport_rapport FOREIGN KEY (rapport_id)
                                    REFERENCES rapport(id) ON DELETE SET NULL
);

ALTER TABLE rapport ADD COLUMN agenda_rapport_id BIGINT;

ALTER TABLE rapport ADD CONSTRAINT fk_rapport_agenda_rapport FOREIGN KEY (agenda_rapport_id)
    REFERENCES agenda_rapport(id) ON DELETE SET NULL;

ALTER TABLE donor ADD COLUMN agenda_rapport_id BIGINT;

ALTER TABLE donor ADD CONSTRAINT fk_donor_agenda_rapport FOREIGN KEY (agenda_rapport_id)
    REFERENCES agenda_rapport(id) ON DELETE SET NULL;


ALTER TABLE beneficiary ADD COLUMN agenda_rapport_id BIGINT,
    ADD COLUMN has_rapport BOOLEAN DEFAULT FALSE;

ALTER TABLE beneficiary ADD CONSTRAINT fk_beneficiary_agenda_rapport FOREIGN KEY (agenda_rapport_id)
    REFERENCES agenda_rapport(id) ON DELETE SET NULL;

ALTER TABLE rapport DROP COLUMN status;