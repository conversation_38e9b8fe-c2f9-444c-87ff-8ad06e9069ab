package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.beneficiary.AllergyBeneficiaryAddDto;
import ma.almobadara.backend.dto.beneficiary.BeneficiaryAllergyDto;
import ma.almobadara.backend.model.beneficiary.BeneficiaryAllergy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public interface BeneficiaryAllergyMapper {

    @Mapping(source = "allergyId", target = "allergy.id")
    BeneficiaryAllergyDto beneficiaryAllergyToBeneficiaryAllergyDto(BeneficiaryAllergy beneficiaryAllergy);

    Iterable<BeneficiaryAllergyDto> beneficiaryAllergyToBeneficiaryAllergyDto(Iterable<BeneficiaryAllergy> beneficiaryAllergies);

    @Mapping(source = "allergy.id", target = "allergyId")
    BeneficiaryAllergy beneficiaryAllergyDtoToBeneficiaryAllergy(BeneficiaryAllergyDto beneficiaryAllergyDto);

    Iterable<BeneficiaryAllergy> beneficiaryAllergyDtoToBeneficiaryAllergy(Iterable<BeneficiaryAllergyDto> beneficiaryAllergyDtos);

    Iterable<BeneficiaryAllergy> beneficiaryAllergyDtoToBeneficiaryAllergy(List<AllergyBeneficiaryAddDto> allergyBeneficiaryAddDtoList);

    BeneficiaryAllergy AllergyBeneficiaryAddDtoToBeneficiaryAllergy(AllergyBeneficiaryAddDto allergyBeneficiaryAddDto);

}
