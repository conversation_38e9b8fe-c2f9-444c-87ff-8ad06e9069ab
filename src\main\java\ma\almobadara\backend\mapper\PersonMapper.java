package ma.almobadara.backend.mapper;

import ma.almobadara.backend.model.beneficiary.Person;
import ma.almobadara.backend.dto.beneficiary.PersonDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface PersonMapper {

	@Mapping(target = "beneficiary", ignore = true)
	@Mapping(source = "typeIdentityId", target = "typeIdentity.id")
	@Mapping(source = "cityId", target = "city.id")
	@Mapping(source = "schoolLevelId", target = "schoolLevel.id")
	@Mapping(source = "professionId", target = "profession.id")
	@Mapping(source = "deathReasonId", target = "deathReasonSelected.id")
	PersonDTO personToPersonDTO(Person person);

	Iterable<PersonDTO> personToPersonDTO(Iterable<Person> persons);

	@Mapping(target = "beneficiary", ignore = true)
	@Mapping(source = "city.id", target = "cityId")
	@Mapping(source = "typeIdentity.id", target = "typeIdentityId")
	@Mapping(source = "schoolLevel.id", target = "schoolLevelId")
	@Mapping(source = "profession.id", target = "professionId")
	@Mapping(source = "deathReasonSelected.id", target = "deathReasonId")
	Person personDTOToPerson(PersonDTO personDTO);

	Iterable<Person> personDTOToPerson(Iterable<PersonDTO> personDTOS);


}
