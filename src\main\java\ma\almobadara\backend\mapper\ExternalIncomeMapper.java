package ma.almobadara.backend.mapper;


import ma.almobadara.backend.dto.ExternalIncomeDTO;
import ma.almobadara.backend.dto.family.FamilyMemberDTO;
import ma.almobadara.backend.dto.takenInCharge.ExternalIncomeAuditDto;
import ma.almobadara.backend.model.family.ExternalIncome;
import ma.almobadara.backend.model.family.FamilyMember;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface ExternalIncomeMapper {

	@Mapping(source = "incomeSourceId", target = "incomeSource.id")
	ExternalIncomeDTO externalIncomeToExternalIncomeDTO(ExternalIncome externalIncome);

	Iterable<ExternalIncomeDTO> externalIncomeToExternalIncomeDTO(Iterable<ExternalIncome> externalIncomes);

	@Mapping(source = "incomeSource.id", target = "incomeSourceId")
	ExternalIncome externalIncomeDTOToExternalIncome(ExternalIncomeDTO externalIncomeDTO);

	Iterable<ExternalIncome> externalIncomeDTOToExternalIncome(Iterable<ExternalIncomeDTO> externalIncomeDTOS);


	@Mapping(target = "externalIncomes", ignore = true)
	@Mapping(target = "person", ignore = true)
	@Mapping(target = "family", ignore = true)
	FamilyMemberDTO familyMemberToFamilyMemberDTO(FamilyMember familyMember);


	ExternalIncomeAuditDto externalIncomeAuditDTOToExternalIncomeDto(ExternalIncomeDTO externalIncomeDTO);

}
