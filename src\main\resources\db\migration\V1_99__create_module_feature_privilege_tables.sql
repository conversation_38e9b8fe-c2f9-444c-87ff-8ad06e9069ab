CREATE TABLE module (
                        id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
                        label VARCHAR(255) NOT NULL,
                        code VARCHAR(255) NOT NULL UNIQUE,
                        creation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
                        update_date TIM<PERSON><PERSON><PERSON> DEFAULT CURRENT_TIMESTAMP NOT NULL
);

CREATE TABLE feature (
                         id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
                         label VARCHAR(255) NOT NULL,
                         code VARCHAR(255) NOT NULL UNIQUE,
                         module_id BIGINT NOT NULL,
                         creation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
                         update_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
                         CONSTRAINT fk_feature_module FOREIGN KEY (module_id) REFERENCES module (id) ON DELETE CASCADE
);

CREATE TABLE privilege (
                           id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
                           label VARCHAR(255) NOT NULL,
                           code VARCHAR(255) NOT NULL UNIQUE,
                           creation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
                           update_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);
