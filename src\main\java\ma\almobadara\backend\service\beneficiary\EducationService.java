package ma.almobadara.backend.service.beneficiary;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.Audit.AuditApplicationService;
import ma.almobadara.backend.clients.RefFeignClient;
import ma.almobadara.backend.controller.referentiel.RefController;
import ma.almobadara.backend.dto.CityWithRegionAndCountryDTO;
import ma.almobadara.backend.dto.beneficiary.EducationDTO;
import ma.almobadara.backend.dto.referentiel.*;
import ma.almobadara.backend.mapper.EducationMapper;
import ma.almobadara.backend.model.beneficiary.Beneficiary;
import ma.almobadara.backend.model.beneficiary.Education;
import ma.almobadara.backend.repository.beneficiary.BeneficiaryRepository;
import ma.almobadara.backend.repository.beneficiary.EducationRepository;
import ma.almobadara.backend.util.times.TimeWatch;
import org.springframework.data.rest.webmvc.ResourceNotFoundException;
import org.springframework.stereotype.Service;

import java.util.*;

import static ma.almobadara.backend.service.administration.CacheAdUserService.getUsernameFromJwt;
import static ma.almobadara.backend.util.constants.GlobalConstants.*;

@RequiredArgsConstructor
@Slf4j
@Service
public class EducationService {

    private final EducationMapper educationMapper;
    private final EducationRepository educationRepository;
    private final BeneficiaryRepository beneficiaryRepository;
    private final RefFeignClient refFeignClient;
    private final RefController refController;
    private final AuditApplicationService auditApplicationService;

    public void deleteEducation(Long id) {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service Delete Education by ID : {}", id);

        // Retrieve the existing education record to be deleted
        Education education = educationRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Education not found with ID: " + id));

        // Proceed with deletion
        Optional<Beneficiary> beneficiary =beneficiaryRepository.findById(education.getBeneficiary().getId());

        Map<String,String> newparams=prepareEducationForAudit(education);
        String newAudit=education.getAudit(newparams,beneficiary.get().getPerson().getFirstName()+" - "+beneficiary.get().getPerson().getLastName());
        // Proceed with deletion
        auditApplicationService.audit("Suppression d'un Bource pour  Beneficiare : " + beneficiary.get().getCode(), getUsernameFromJwt(), "Add Family Member",
                newAudit,null, BENEFICIAIRE, DELETE);
        educationRepository.deleteById(id);
        log.debug("End service Delete Education by ID : {}, took {}", id, watch.toMS());
    }


    public Education addEducationToBeneficiary(Beneficiary beneficiary, EducationDTO educationDTO) {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service create or update Education by ID : {}", educationDTO.getId());

        Education education = educationMapper.educationDTOToEducation(educationDTO);

        boolean isUpdate = educationDTO.getId() != null;
        String oldAudit=null;
        if (isUpdate) {

            Optional<Education> existingEducationOpt = educationRepository.findById(educationDTO.getId());
            if (existingEducationOpt.isPresent()) {
                Education existingEducation = existingEducationOpt.get();
                education.setId(existingEducation.getId());
                Map<String,String> oldparams=prepareEducationForAudit(existingEducationOpt.get());
                oldAudit=existingEducation.getAudit(oldparams,beneficiary.getPerson().getFirstName()+" - "+beneficiary.getPerson().getLastName());
            }
        }



        education.setBeneficiary(beneficiary);
        Map<String,String> newparams=prepareEducationForAudit(education);
        String newAudit=education.getAudit(newparams,beneficiary.getPerson().getFirstName()+" - "+beneficiary.getPerson().getLastName());
        if(educationDTO.getId()!=null){
            auditApplicationService.audit("Modification d'un Education pour  Beneficiare : " + beneficiary.getCode(), getUsernameFromJwt(), "Add Family Member",
                    oldAudit,newAudit, BENEFICIAIRE, UPDATE);
        }else{
            auditApplicationService.audit("Ajout d'un Education pour  Beneficiare : " + beneficiary.getCode(), getUsernameFromJwt(), "Add Family Member",
                    null,newAudit, BENEFICIAIRE, CREATE);
        }
        education.setSemestre(educationDTO.getSemestre());
        educationRepository.save(education);

        log.debug("End service create or update Education by ID : {}, took {}", educationDTO.getId(), watch.toMS());
        return education;
    }

    private Map<String,String> prepareEducationForAudit(Education education){
        Map<String,String> educationMap = new HashMap<>();
        System.out.println(education);
        if(education.getSchoolLevelId() != null){
            SchoolLevelDTO schoolLevelDTO=refFeignClient.getParSchoolLevel(education.getSchoolLevelId());
            educationMap.put("schoolLevel",schoolLevelDTO.getName());
        }
        else{
            educationMap.put("schoolLevel",null);
        }
        if(education.getSchoolYearId() != null){
            SchoolYearDTO schoolYearDTO=refFeignClient.getParSchoolYear(education.getSchoolYearId());
            educationMap.put("schoolYear",schoolYearDTO.getName());
        }
        else{
            educationMap.put("schoolYear",null);
        }
        if(education.getEducationSystemTypeId() != null){
            EducationSystemTypeDTO educationSystemTypeDTO=refFeignClient.getConsEducationSystemType(education.getEducationSystemTypeId());
            educationMap.put("educationSystem",educationSystemTypeDTO.getName());
        }
        else{
            educationMap.put("educationSystem",null);
        }
        if(education.getCityId() != null){

            CityWithRegionAndCountryDTO cityDTO= refController.getCityWithRegionAndCountry(education.getCityId()).getBody();
            System.out.println(cityDTO);
            educationMap.put("pays",cityDTO.getRegion().getCountry().getNameFrench());
            educationMap.put("region",cityDTO.getRegion().getName());
            educationMap.put("ville",cityDTO.getName());
        }
        else{
            educationMap.put("pays",null);
            educationMap.put("region",null);
            educationMap.put("ville",null);
        }
        if(education.getHonorId() != null){
            HonorDTO honorDTO=refFeignClient.getParHonor(education.getHonorId());
            System.out.println(honorDTO);
            educationMap.put("honor",honorDTO.getName());
        }
        else{
            educationMap.put("honor",null);
        }
        return educationMap;
    }


    public List<EducationDTO> getEducationsByBeneficiaryId(Long beneficiaryId) {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service get Education");
        Beneficiary beneficiary = beneficiaryRepository.findById(beneficiaryId)
                .orElseThrow(() -> new ResourceNotFoundException("Beneficiary not found with id: " + beneficiaryId));

        List<Education> educations = educationRepository.findByBeneficiary(beneficiary);
        List<EducationDTO> educationDTOS = new ArrayList<>();

        for (Education education : educations) {

            CityDTO cityDTO = new CityDTO();
            CityWithRegionAndCountryDTO cityWithRegionAndCountryDTO = new CityWithRegionAndCountryDTO();
            if(education.getCityId() != null){
                cityDTO = refFeignClient.getParCity(education.getCityId());
                cityWithRegionAndCountryDTO = refController.getCityWithRegionAndCountry(cityDTO.getId()).getBody();
            }

            SchoolYearDTO schoolYearDTO = refFeignClient.getParSchoolYear(education.getSchoolYearId());
            SchoolLevelDTO schoolLevelDTO = refFeignClient.getParSchoolLevel(education.getSchoolLevelId());
             HonorDTO honorDTO = refFeignClient.getParHonor(education.getHonorId());
            MajorDTO majorDTO = new MajorDTO();
            if(education.getMajorId() != null){
                majorDTO = refFeignClient.getParMajor(education.getMajorId());
            }
            EducationDTO educationDTO = educationMapper.educationToEducationDTO(education);

            if (cityDTO != null) {
                educationDTO.setCity(cityDTO);
            }
            educationDTO.setInfo(cityWithRegionAndCountryDTO);
            educationDTO.setSchoolYear(schoolYearDTO);
            educationDTO.setSchoolLevel(schoolLevelDTO);
            educationDTO.setHonor(honorDTO);
            educationDTO.setSemestre(education.getSemestre());
            if (majorDTO != null) {
                educationDTO.setMajor(majorDTO);
            }
            educationDTOS.add(educationDTO);
        }
        log.debug("End service get Education, took {}", watch.toMS());
        return educationDTOS;
    }

}
