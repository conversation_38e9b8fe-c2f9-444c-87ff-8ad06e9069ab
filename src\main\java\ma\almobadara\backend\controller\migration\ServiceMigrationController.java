package ma.almobadara.backend.controller.migration;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.exceptions.FunctionalException;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.service.migration.DonateurMigration;
import ma.almobadara.backend.service.migration.ServiceMigration;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
@RestController
@RequestMapping("/service-migration")
@Slf4j
@RequiredArgsConstructor
public class ServiceMigrationController {

    private final ServiceMigration serviceMigration;

    @PostMapping("/upload")
    public ResponseEntity<String> migrateDonorData(@RequestParam("file") MultipartFile file) throws FunctionalException {
        log.info("Received request to migrate donor data");
        try {
            serviceMigration.migrationService(file);
            return ResponseEntity.ok("Donor data migration completed successfully.");
        } catch (IOException e) {
            log.error("Error during donor migration: {}", e.getMessage());
            return ResponseEntity.internalServerError().body("Error during donor migration: " + e.getMessage());
        }
    }
}
