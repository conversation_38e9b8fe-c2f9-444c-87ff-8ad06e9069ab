package ma.almobadara.backend.mapper.operationTakenInCharge;

import ma.almobadara.backend.dto.takenInCharge.operationTakenInCharge.DonorOperationDTO;
import ma.almobadara.backend.model.donor.Donor;
import ma.almobadara.backend.model.donor.DonorMoral;
import ma.almobadara.backend.model.donor.DonorPhysical;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface DonorOperationMapper {

    @Mapping(target = "firstName", expression = "java(getFirstName(donor))")
    @Mapping(target = "lastName", expression = "java(getLastName(donor))")
    @Mapping(target = "firstNameAr", expression = "java(getFirstNameAr(donor))")
    @Mapping(target = "lastNameAr", expression = "java(getLastNameAr(donor))")
    @Mapping(target = "type", expression = "java(getType(donor))")
    DonorOperationDTO donorToDonorDTO(Donor donor);


    default String getFirstName(Donor donor) {
        if (donor instanceof DonorPhysical) {
            return ((DonorPhysical) donor).getFirstName();
        } else if (donor instanceof DonorMoral && !((DonorMoral) donor).getDonorContacts().isEmpty()) {
            return ((DonorMoral) donor).getDonorContacts().get(0).getFirstName();
        }
        return "";
    }

    default String getLastName(Donor donor) {
        if (donor instanceof DonorPhysical) {
            return ((DonorPhysical) donor).getLastName();
        } else if (donor instanceof DonorMoral && !((DonorMoral) donor).getDonorContacts().isEmpty()) {
            return ((DonorMoral) donor).getDonorContacts().get(0).getLastName();
        }
        return "";
    }

    default String getFirstNameAr(Donor donor) {
        if (donor instanceof DonorPhysical) {
            return ((DonorPhysical) donor).getFirstNameAr();
        } else if (donor instanceof DonorMoral && !((DonorMoral) donor).getDonorContacts().isEmpty()) {
            return ((DonorMoral) donor).getDonorContacts().get(0).getFirstNameAr();
        }
        return "";
    }

    default String getLastNameAr(Donor donor) {
        if (donor instanceof DonorPhysical) {
            return ((DonorPhysical) donor).getLastNameAr();
        } else if (donor instanceof DonorMoral && !((DonorMoral) donor).getDonorContacts().isEmpty()) {
            return ((DonorMoral) donor).getDonorContacts().get(0).getLastNameAr();
        }
        return "";
    }

    default String getType(Donor donor) {
        if (donor instanceof DonorPhysical) {
            return ((DonorPhysical) donor).getType();
        } else if (donor instanceof DonorMoral) {
            return ((DonorMoral) donor).getType();
        }
        return "";
    }
}
